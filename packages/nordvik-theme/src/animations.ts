import { KeyValuePair, ResolvableTo } from 'tailwindcss/types/config'

export const keyframes = {
  'accordion-down': {
    from: { height: '0px' },
    to: { height: 'var(--radix-accordion-content-height)' },
  },
  'accordion-up': {
    from: { height: 'var(--radix-accordion-content-height)' },
    to: { height: '0px' },
  },
  'glow-movement': {
    from: {
      offsetDistance: '0%',
    },
    to: {
      offsetDistance: '100%',
    },
  },
  collapsibleSlideDown: {
    from: {
      height: '0px',
    },
    to: {
      height: 'var(--radix-collapsible-content-height)',
    },
  },

  'caret-blink': {
    '0%,70%,100%': { opacity: '1' },
    '20%,50%': { opacity: '0' },
  },

  collapsibleSlideUp: {
    from: {
      height: 'var(--radix-collapsible-content-height)',
    },
    to: {
      height: '0px',
    },
  },

  notification: {
    '0%': {
      transform: 'translateY(0)',
    },
    '25%': {
      transform: 'translateY(-4px)',
    },
    '50%': {
      transform: 'translateY(0px)',
    },
    '75%': {
      transform: 'translateY(-3px)',
    },
    '100%': {
      transform: 'translateY(0px)',
    },
  },
} satisfies ResolvableTo<
  KeyValuePair<string, KeyValuePair<string, KeyValuePair<string, string>>>
>

export const animation = {
  'accordion-down': 'accordion-down 0.2s ease-out',
  'accordion-up': 'accordion-up 0.2s ease-out',
  'collapsible-slide-down': 'collapsibleSlideDown 0.2s ease-out',
  'collapsible-slide-up': 'collapsibleSlideUp 0.2s ease-out',
  'caret-blink': 'caret-blink 1.2s ease-out infinite',
  notification: 'notification 1s ease-in-out forwards',
  'glow-movement': 'glow-movement 5s linear infinite',
} satisfies ResolvableTo<KeyValuePair<string, string>>
