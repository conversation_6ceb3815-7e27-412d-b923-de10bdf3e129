import React from 'react'

import { Button } from '../components/button'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetOverlay,
  SheetTitle,
  SheetTrigger,
} from '../components/sheet'

export default {
  title: 'Navigation / Sheet',
  parameters: {
    theme: 'dark',
  },
}

export function SheetStory() {
  const [opened, setOpened] = React.useState<'dark' | 'light' | null>('dark')
  return (
    <div className="flex flex-col gap-4">
      <Sheet
        open={opened === 'dark'}
        onOpenChange={(o) => {
          setOpened(o ? 'dark' : null)
        }}
      >
        <SheetTrigger>
          <Button size="lg">Open dark sheet</Button>
        </SheetTrigger>
        <SheetOverlay />
        <SheetContent data-theme="dark">
          <SheetHeader>
            <SheetTitle>Sheet title</SheetTitle>
            <SheetClose />
          </SheetHeader>
          <SheetDescription>Sheet description</SheetDescription>
          Other content
          <SheetFooter>
            <Button size="lg">Cancel</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
      <Sheet
        open={opened === 'light'}
        onOpenChange={(o) => {
          setOpened(o ? 'light' : null)
        }}
      >
        <SheetTrigger>
          <Button size="lg">Open default sheet</Button>
        </SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Sheet title</SheetTitle>
            <SheetClose />
          </SheetHeader>
          <SheetDescription>Sheet description</SheetDescription>
          Other content
          <SheetFooter>
            <Button size="lg">Cancel</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  )
}
SheetStory.storyName = 'Sheet'
