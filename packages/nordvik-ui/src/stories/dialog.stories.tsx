import { InfoIcon, Trash2Icon } from 'lucide-react'
import React from 'react'

import { Alert, AlertDescription, AlertTitle } from '../components/alert'
import { But<PERSON> } from '../components/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogTrigger,
} from '../components/dialog'
import {
  DialogCloseButton,
  DialogContainer,
  DialogDescription,
  DialogHeader,
} from '../components/dialog-blocks'
import { Input } from '../components/input'

import { StoryPartHeading } from './story-blocks'

export default {
  title: 'Feedback / Dialog',
}

export function Examples() {
  return (
    <div className="flex flex-col gap-20 py-20 items-center">
      <DialogContainer>
        <DialogCloseButton>Lukk</DialogCloseButton>
        <DialogHeader title="Send til kunde" />
        <DialogDescription>
          Kunden vil få en e-post med lenke til oppdragsavtalen.
        </DialogDescription>
        <DialogFooter>
          <Button variant="ghost" size="md">
            Avbryt
          </Button>
          <Button variant="default" size="md">
            Send til kunde
          </Button>
        </DialogFooter>
      </DialogContainer>

      <DialogContainer>
        <DialogCloseButton>Lukk</DialogCloseButton>
        <DialogHeader title="Pris per time" />
        <DialogDescription>
          Oppdragsgiver kan velge mellom timebasert vederlag (punkt 4.1) eller
          provisjonsbasert vederlag (punkt 4.2). Uansett hvilket av
          alternativene som avtales, påløper kostnader i forbindelse med utlegg
          og andre utgifter.
        </DialogDescription>
      </DialogContainer>

      <DialogContainer>
        <DialogCloseButton>Lukk</DialogCloseButton>
        <DialogHeader
          title="Notater"
          subtitle="Kun synlig for meglere på oppdraget"
        />
        <DialogDescription className="max-h-[200px] space-y-2">
          <p>
            Kunde har vist sterk interesse for eiendommen og ønsker å komme på
            visning neste uke. De har tidligere kjøpt lignende eiendommer i
            området og ser ut til å være seriøse kjøpere med god finansiering på
            plass.
          </p>
          <p>
            Eiendommen trenger noen mindre oppussinger før salg, spesielt i
            kjøkkenet og badet. Anbefaler å få takstmann til å vurdere om det er
            verdt å investere i oppussing før visning, eller om vi bør selge
            som-stand. Kunden nevnte at de er villige til å ta på seg noe
            oppussing selv.
          </p>
          <p>
            Konkurransen i området er moderat, med 3-4 lignende eiendommer til
            salgs. Vår pris er konkurransedyktig, men vi bør være forberedt på
            forhandling. Kunden nevnte at de har sett på andre alternativer og
            at prisen er viktig, men ikke alt.
          </p>
          <p>
            Anbefaler å fokusere på eiendommens unike kvaliteter under visning:
            den store terrassen, god standard på installasjoner, og rolig
            beliggenhet. Dette skiller oss fra konkurrentene. Kunden er spesielt
            interessert i uteområdet siden de har små barn.
          </p>
          <p>
            Følg opp med kunden innen 24 timer etter visning for å høre deres
            første inntrykk. Hvis de er positive, bør vi be om et konkret tilbud
            innen en uke. Husk å fremheve at det er andre interessenter som også
            har booket visning.
          </p>
        </DialogDescription>
        <DialogFooter
          secondary={
            <Button variant="ghost" iconOnly={<Trash2Icon />} size="md">
              Slett
            </Button>
          }
        >
          <Button variant="ghost" size="md">
            Avbryt
          </Button>
          <Button variant="default" size="md">
            Lagre notat
          </Button>
        </DialogFooter>
      </DialogContainer>
    </div>
  )
}

export function Sizes() {
  return (
    <>
      <StoryPartHeading>Small</StoryPartHeading>
      <div className="flex flex-col py-10 items-center">
        <DialogContainer size="sm">
          <DialogCloseButton>Lukk</DialogCloseButton>
          <DialogHeader title="Send til kunde" />
          <DialogDescription>
            Kunden vil få en e-post med lenke til oppdragsavtalen.
          </DialogDescription>
          <DialogFooter>
            <Button variant="ghost" size="sm">
              Avbryt
            </Button>
            <Button variant="default" size="sm">
              Send til kunde
            </Button>
          </DialogFooter>
        </DialogContainer>
      </div>
      <StoryPartHeading>Medium</StoryPartHeading>
      <div className="flex flex-col py-10 items-center">
        <DialogContainer size="md">
          <DialogCloseButton>Lukk</DialogCloseButton>
          <DialogHeader title="Optimalisere interiør" />
          <DialogDescription className="space-y-2">
            <p>
              God styling selger. Boliger som er optimalt innredet selges etter
              vår erfaring raskere og til en høyere pris.
            </p>
            <p>
              Våre kunder er ofte litt mer opptatt av bolig enn andre, og
              boligene deres er gjerne litt finere enn gjennomsnittet. Likevel
              ser vi på prisene vi oppnår, at en stylist blir en lønnsom
              investering rett og slett fordi bildene i markedsføringen blir
              vakrere og stemningen på visningen enda bedre.
            </p>
            <p>
              Det handler rett og slett om å få et profesjonelt blikk utenfra
              som sammen med megler vet hva som selger.
            </p>
            <p>
              Når det kommer til å få profesjonell hjelp til å optimalisere
              interiøret har du to valg:
            </p>
            <ul>
              <li>
                Befaring med interiørveileder, som gir deg råd om hva du kan
                gjøre selv for å optimalisere boligen for fotografering og
                visning.
              </li>
              <li>
                Del- eller helstyling av boligen utført av profesjonell stylist.
                Inkluderer som regel også en befaring, hvor du får råd om hva du
                bør gjøre før stylisten kommer.
              </li>
            </ul>
          </DialogDescription>
        </DialogContainer>
      </div>
      <StoryPartHeading>Large</StoryPartHeading>
      <div className="flex flex-col py-10 items-center">
        <DialogContainer size="lg">
          <DialogCloseButton>Lukk</DialogCloseButton>
          <DialogHeader
            title="Kontaktinformasjon"
            subtitle="Informasjonen om kunden blir oppdatert i Next"
          />
          <DialogDescription divider>
            <form className="grid grid-cols-2 gap-x-4 gap-y-4 max-md:grid-cols-1">
              <Input label="Fornavn" placeholder="Ola" />
              <Input label="Etternavn" placeholder="Nordmann" />
              <Input label="E-post" placeholder="<EMAIL>" />
              <Input label="Telefon" placeholder="99999999" />
            </form>
          </DialogDescription>
          <DialogFooter>
            <Button variant="ghost" size="md">
              Avbryt
            </Button>
            <Button variant="default" size="md">
              Lagre
            </Button>
          </DialogFooter>
        </DialogContainer>
      </div>
    </>
  )
}

const background = (
  <div className="max-w-screen-md mx-auto px-8 my-20 typo-body-md [&_p+p]:mt-4">
    <h1 className="typo-display-xl">Example page to display a dialog</h1>
    <p className="typo-body-xl text-pretty mt-2">
      The reason why we display this content is to let us see how the dialog
      performs above it.
    </p>
    <h2 className="typo-title-xs mt-4 mb-2">Some things to look out for</h2>
    <ul className="list-disc mt-2">
      <li>Does it scroll?</li>
      <li>Does it cover the content?</li>
      <li>Does it cover the background?</li>
      <li>Is the contrast between the dialog and the background good?</li>
    </ul>
    <div className="border border-subtle rounded-sm p-4 -mx-4 my-8 flex items-center gap-2 justify-between bg-root-muted">
      <span className="typo-body-md-bold">Ready to open the dialog?</span>
      <DialogTrigger asChild>
        <Button variant="default" size="sm">
          Open dialog
        </Button>
      </DialogTrigger>
    </div>
    <h2 className="typo-title-xs mt-4 mb-2">
      To make this page longer, I'll let AI tell a story about a cat.{' '}
    </h2>
    <p>
      In a quiet village nestled between rolling hills and ancient forests,
      there lived a cat named Mistral. She was no ordinary feline. Her fur was
      the color of storm clouds, with faint streaks of silver that shimmered
      under the moonlight. Mistral belonged to no one, yet everyone in the
      village claimed her as their own. Children left saucers of milk by the
      windowsills, shopkeepers paused their work to scratch her ears, and old
      women swore she brought them luck when she crossed their path.
    </p>

    <p>
      Each morning, Mistral strolled through the cobbled streets like a queen
      surveying her domain. She greeted the baker first, for he always shared a
      corner of his still-warm bread. Then she'd curl up by the flower stall and
      nap amidst the scent of peonies and lavender. But by afternoon, she
      vanished. No one ever knew where she went, though many tried to follow.
      She would slip into an alley and be gone by the time a curious child
      peeked around the corner.
    </p>

    <p>
      Legends grew around her disappearances. Some said she turned into mist and
      drifted through the forest. Others believed she had a secret tunnel that
      led beneath the hills to an underground kingdom of cats. But the truth was
      simpler and stranger. Mistral had discovered an old, forgotten cottage
      deep in the woods, hidden by brambles and ivy. There, in a sun-dappled
      clearing, she lived a second life as guardian of something very old and
      very powerful.
    </p>

    <p>
      Inside the cottage was a mirror. Tall, cracked, and ringed with runes no
      one in the village could read. When Mistral stood before it, the glass
      shimmered like water. She would press her paw gently to its surface and
      step through into another world — a world of flickering stars and floating
      islands, where cats walked on two legs and spoke in ancient tongues.
      There, she was not a village stray but a high priestess of the Mooncat
      Court.
    </p>

    <p>
      In that realm, her name was Myr'Tahl, and she held the memory of all cats
      that had ever lived. The other mooncats gathered in a circle each time she
      arrived, tails flicking, eyes gleaming. They brought news from distant
      stars and debated the fate of dreams and shadows. Myr'Tahl presided with
      grace, offering wisdom and warnings when necessary. Her greatest task,
      however, was watching over the boundary between the worlds — to keep the
      veil intact, so magic would not spill where it was not welcome.
    </p>

    <p>
      Back in the village, the seasons turned. Leaves burned gold and red in
      autumn, snow blanketed the streets in winter, and still Mistral walked her
      path. Children grew into adults, and new children took their place, all
      loving the mysterious gray cat who never seemed to age. Only the oldest
      villagers remembered the stories, whispering them like bedtime fairy
      tales. They said Mistral had a secret, that she guarded something
      invisible yet important. Most brushed it off with a smile.
    </p>

    <p>
      One night, a strange wind swept through the village. Lights flickered, and
      a low hum buzzed in the air. Mistral stood atop the old stone wall, eyes
      fixed on the darkened forest. She leapt down without a sound and vanished
      into the trees. That night, the stars pulsed a little brighter, and dreams
      across the village were filled with silver cats and speaking mirrors. In
      the morning, Mistral returned, her fur damp and her eyes distant, as if
      she'd looked upon something vast and ancient.
    </p>

    <p>
      To this day, she still walks the village — a little slower, perhaps, but
      no less regal. The children still leave her milk, and the baker still
      breaks his bread for her. But every now and then, if you look closely,
      you'll see her pause beside a shadow or a beam of moonlight, tilting her
      head as if listening to voices only she can hear. And if you're very
      quiet, and the stars are just right, you might hear the faint whisper of a
      realm beyond mirrors, where the mooncats sing her name.
    </p>
  </div>
)

export const WithBackgroundContent = ({
  theme,
}: {
  theme: 'light' | 'dark'
}) => (
  <Dialog defaultOpen={process.env.STORYBOOK_CI === 'true'}>
    {background}
    <DialogContent size="md" title="Send til kunde" data-theme={theme}>
      <DialogDescription className="max-h-[20rem]">
        <div className="space-y-6">
          <p>
            Kunden vil få en e-post med lenke til oppdragsavtalen. Når du sender
            oppdragsavtalen til kunden, kan du forvente følgende:
          </p>

          <div className="space-y-3">
            <h4 className="typo-title-xs">Hva skjer når du sender</h4>
            <ul className="list-disc space-y-1 pl-4">
              <li>
                Kunden mottar en sikker e-post med direkte lenke til
                oppdragsavtalen
              </li>
              <li>De kan lese gjennom alle vilkår og betingelser digitalt</li>
              <li>
                Kunden kan signere dokumentet elektronisk med BankID eller andre
                sikre metoder
              </li>
              <li>Du får varsling når kunden har åpnet og signert avtalen</li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="typo-title-xs">Hva du bør gjøre</h4>
            <ul className="list-disc space-y-1 pl-4">
              <li>
                Følg opp med kunden innen 24 timer hvis de ikke har åpnet
                e-posten
              </li>
              <li>Vær tilgjengelig for spørsmål om avtalevilkårene</li>
              <li>Klarer du opp eventuelle uklarheter før signering</li>
              <li>Bekreft at kunden har forstått alle betingelser</li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="typo-title-xs">Vanlige spørsmål fra kunder</h4>
            <ul className="list-disc space-y-1 pl-4">
              <li>Hvor lenge er avtalen gyldig?</li>
              <li>Kan vi endre vilkår etter signering?</li>
              <li>Hva skjer hvis eiendommen ikke selges?</li>
              <li>Hvordan fungerer provisjonsberegningen?</li>
            </ul>
          </div>

          <Alert className="mt-6" Icon={InfoIcon}>
            <AlertTitle>Før du sender</AlertTitle>
            <AlertDescription>
              Det er lurt å ha en kort samtale med kunden før du sender avtalen,
              så de vet hva de kan forvente og når de skal sjekke e-posten sin.
            </AlertDescription>
          </Alert>
        </div>
      </DialogDescription>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="ghost" size="md">
            Avbryt
          </Button>
        </DialogClose>
        <Button variant="default" size="md">
          Send til kunde
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
)

WithBackgroundContent.parameters = {
  theme: 'light',
}

export const WithDarkBackgroundContent = () => (
  <WithBackgroundContent theme="dark" />
)

WithDarkBackgroundContent.parameters = {
  theme: 'dark',
}
