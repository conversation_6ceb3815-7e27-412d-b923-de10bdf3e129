import { X } from 'lucide-react'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

import { Button } from './button'

export type InputProps = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  'size'
> & {
  addonLeft?: React.ReactNode | string
  addonRight?: React.ReactNode | string
  wrapperClassName?: string
  onClearInput?: () => void
  error?: boolean | null
  errorMessage?: string | null
  variant?: 'outline' | 'fill'
  size?: 'sm' | 'md'
  label?: React.ReactNode
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant = 'outline',
      className,
      label,
      wrapperClassName,
      type,
      addonLeft,
      addonRight,
      onClearInput,
      error,
      errorMessage,
      size = 'md',
      ...props
    },
    ref,
  ) => {
    const input = (
      <>
        <div
          data-error={<PERSON><PERSON>an(error || errorMessage)}
          className={cn(
            'group relative flex  w-full items-center overflow-hidden rounded-lg ring-0 focus-within:ring-2 focus-within:ring-inputs-border-active focus-within:ring-offset-0 ring-inset has-[:disabled]:opacity-50',
            'data-[error=true]:ring-danger-emphasis data-[error=true]:ring-1',
            {
              outline: 'ring-inputs-border ring-1',
              fill: 'bg-inputs-fill hover:bg-inputs-fill-hover',
            }[variant],
            {
              sm: 'min-h-9 typo-body-sm',
              md: 'min-h-11 typo-body-md',
            }[size],
            wrapperClassName,
          )}
        >
          {addonLeft ? <InputAddon size={size}>{addonLeft}</InputAddon> : null}
          <input
            className={cn(
              'flex size-full bg-transparent px-3 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:ink-muted focus-visible:outline-none disabled:cursor-not-allowed ',
              'not-last:pr-0 not-first:pl-0',
              className,
            )}
            ref={ref}
            type={type}
            {...props}
          />
          {onClearInput && props.value ? (
            <OnClearAddon onClearInput={onClearInput} />
          ) : null}
          {addonRight ? (
            <InputAddon size={size} className="pr-3">
              {addonRight}
            </InputAddon>
          ) : null}
        </div>
        {errorMessage ? (
          <div className="typo-body-sm ink-danger mt-1.5">{errorMessage}</div>
        ) : null}
      </>
    )

    if (label)
      return (
        <label>
          <div className="typo-label-md mb-1.5">{label}</div>
          {input}
        </label>
      )

    // Has to be wrapped in something, else the errorMessage might be spaced by accidental space-y classes.
    return <div>{input}</div>
  },
)
Input.displayName = 'Input'

function OnClearAddon({ onClearInput }: { onClearInput: () => void }) {
  return (
    <InputAddon className="min-w-5">
      <Button
        variant="unstyled"
        size="sm"
        iconOnly={<X />}
        onClick={onClearInput}
        className="text-[inherit] px-2 not-last:w-auto"
      >
        Tøm
      </Button>
    </InputAddon>
  )
}

function InputAddon({
  children,
  className,
  size = 'md',
}: {
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md'
}) {
  return (
    <div
      className={cn(
        'flex-shrink-0 flex h-full group-data-[error=true]:ink-danger items-center justify-center ink-muted not-last:group-focus-within:ink-default',
        {
          sm: '[&>svg]:size-3.5 min-w-8',
          md: '[&>svg]:size-4 min-w-10',
        }[size],
        className,
      )}
    >
      {children}
    </div>
  )
}
