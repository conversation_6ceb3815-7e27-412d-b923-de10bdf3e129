import React from 'react'

import { cn } from '@nordvik/theme/cn'

export type TextareaProps =
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    wrapperClassName?: string
    error?: boolean | null
    errorMessage?: string | null
    variant?: 'outline' | 'fill'
    label?: React.ReactNode
  }

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      wrapperClassName,
      error,
      errorMessage,
      variant = 'outline',
      label,
      ...props
    },
    ref,
  ) => {
    const textarea = (
      <>
        <div
          data-error={Boolean(error || errorMessage)}
          className={cn(
            'group relative flex min-h-10 w-full items-center bg-transparent overflow-hidden rounded-lg  ring-inputs-border-hover ring-0 focus-within:ring-2 focus-within:ring-inputs-border-active focus-within:ring-offset-0 ring-inset has-[:disabled]:opacity-50',
            'data-[error=true]:ring-danger-emphasis',
            {
              outline: 'ring-inputs-border ring-1',
              fill: 'bg-inputs-fill hover:bg-inputs-fill-hover',
            }[variant],
            wrapperClassName,
          )}
        >
          <textarea
            className={cn(
              'typo-body-md flex size-full min-h-10 bg-transparent  px-3 py-2 file:border-0 fiscrollbar-thinle:bg-transparent file:text-sm file:font-medium placeholder:ink-muted focus-visible:outline-none disabled:cursor-not-allowed [scrollbar-width:thin]',
              'not-last:pr-0 not-first:pl-0',
              '[field-sizing:content] md:[resize:none;] ',
              className,
            )}
            ref={ref}
            {...props}
          />
        </div>
        {errorMessage ? (
          <span className="typo-body-sm ink-danger">{errorMessage}</span>
        ) : null}
      </>
    )

    if (label)
      return (
        <label>
          <div className="typo-label-md mb-1.5">{label}</div>
          {textarea}
        </label>
      )

    // Has to be wrapped in something, else the errorMessage might be spaced by accidental space-y classes.
    return <div>{textarea}</div>
  },
)
Textarea.displayName = 'Textarea'
