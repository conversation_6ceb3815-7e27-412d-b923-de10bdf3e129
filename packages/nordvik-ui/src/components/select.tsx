'use client'

import * as SelectPrimitive from '@radix-ui/react-select'
import { cva } from 'class-variance-authority'
import { Check, ChevronDown } from 'lucide-react'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

export const Select = SelectPrimitive.Root

export const SelectGroup = SelectPrimitive.Group
export const SelectPortal = SelectPrimitive.Portal

export const SelectValue = SelectPrimitive.Value

export const selectTriggerVariants = cva(
  cn(
    'ink-default placeholder:ink-subtle',
    'flex grow shrink-0 items-center rounded-lg px-3 border-2 border-transparent data-[state=error]:border-stroke-danger-emphasis [&>span]:line-clamp-1',
    // Disabled
    'disabled:cursor-not-allowed  focus:outline-none disabled:border-inputs-border-disabled disabled:bg-inputs-fill-disabled',
    // Focus
    'focus-visible:ring-2 ring-offset-background-root ring-inputs-border-active ring-offset-2',
  ),
  {
    variants: {
      variant: {
        outline: 'border border-inputs-border hover:border-inputs-border-hover',
        fill: 'bg-inputs-fill hover:bg-inputs-fill-hover focus-visible:bg-inputs-fill-active',
      },
      size: {
        sm: 'min-h-9 typo-body-sm gap-1',
        md: 'min-h-11 typo-body-md  gap-1',
        // lg: 'h-12',
      },
    },
  },
)

export const Trigger = SelectPrimitive.Trigger

export const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    error?: string
    label?: string
    variant?: 'outline' | 'fill'
    size?: 'sm' | 'md'
  }
>(
  (
    { className, label, variant = 'outline', size = 'md', children, ...props },
    ref,
  ) => {
    const triggerComponent = (
      <SelectPrimitive.Trigger
        className={cn(selectTriggerVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      >
        {children}
        <SelectPrimitive.Icon asChild>
          <ChevronDown className="ml-auto size-[1.25em] mt-[0.1em] ink-subtle" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
    )

    if (label) {
      return (
        <div className="flex flex-col gap-1.5">
          <span className="typo-label-md">{label}</span>
          {triggerComponent}
        </div>
      )
    }
    return triggerComponent
  },
)
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

export const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Content
    className={cn(
      'relative max-h-96 min-w-[8rem] overflow-y-auto rounded-md bg-interactive-top ink-default shadow-sm',
      position === 'popper' &&
        'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
      className,
    )}
    position={position}
    ref={ref}
    {...props}
  >
    <SelectPrimitive.Viewport
      className={cn(
        'p-2',
        position === 'popper' &&
          'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
      )}
    >
      {children}
    </SelectPrimitive.Viewport>
  </SelectPrimitive.Content>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

export const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    className={cn('typo-label-md py-1.5 pl-8 pr-2', className)}
    ref={ref}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

export const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    className={cn(
      'typo-body-sm flex w-full cursor-pointer select-none items-center gap-2 rounded-sm p-1.5 text-sm outline-none focus:bg-interactive-muted focus:ink-default data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    ref={ref}
    {...props}
  >
    <span className="left-2 flex size-4 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="size-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

export const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    className={cn('-mx-1 my-1 h-px bg-stroke-muted', className)}
    ref={ref}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName
