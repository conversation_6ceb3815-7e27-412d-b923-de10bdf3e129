'use client'

import { formatDate as formatFns } from 'date-fns'
import { endOfMonth } from 'date-fns/endOfMonth'
import { isSameDay } from 'date-fns/isSameDay'
import { startOfMonth } from 'date-fns/startOfMonth'
import { Calendar as CalendarIcon } from 'lucide-react'
import * as React from 'react'
import type { DateRange } from 'react-day-picker'

import { cn } from '@nordvik/theme/cn'

import { Button, createButtonClassName } from './button'
import { Calendar } from './calendar'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { selectTriggerVariants } from './select'

export type { DateRange } from 'react-day-picker'

type CommonProps = React.HTMLAttributes<HTMLDivElement> & {
  presets?: { value: DateRange; label: string }[]
  onDateChange?: (date: DateRange | undefined) => void
  onSubmit: (date: DateRange | undefined) => void
  formatDate?: typeof formatFns
} & { range?: DateRange }

export function DatePickerWithRangeAndPresets(
  props: CommonProps,
): React.ReactElement

export function DatePickerWithRangeAndPresets({
  className,
  presets,
  onDateChange,
  onSubmit,
  range,
  formatDate = formatFns,
}: CommonProps & {
  range?: DateRange
}): React.ReactElement {
  const today = new Date()

  const [date, setDate] = React.useState<DateRange | undefined>(() => {
    if (range?.from && range.to) {
      return range
    }

    return {
      from: startOfMonth(today),
      to: endOfMonth(today),
    }
  })

  const onDateChangeHandler = React.useCallback(
    (inputDate: DateRange | undefined) => {
      setDate(inputDate)
      onDateChange?.(inputDate)
    },
    [onDateChange],
  )

  const [isOpen, setIsOpen] = React.useState(false)

  const mainLabel = React.useMemo(() => {
    if (date?.from) {
      if (date.to && !isSameDay(date.from, date.to)) {
        return `${formatDate(date.from, 'dd. MMM')} - ${formatDate(date.to, 'dd. MMM')}`
      }

      return formatDate(date.from, 'dd. MMMM')
    }

    return 'Velg dato'
  }, [date?.from, date?.to, formatDate])

  const presetDefault = React.useMemo(
    () => getCurrentPreset(presets, date),
    [date, presets],
  )

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover onOpenChange={setIsOpen} open={isOpen}>
        <PopoverTrigger
          className={cn(
            'min-w-max',
            selectTriggerVariants({ variant: 'fill' }),
          )}
        >
          <CalendarIcon className="mr-2 size-4" />
          {mainLabel}
        </PopoverTrigger>
        <PopoverContent align="start" className="w-auto">
          <div className="flex gap-6 space-y-2">
            {presets ? (
              <div className="flex flex-col gap-2">
                {presets.map((preset) => (
                  <Button
                    className={cn(
                      createButtonClassName({
                        variant:
                          presetDefault === preset.label
                            ? 'default'
                            : 'outline',
                        size: 'sm',
                      }),
                      'w-fit',
                    )}
                    key={preset.label}
                    onClick={() => {
                      onDateChangeHandler(preset.value)
                    }}
                    value={preset.label}
                    size="sm"
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            ) : null}
            <div
              className={presets ? 'rounded-md' : undefined}
              data-testid="date-picker-content"
            >
              <Calendar
                defaultMonth={date?.from}
                initialFocus
                mode="range"
                disabled={{ after: today }}
                numberOfMonths={2}
                onSelect={onDateChangeHandler}
                selected={date}
              />
            </div>
          </div>
          <div className="mt-2 flex justify-end border-t border-muted pt-2">
            <Button
              className={cn(
                createButtonClassName({ variant: 'default', size: 'sm' }),
              )}
              onClick={() => {
                onSubmit(date)
                setIsOpen(false)
              }}
              size="sm"
            >
              Vis resultat
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export function getCurrentPreset(
  presets: CommonProps['presets'],
  range?: DateRange,
) {
  if (!presets || presets.length === 0) {
    return undefined
  }
  if (!range?.from || !range.to) {
    return undefined
  }

  return presets.find(
    (p) =>
      isSameDay(p.value.from!, range.from!) &&
      isSameDay(p.value.to!, range.to!),
  )?.label
}
