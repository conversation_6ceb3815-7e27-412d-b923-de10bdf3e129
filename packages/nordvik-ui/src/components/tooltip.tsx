'use client'

import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import * as React from 'react'

import { cn } from '@nordvik/theme/cn'

export const TooltipProvider = TooltipPrimitive.Provider

export const Tooltip = TooltipPrimitive.Root
export const TooltipPortal = TooltipPrimitive.Portal

export const TooltipTrigger = TooltipPrimitive.Trigger

export type TooltipPosition = TooltipPrimitive.TooltipContentProps['side']

export const variants = {
  dark: cn('bg-[black] text-[white] [&_.arrow]:fill-[black]'),
  light: cn('bg-[white] text-[black] [&_.arrow]:fill-[white]'),
  danger: cn('bg-danger-bold ink-on-danger-bold [&_.arrow]:fill-danger-bold'),
  warning: cn('bg-gold-emphasis ink-on-gold [&_.arrow]:fill-gold-emphasis'),
} as const

export const TooltipContent = React.forwardRef<
  React.ComponentRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
    variant?: keyof typeof variants
  }
>(
  (
    { className, sideOffset = 4, children, variant = 'dark', ...props },
    ref,
  ) => (
    <TooltipPrimitive.Content
      className={cn(
        'animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 overflow-hidden rounded-md shadow-md',
        'typo-body-sm px-3 py-2 text-pretty max-w-[320px]',
        variants[variant],
        className,
      )}
      ref={ref}
      sideOffset={sideOffset}
      data-theme="light"
      {...props}
    >
      {children}
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <TooltipPrimitive.Arrow className="arrow" />
    </TooltipPrimitive.Content>
  ),
)
TooltipContent.displayName = TooltipPrimitive.Content.displayName
