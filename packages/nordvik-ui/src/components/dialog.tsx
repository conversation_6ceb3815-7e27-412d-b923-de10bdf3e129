import {
  DialogClose,
  DialogContent as DialogContentPrimitive,
  DialogDescription as DialogDescriptionPrimitive,
  DialogOverlay as DialogOverlayPrimitive,
  DialogPortal,
  DialogTitle,
} from '@radix-ui/react-dialog'

import { cn } from '@nordvik/theme/cn'

import * as DialogBlocks from './dialog-blocks'
import type {
  DialogContainerProps,
  DialogDescriptionProps,
} from './dialog-blocks'

export { DialogFooter } from './dialog-blocks'
export {
  DialogTrigger,
  DialogTitle,
  Dialog,
  DialogClose,
} from '@radix-ui/react-dialog'

export function DialogContentWrapper({
  children,
  'data-theme': theme,
}: {
  children: React.ReactNode
  'data-theme'?: 'light' | 'dark'
}) {
  return (
    <DialogPortal>
      <DialogOverlayPrimitive
        className={cn(
          'w-screen h-full bg-overlay fixed left-0 top-0 grid grid-rows-[1fr_auto_2fr] place-items-center overflow-y-auto p-2 md:p-4',
          'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
        )}
        data-theme={theme}
      >
        {children}
      </DialogOverlayPrimitive>
    </DialogPortal>
  )
}

export type DialogContentProps = Omit<
  React.ComponentProps<typeof DialogContentPrimitive>,
  'title'
> & {
  size?: DialogContainerProps['size']
  title: string | false
  subtitle?: React.ReactNode
  'data-theme'?: 'light' | 'dark'
}

export function DialogContent({
  children,
  size,
  title,
  subtitle,
  'data-theme': theme,
  ...rest
}: DialogContentProps) {
  return (
    <DialogContentWrapper data-theme={theme}>
      <DialogContentPrimitive asChild {...rest}>
        <DialogBlocks.DialogContainer size={size} className="row-start-2">
          {title && (
            <DialogTitle asChild>
              <DialogBlocks.DialogHeader title={title} subtitle={subtitle} />
            </DialogTitle>
          )}
          {children}
          <DialogClose asChild>
            <DialogBlocks.DialogCloseButton>
              Lukk
            </DialogBlocks.DialogCloseButton>
          </DialogClose>
        </DialogBlocks.DialogContainer>
      </DialogContentPrimitive>
    </DialogContentWrapper>
  )
}

export function DialogDescription(props: DialogDescriptionProps) {
  return (
    <DialogDescriptionPrimitive asChild>
      <DialogBlocks.DialogDescription {...props} />
    </DialogDescriptionPrimitive>
  )
}
