'use client'

import * as React from 'react'
import { Drawer as DrawerPrimitive } from 'vaul'

import { cn } from '@nordvik/theme/cn'

export function Drawer({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root>) {
  return (
    <DrawerPrimitive.Root
      shouldScaleBackground={shouldScaleBackground}
      {...props}
    />
  )
}
Drawer.displayName = 'Drawer'

export const DrawerTrigger = DrawerPrimitive.Trigger

export const DrawerPortal = DrawerPrimitive.Portal

export const DrawerClose = DrawerPrimitive.Close

export const DrawerOverlay = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Overlay
    className={cn('fixed inset-0 border-none bg-overlay', className)}
    ref={ref}
    {...props}
  />
))
DrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName

export const DrawerContent = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content> & {
    hideGrabber?: boolean
    overlayClassName?: string
  }
>(
  (
    { className, children, hideGrabber = false, overlayClassName, ...props },
    ref,
  ) => (
    <DrawerPortal>
      <DrawerOverlay className={cn('bg-overlay', overlayClassName)} />
      <DrawerPrimitive.Content
        className={cn(
          'fixed inset-x-0 bottom-0 flex h-auto max-h-[calc(100dvh-2rem)] flex-col rounded-t-[32px] border-none bg-root pt-2 pb-4',
          className,
        )}
        ref={ref}
        {...props}
      >
        {!hideGrabber && (
          <div className="mx-auto mb-2 mt-1 h-1 w-full max-w-[48px] rounded-full bg-stroke-muted" />
        )}
        {children}
      </DrawerPrimitive.Content>
    </DrawerPortal>
  ),
)
DrawerContent.displayName = 'DrawerContent'

export function DrawerHeader({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('mb-2 grid gap-1.5 px-4 pb-2 text-left', className)}
      {...props}
    />
  )
}
DrawerHeader.displayName = 'DrawerHeader'

export function DrawerFooter({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'my-2 flex flex-col border-t border-t-muted p-4 py-3',
        className,
      )}
      {...props}
    />
  )
}
DrawerFooter.displayName = 'DrawerFooter'

export const DrawerTitle = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Title
    className={cn('typo-title-sm', className)}
    ref={ref}
    {...props}
  />
))
DrawerTitle.displayName = DrawerPrimitive.Title.displayName

export const DrawerDescription = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Description
    className={cn('text-sm ink-default', className)}
    ref={ref}
    {...props}
  />
))

DrawerDescription.displayName = DrawerPrimitive.Description.displayName
