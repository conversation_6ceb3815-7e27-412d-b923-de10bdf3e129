{"name": "@nordvik/ui", "version": "0.1.0", "private": true, "exports": {"./*": "./src/components/*.tsx", "./story-blocks": "./src/stories/story-blocks.tsx"}, "scripts": {"build-storybook": "STORYBOOK_CI=true storybook build", "lint": "eslint . --ext .ts,.tsx --max-warnings 0", "format": "prettier --write . --ignore-path ./../../.prettierignore", "check:format": "prettier --check src  --ignore-path ./../../.prettierignore", "check:type": "tsc --noEmit", "storybook": "storybook dev -p 6006 --no-version-updates --no-open --disable-telemetry", "storybook:skip": "npx chromatic --project-token=chpt_180211c64781077 --skip", "storybook:snapshot": "npx chromatic --project-token=chpt_180211c64781077 -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded", "storybook:snapshot:accept": "npx chromatic --project-token=chpt_180211c64781077 -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded --auto-accept-changes"}, "dependencies": {"@nordvik/eslint-config": "workspace:*", "@nordvik/utils": "workspace:*", "@nordvik/theme": "workspace:*", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-tooltip": "1.2.7", "@tanstack/react-table": "8.21.3", "@radix-ui/react-visually-hidden": "1.2.3", "class-variance-authority": "0.7.1", "recharts": "2.15.3", "clsx": "2.1.1", "cmdk": "0.2.1", "date-fns": "4.1.0", "lucide-react": "0.511.0", "react": "19.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "tailwindcss-animate": "1.0.7", "vaul": "1.1.2"}, "devDependencies": {"@storybook/addon-styling-webpack": "^1.0.0", "@storybook/addon-webpack5-compiler-swc": "3.0.0", "@storybook/blocks": "8.3.5", "@storybook/react": "8.3.5", "@storybook/react-webpack5": "8.3.5", "@storybook/test": "8.3.5", "@types/node": "22.17.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "^10.4.17", "eslint": "8.57.0", "eslint-plugin-storybook": "0.9.0", "mockdate": "3.0.5", "postcss": "^8.4.33", "postcss-loader": "^8.1.1", "storybook": "8.3.5", "tailwindcss": "3.4.4", "typescript": "5.8.3"}}