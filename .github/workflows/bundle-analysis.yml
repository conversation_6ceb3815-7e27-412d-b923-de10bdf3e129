name: Bundle Analysis

on:
  pull_request:
    branches: [main, dev]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'pnpm-lock.yaml'
  push:
    branches: [main, dev]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'pnpm-lock.yaml'

jobs:
  bundle-analysis:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Needed for comparison with previous commit

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'pnpm'
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          # Use frozen lockfile for faster, deterministic installs
          pnpm install --frozen-lockfile --prefer-offline
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          # Ignore peer dependency warnings for CI
          NPM_CONFIG_LEGACY_PEER_DEPS: 'true'

      - name: Cache Prisma client
        uses: actions/cache@v4
        with:
          path: apps/web/node_modules/.prisma
          key: ${{ runner.os }}-prisma-${{ hashFiles('apps/web/prisma/schema.prisma') }}
          restore-keys: |
            ${{ runner.os }}-prisma-

      - name: Generate Prisma client
        run: |
          # Only generate if not cached
          if [ ! -d "node_modules/.prisma" ]; then
            npx prisma generate --no-engine
          fi
        working-directory: apps/web
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'
          # Skip Prisma telemetry for faster generation
          CHECKPOINT_DISABLE: '1'

      - name: Cache Next.js build
        uses: actions/cache@v4
        with:
          path: |
            apps/web/.next/cache
            apps/web/.next/static
          key: ${{ runner.os }}-nextjs-${{ hashFiles('apps/web/package.json', 'pnpm-lock.yaml') }}-${{ hashFiles('apps/web/src/**/*.[jt]s', 'apps/web/src/**/*.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('apps/web/package.json', 'pnpm-lock.yaml') }}-

      - name: Build application
        run: |
          # Use Next.js build with optimizations
          NEXT_BUILD_CACHE=1 pnpm run build
        working-directory: apps/web
        env:
          # Disable Sentry source map upload in CI for bundle analysis
          SENTRY_AUTH_TOKEN: ''
          # Skip Sentry upload completely for faster builds
          SENTRY_UPLOAD_SOURCEMAPS: 'false'
          # Required env vars for build (use dummy values)
          PG_DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
          NEXTAUTH_SECRET: 'dummy-secret-for-build'
          NEXTAUTH_URL: 'http://localhost:3000'
          # Redis configuration for Upstash
          REDIS_REST_API_URL: 'https://dummy-redis-url.upstash.io'
          REDIS_REST_API_TOKEN: 'dummy-redis-token'
          # MongoDB
          MONGODB_URI: '*******************************************'
          # URLs
          NEXT_PUBLIC_URL: 'http://localhost:3000'
          NEXT_PUBLIC_NORDVIK_APP_URL: 'https://nordvik.app'
          CRAFT_CMS_URL: 'https://cms-staging.nordvikbolig.no'
          NORDVIK_APP_API_URL: 'https://api.nordvik.app'
          OLD_MEGLER_URL: 'https://megler-gammel.nordvikbolig.no'
          SYNC_API_URL: 'https://sync.nordvikbolig.no'
          NORDVIK_NO_API_URL: 'https://api.nordvikbolig.no'
          # Auth
          AUTH_SECRET: 'dummy-auth-secret-for-build'
          AUTH_BROKER_API_URL: 'https://api.nordvik.app/auth/login/broker'
          # SMTP/Email
          SMTP_HOST: 'smtp.dummy.com'
          SMTP_USER: 'dummy-user'
          SMTP_PASSWORD: 'dummy-password'
          SMTP_FROM: '<EMAIL>'
          NO_REPLY_EMAIL: '<EMAIL>'
          # Mandrill
          MANDRILL_API_KEY: 'dummy-mandrill-key'
          # Adplenty
          ADPLENTY_LOGIN_URL: 'https://api-pro.adplenty.io/login/nordvik'
          ADPLENTY_AUTH_KEY: 'dummy-adplenty-auth-key'
          ADPLENTY_PARTNER_API_KEY: 'dummy-adplenty-partner-key'
          # Vitec
          VITEC_USER: 'dummy-vitec-user'
          VITEC_PASSWORD: 'dummy-vitec-password'
          VITEC_INSTALLATION_ID: 'DUMMY'
          VITEC_URL: 'https://hub.megler.vitec.net'
          # Signicat
          SIGNICAT_API_URL: 'https://api.signicat.com'
          SIGNICAT_CLIENT_ID: 'dummy-signicat-client-id'
          SIGNICAT_CLIENT_SECRET: 'dummy-signicat-client-secret'
          SIGNICAT_ACCOUNT_ID: 'dummy-signicat-account-id'
          HANDWRITTEN_SIGNATURE: 'false'
          # Eiendomsverdi
          EV_TOKEN_URL: 'https://api.eiendomsverdi.no/oauth/token'
          EV_API_URL: 'https://api.eiendomsverdi.no'
          EV_CLIENT_ID: 'dummy-ev-client-id'
          EV_CLIENT_SECRET: 'dummy-ev-client-secret'
          # AWS
          AWS_REGION: 'eu-west-1'
          AWS_ACCESS_KEY_ID: 'dummy-aws-access-key'
          AWS_SECRET_ACCESS_KEY: 'dummy-aws-secret-key'
          # Analytics
          NEXT_PUBLIC_POSTHOG_KEY: 'dummy-posthog-key'
          NEXT_PUBLIC_POSTHOG_HOST: 'https://dummy-posthog.com'
          NEXT_PUBLIC_FEATURE_FLAGS: 'dummy-flag1,dummy-flag2'
          # Vercel
          NEXT_PUBLIC_VERCEL_BRANCH_URL: 'dummy-vercel-url.vercel.app'
          NEXT_PUBLIC_VERCEL_ENV: 'preview'
          VERCEL_ENV: 'preview'
          VERCEL_API_TOKEN: 'dummy-vercel-token'
          # Mailchimp
          MAILCHIMP_API_KEY: 'dummy-mailchimp-key'
          # Build optimizations for speed
          NODE_OPTIONS: '--max-old-space-size=6144'
          NEXT_TELEMETRY_DISABLED: '1'
          # Skip type checking during build (faster)
          SKIP_TYPE_CHECK: 'true'
          # Skip linting during build (faster)
          SKIP_LINT: 'true'
          # Production build optimizations
          NODE_ENV: 'production'

      - name: Run bundle analysis
        run: pnpm run bundle:report
        working-directory: apps/web
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'

      - name: Run bundle size check
        run: pnpm run bundle:size
        working-directory: apps/web
        continue-on-error: true
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'

      - name: Upload bundle analysis artifacts
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis-${{ github.sha }}
          path: |
            apps/web/.next/analyze/
            apps/web/.next/static/chunks/
            apps/web/.next/static/css/
          retention-days: 30

      - name: Comment bundle analysis on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');

            try {
              const analysisFile = 'apps/web/.next/analyze/current-analysis.json';
              if (fs.existsSync(analysisFile)) {
                const analysis = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));
                
                const totalSizeKB = analysis.totalSize.toFixed(2);
                const jsFilesCount = analysis.jsFiles.length;
                const cssFilesCount = analysis.cssFiles.length;
                
                const largestJsFiles = analysis.jsFiles.slice(0, 3)
                  .map(file => \`- \${file.name}: \${file.size} KB\`)
                  .join('\\n');
                
                const comment = \`## 📦 Bundle Analysis Report
                
            **Total Bundle Size:** \${totalSizeKB} KB
            **JavaScript Files:** \${jsFilesCount}
            **CSS Files:** \${cssFilesCount}

            ### Largest JavaScript Files:
            \${largestJsFiles}

            ### Recommendations:
            \${analysis.totalSize > 1000 ? '⚠️ Bundle size is large. Consider code splitting and removing unused dependencies.' : '✅ Bundle size looks good!'}

            <!-- This comment was automatically generated by the bundle analysis workflow. -->\`;

                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            } catch (error) {
              console.log('Could not create bundle analysis comment:', error.message);
            }

  bundle-size-comparison:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout PR
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'pnpm'
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          # Use frozen lockfile for faster, deterministic installs
          pnpm install --frozen-lockfile --prefer-offline
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          NPM_CONFIG_LEGACY_PEER_DEPS: 'true'

      - name: Cache Prisma client (PR)
        uses: actions/cache@v4
        with:
          path: apps/web/node_modules/.prisma
          key: ${{ runner.os }}-prisma-pr-${{ hashFiles('apps/web/prisma/schema.prisma') }}
          restore-keys: |
            ${{ runner.os }}-prisma-

      - name: Generate Prisma client (PR)
        run: |
          if [ ! -d "node_modules/.prisma" ]; then
            npx prisma generate --no-engine
          fi
        working-directory: apps/web
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'
          CHECKPOINT_DISABLE: '1'

      - name: Build PR version
        run: |
          NEXT_BUILD_CACHE=1 pnpm run build
        working-directory: apps/web
        env:
          SENTRY_AUTH_TOKEN: ''
          SENTRY_UPLOAD_SOURCEMAPS: 'false'
          PG_DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
          NEXTAUTH_SECRET: 'dummy-secret-for-build'
          NEXTAUTH_URL: 'http://localhost:3000'
          REDIS_REST_API_URL: 'https://dummy-redis-url.upstash.io'
          REDIS_REST_API_TOKEN: 'dummy-redis-token'
          MONGODB_URI: '*******************************************'
          NEXT_PUBLIC_URL: 'http://localhost:3000'
          NEXT_PUBLIC_NORDVIK_APP_URL: 'https://nordvik.app'
          CRAFT_CMS_URL: 'https://cms-staging.nordvikbolig.no'
          NORDVIK_APP_API_URL: 'https://api.nordvik.app'
          OLD_MEGLER_URL: 'https://megler-gammel.nordvikbolig.no'
          SYNC_API_URL: 'https://sync.nordvikbolig.no'
          NORDVIK_NO_API_URL: 'https://api.nordvikbolig.no'
          AUTH_SECRET: 'dummy-auth-secret-for-build'
          AUTH_BROKER_API_URL: 'https://api.nordvik.app/auth/login/broker'
          SMTP_HOST: 'smtp.dummy.com'
          SMTP_USER: 'dummy-user'
          SMTP_PASSWORD: 'dummy-password'
          SMTP_FROM: '<EMAIL>'
          NO_REPLY_EMAIL: '<EMAIL>'
          MANDRILL_API_KEY: 'dummy-mandrill-key'
          ADPLENTY_LOGIN_URL: 'https://api-pro.adplenty.io/login/nordvik'
          ADPLENTY_AUTH_KEY: 'dummy-adplenty-auth-key'
          ADPLENTY_PARTNER_API_KEY: 'dummy-adplenty-partner-key'
          VITEC_USER: 'dummy-vitec-user'
          VITEC_PASSWORD: 'dummy-vitec-password'
          VITEC_INSTALLATION_ID: 'DUMMY'
          VITEC_URL: 'https://hub.megler.vitec.net'
          SIGNICAT_API_URL: 'https://api.signicat.com'
          SIGNICAT_CLIENT_ID: 'dummy-signicat-client-id'
          SIGNICAT_CLIENT_SECRET: 'dummy-signicat-client-secret'
          SIGNICAT_ACCOUNT_ID: 'dummy-signicat-account-id'
          HANDWRITTEN_SIGNATURE: 'false'
          EV_TOKEN_URL: 'https://api.eiendomsverdi.no/oauth/token'
          EV_API_URL: 'https://api.eiendomsverdi.no'
          EV_CLIENT_ID: 'dummy-ev-client-id'
          EV_CLIENT_SECRET: 'dummy-ev-client-secret'
          AWS_REGION: 'eu-west-1'
          AWS_ACCESS_KEY_ID: 'dummy-aws-access-key'
          AWS_SECRET_ACCESS_KEY: 'dummy-aws-secret-key'
          NEXT_PUBLIC_POSTHOG_KEY: 'dummy-posthog-key'
          NEXT_PUBLIC_POSTHOG_HOST: 'https://dummy-posthog.com'
          NEXT_PUBLIC_FEATURE_FLAGS: 'dummy-flag1,dummy-flag2'
          NEXT_PUBLIC_VERCEL_BRANCH_URL: 'dummy-vercel-url.vercel.app'
          NEXT_PUBLIC_VERCEL_ENV: 'preview'
          VERCEL_ENV: 'preview'
          VERCEL_API_TOKEN: 'dummy-vercel-token'
          MAILCHIMP_API_KEY: 'dummy-mailchimp-key'
          NODE_OPTIONS: '--max-old-space-size=6144'
          NEXT_TELEMETRY_DISABLED: '1'
          SKIP_TYPE_CHECK: 'true'
          SKIP_LINT: 'true'
          NODE_ENV: 'production'
          CHECKPOINT_DISABLE: '1'

      - name: Save PR bundle stats
        run: |
          mkdir -p /tmp/pr-bundle
          # Save bundle size and file count for comparison
          du -sk apps/web/.next/static > /tmp/pr-bundle/size.txt
          find apps/web/.next/static -name "*.js" | wc -l > /tmp/pr-bundle/js-count.txt
          find apps/web/.next/static -name "*.css" | wc -l > /tmp/pr-bundle/css-count.txt
        continue-on-error: true

      - name: Clean PR build to free memory
        run: |
          rm -rf apps/web/.next
          rm -rf apps/web/node_modules/.cache

      - name: Checkout base branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.base_ref }}
          clean: false

      - name: Install dependencies (base)
        run: |
          # Reuse cached dependencies when possible
          pnpm install --frozen-lockfile --prefer-offline
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          NPM_CONFIG_LEGACY_PEER_DEPS: 'true'

      - name: Generate Prisma client (base)
        run: |
          if [ ! -d "node_modules/.prisma" ]; then
            npx prisma generate --no-engine
          fi
        working-directory: apps/web
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'
          CHECKPOINT_DISABLE: '1'

      - name: Build base version
        run: |
          NEXT_BUILD_CACHE=1 pnpm run build
        working-directory: apps/web
        env:
          SENTRY_AUTH_TOKEN: ''
          SENTRY_UPLOAD_SOURCEMAPS: 'false'
          PG_DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
          NEXTAUTH_SECRET: 'dummy-secret-for-build'
          NEXTAUTH_URL: 'http://localhost:3000'
          REDIS_REST_API_URL: 'https://dummy-redis-url.upstash.io'
          REDIS_REST_API_TOKEN: 'dummy-redis-token'
          MONGODB_URI: '*******************************************'
          NEXT_PUBLIC_URL: 'http://localhost:3000'
          NEXT_PUBLIC_NORDVIK_APP_URL: 'https://nordvik.app'
          CRAFT_CMS_URL: 'https://cms-staging.nordvikbolig.no'
          NORDVIK_APP_API_URL: 'https://api.nordvik.app'
          OLD_MEGLER_URL: 'https://megler-gammel.nordvikbolig.no'
          SYNC_API_URL: 'https://sync.nordvikbolig.no'
          NORDVIK_NO_API_URL: 'https://api.nordvikbolig.no'
          AUTH_SECRET: 'dummy-auth-secret-for-build'
          AUTH_BROKER_API_URL: 'https://api.nordvik.app/auth/login/broker'
          SMTP_HOST: 'smtp.dummy.com'
          SMTP_USER: 'dummy-user'
          SMTP_PASSWORD: 'dummy-password'
          SMTP_FROM: '<EMAIL>'
          NO_REPLY_EMAIL: '<EMAIL>'
          MANDRILL_API_KEY: 'dummy-mandrill-key'
          ADPLENTY_LOGIN_URL: 'https://api-pro.adplenty.io/login/nordvik'
          ADPLENTY_AUTH_KEY: 'dummy-adplenty-auth-key'
          ADPLENTY_PARTNER_API_KEY: 'dummy-adplenty-partner-key'
          VITEC_USER: 'dummy-vitec-user'
          VITEC_PASSWORD: 'dummy-vitec-password'
          VITEC_INSTALLATION_ID: 'DUMMY'
          VITEC_URL: 'https://hub.megler.vitec.net'
          SIGNICAT_API_URL: 'https://api.signicat.com'
          SIGNICAT_CLIENT_ID: 'dummy-signicat-client-id'
          SIGNICAT_CLIENT_SECRET: 'dummy-signicat-client-secret'
          SIGNICAT_ACCOUNT_ID: 'dummy-signicat-account-id'
          HANDWRITTEN_SIGNATURE: 'false'
          EV_TOKEN_URL: 'https://api.eiendomsverdi.no/oauth/token'
          EV_API_URL: 'https://api.eiendomsverdi.no'
          EV_CLIENT_ID: 'dummy-ev-client-id'
          EV_CLIENT_SECRET: 'dummy-ev-client-secret'
          AWS_REGION: 'eu-west-1'
          AWS_ACCESS_KEY_ID: 'dummy-aws-access-key'
          AWS_SECRET_ACCESS_KEY: 'dummy-aws-secret-key'
          NEXT_PUBLIC_POSTHOG_KEY: 'dummy-posthog-key'
          NEXT_PUBLIC_POSTHOG_HOST: 'https://dummy-posthog.com'
          NEXT_PUBLIC_FEATURE_FLAGS: 'dummy-flag1,dummy-flag2'
          NEXT_PUBLIC_VERCEL_BRANCH_URL: 'dummy-vercel-url.vercel.app'
          NEXT_PUBLIC_VERCEL_ENV: 'preview'
          VERCEL_ENV: 'preview'
          VERCEL_API_TOKEN: 'dummy-vercel-token'
          MAILCHIMP_API_KEY: 'dummy-mailchimp-key'
          NODE_OPTIONS: '--max-old-space-size=6144'
          NEXT_TELEMETRY_DISABLED: '1'
          SKIP_TYPE_CHECK: 'true'
          SKIP_LINT: 'true'
          NODE_ENV: 'production'
          CHECKPOINT_DISABLE: '1'

      - name: Compare bundle sizes
        run: |
          echo "## Bundle Size Comparison" > /tmp/comparison.md
          echo "" >> /tmp/comparison.md

          # Get base bundle size
          BASE_SIZE=$(du -sk apps/web/.next/static | cut -f1)

          # Get PR bundle size from saved stats
          PR_SIZE=$(cat /tmp/pr-bundle/size.txt | cut -f1)

          # Calculate difference
          DIFF=$((PR_SIZE - BASE_SIZE))

          # Calculate percentage (simplified)
          if [ "$BASE_SIZE" -ne 0 ]; then
            PERCENT=$(awk "BEGIN {printf \"%.2f\", $DIFF * 100 / $BASE_SIZE}")
          else
            PERCENT="0.00"
          fi

          echo "**Base Branch:** ${BASE_SIZE} KB" >> /tmp/comparison.md
          echo "**PR Branch:** ${PR_SIZE} KB" >> /tmp/comparison.md
          echo "**Difference:** ${DIFF} KB (${PERCENT}%)" >> /tmp/comparison.md
          echo "" >> /tmp/comparison.md

          if [ $DIFF -gt 100 ]; then
            echo "⚠️ **Warning:** Bundle size increased significantly!" >> /tmp/comparison.md
          elif [ $DIFF -gt 0 ]; then
            echo "📈 Bundle size increased slightly." >> /tmp/comparison.md
          elif [ $DIFF -lt 0 ]; then
            echo "📉 Bundle size decreased. Great job!" >> /tmp/comparison.md
          else
            echo "➡️ No change in bundle size." >> /tmp/comparison.md
          fi

      - name: Comment comparison on PR
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');

            try {
              const comparison = fs.readFileSync('/tmp/comparison.md', 'utf8');
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comparison
              });
            } catch (error) {
              console.log('Could not create comparison comment:', error.message);
            }
