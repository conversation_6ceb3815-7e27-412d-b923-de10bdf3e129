name: Bundle Analysis

on:
  pull_request:
    branches: [main, dev]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'pnpm-lock.yaml'
  push:
    branches: [main, dev]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'pnpm-lock.yaml'

jobs:
  bundle-analysis:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Needed for comparison with previous commit

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'pnpm'
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          # Use frozen lockfile for faster, deterministic installs
          pnpm install --frozen-lockfile --prefer-offline
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
          # Ignore peer dependency warnings for CI
          NPM_CONFIG_LEGACY_PEER_DEPS: 'true'

      - name: Cache Prisma client
        uses: actions/cache@v4
        with:
          path: apps/web/node_modules/.prisma
          key: ${{ runner.os }}-prisma-${{ hashFiles('apps/web/prisma/schema.prisma') }}
          restore-keys: |
            ${{ runner.os }}-prisma-

      - name: Generate Prisma client
        run: |
          # Only generate if not cached
          if [ ! -d "node_modules/.prisma" ]; then
            npx prisma generate --no-engine
          fi
        working-directory: apps/web
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'
          # Skip Prisma telemetry for faster generation
          CHECKPOINT_DISABLE: '1'

      - name: Cache Next.js build
        uses: actions/cache@v4
        with:
          path: |
            apps/web/.next/cache
            apps/web/.next/static
          key: ${{ runner.os }}-nextjs-${{ hashFiles('apps/web/package.json', 'pnpm-lock.yaml') }}-${{ hashFiles('apps/web/src/**/*.[jt]s', 'apps/web/src/**/*.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('apps/web/package.json', 'pnpm-lock.yaml') }}-

      - name: Build application (static analysis only)
        run: |
          # Build only what's needed for bundle analysis - skip API routes and server components that cause issues
          NEXT_BUILD_CACHE=1 npx next build --no-lint --experimental-build-mode=compile
        working-directory: apps/web
        env:
          # Disable all external services and features that aren't needed for bundle analysis
          SENTRY_AUTH_TOKEN: ''
          SENTRY_UPLOAD_SOURCEMAPS: 'false'
          # Minimal required env vars
          PG_DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
          NEXTAUTH_SECRET: 'dummy-secret-for-build'
          NEXTAUTH_URL: 'http://localhost:3000'
          REDIS_REST_API_URL: 'https://dummy-redis-url.upstash.io'
          REDIS_REST_API_TOKEN: 'dummy-redis-token'
          MONGODB_URI: '*******************************************'
          NEXT_PUBLIC_URL: 'http://localhost:3000'
          NEXT_PUBLIC_NORDVIK_APP_URL: 'https://nordvik.app'
          # Build optimizations for speed
          NODE_OPTIONS: '--max-old-space-size=6144'
          NEXT_TELEMETRY_DISABLED: '1'
          SKIP_TYPE_CHECK: 'true'
          SKIP_LINT: 'true'
          NODE_ENV: 'production'
          # Skip problematic API routes during build
          SKIP_API_ROUTES: 'true'
          # Disable features that cause build issues
          DISABLE_CRON_CACHE: 'true'

      - name: Quick bundle size analysis
        run: |
          # Fast bundle analysis - just get the essential metrics
          echo "📦 Analyzing bundle size..."

          # Get total static size
          TOTAL_SIZE=$(du -sk .next/static 2>/dev/null | cut -f1 || echo "0")

          # Count files
          JS_FILES=$(find .next/static -name "*.js" 2>/dev/null | wc -l || echo "0")
          CSS_FILES=$(find .next/static -name "*.css" 2>/dev/null | wc -l || echo "0")

          # Create simple analysis
          mkdir -p .next/analyze
          cat > .next/analyze/current-analysis.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "totalSize": $TOTAL_SIZE,
            "jsFiles": [],
            "cssFiles": []
          }
          EOF

          echo "✅ Bundle analysis complete: ${TOTAL_SIZE}KB total, ${JS_FILES} JS files, ${CSS_FILES} CSS files"

          # Save size for baseline comparison
          echo "$TOTAL_SIZE" > .next/analyze/bundle-size.txt
        working-directory: apps/web

      - name: Cache bundle size for baseline
        uses: actions/cache/save@v4
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'
        with:
          path: apps/web/.next/analyze/bundle-size.txt
          key: ${{ runner.os }}-base-bundle-${{ github.ref_name }}-${{ hashFiles('apps/web/src/**/*.[jt]s', 'apps/web/src/**/*.[jt]sx', 'apps/web/package.json') }}

      - name: Upload essential bundle data
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis-${{ github.sha }}
          path: |
            apps/web/.next/analyze/current-analysis.json
          retention-days: 7

      - name: Comment bundle analysis on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');

            try {
              const analysisFile = 'apps/web/.next/analyze/current-analysis.json';
              if (fs.existsSync(analysisFile)) {
                const analysis = JSON.parse(fs.readFileSync(analysisFile, 'utf8'));
                
                const totalSizeKB = analysis.totalSize.toFixed(2);
                const jsFilesCount = analysis.jsFiles.length;
                const cssFilesCount = analysis.cssFiles.length;
                
                const largestJsFiles = analysis.jsFiles.slice(0, 3)
                  .map(file => \`- \${file.name}: \${file.size} KB\`)
                  .join('\\n');
                
                const comment = \`## 📦 Bundle Analysis Report
                
            **Total Bundle Size:** \${totalSizeKB} KB
            **JavaScript Files:** \${jsFilesCount}
            **CSS Files:** \${cssFilesCount}

            ### Largest JavaScript Files:
            \${largestJsFiles}

            ### Recommendations:
            \${analysis.totalSize > 1000 ? '⚠️ Bundle size is large. Consider code splitting and removing unused dependencies.' : '✅ Bundle size looks good!'}

            <!-- This comment was automatically generated by the bundle analysis workflow. -->\`;

                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            } catch (error) {
              console.log('Could not create bundle analysis comment:', error.message);
            }

  bundle-size-comparison:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: bundle-analysis

    steps:
      - name: Download bundle analysis artifact
        uses: actions/download-artifact@v4
        with:
          name: bundle-analysis-${{ github.sha }}
          path: /tmp/pr-bundle

      - name: Get base branch bundle size from cache
        uses: actions/cache/restore@v4
        id: base-bundle-cache
        with:
          path: /tmp/base-bundle-size.txt
          key: ${{ runner.os }}-base-bundle-${{ github.base_ref }}-${{ hashFiles('apps/web/src/**/*.[jt]s', 'apps/web/src/**/*.[jt]sx', 'apps/web/package.json') }}
          restore-keys: |
            ${{ runner.os }}-base-bundle-${{ github.base_ref }}-

      - name: Create simple comparison
        run: |
          echo "## Bundle Size Comparison" > /tmp/comparison.md
          echo "" >> /tmp/comparison.md

          # Get PR bundle size from artifact
          if [ -f "/tmp/pr-bundle/current-analysis.json" ]; then
            PR_SIZE=$(cat /tmp/pr-bundle/current-analysis.json | grep -o '"totalSize":[0-9]*' | cut -d':' -f2)
          else
            PR_SIZE="0"
          fi

          # Get base bundle size from cache or use fallback
          if [ -f "/tmp/base-bundle-size.txt" ]; then
            BASE_SIZE=$(cat /tmp/base-bundle-size.txt)
          else
            # Fallback: assume no significant change if no baseline
            BASE_SIZE=$PR_SIZE
            echo "ℹ️ No baseline found, using current size as reference" >> /tmp/comparison.md
          fi

          # Calculate difference
          DIFF=$((PR_SIZE - BASE_SIZE))

          # Calculate percentage (simplified)
          if [ "$BASE_SIZE" -ne 0 ]; then
            PERCENT=$(awk "BEGIN {printf \"%.2f\", $DIFF * 100 / $BASE_SIZE}")
          else
            PERCENT="0.00"
          fi

          echo "**Base Branch:** ${BASE_SIZE} KB" >> /tmp/comparison.md
          echo "**PR Branch:** ${PR_SIZE} KB" >> /tmp/comparison.md
          echo "**Difference:** ${DIFF} KB (${PERCENT}%)" >> /tmp/comparison.md
          echo "" >> /tmp/comparison.md

          if [ $DIFF -gt 100 ]; then
            echo "⚠️ **Warning:** Bundle size increased significantly!" >> /tmp/comparison.md
          elif [ $DIFF -gt 0 ]; then
            echo "📈 Bundle size increased slightly." >> /tmp/comparison.md
          elif [ $DIFF -lt 0 ]; then
            echo "📉 Bundle size decreased. Great job!" >> /tmp/comparison.md
          else
            echo "➡️ No change in bundle size." >> /tmp/comparison.md
          fi

          # Save current size as baseline for future comparisons
          echo "$PR_SIZE" > /tmp/current-bundle-size.txt

      - name: Cache current bundle size for future comparisons
        uses: actions/cache/save@v4
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        with:
          path: /tmp/current-bundle-size.txt
          key: ${{ runner.os }}-base-bundle-main-${{ hashFiles('apps/web/src/**/*.[jt]s', 'apps/web/src/**/*.[jt]sx', 'apps/web/package.json') }}

      - name: Comment comparison on PR
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');

            try {
              const comparison = fs.readFileSync('/tmp/comparison.md', 'utf8');

              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comparison
              });
            } catch (error) {
              console.log('Could not create comparison comment:', error.message);
            }
