# Shared environment variables for CI builds
# This file contains common environment variables used across different workflows
# to reduce duplication and ensure consistency

env:
  # Build optimizations
  NODE_OPTIONS: '--max-old-space-size=6144'
  NEXT_TELEMETRY_DISABLED: '1'
  SKIP_TYPE_CHECK: 'true'
  SKIP_LINT: 'true'
  NODE_ENV: 'production'
  CHECKPOINT_DISABLE: '1'
  
  # Sentry (disabled for CI builds)
  SENTRY_AUTH_TOKEN: ''
  SENTRY_UPLOAD_SOURCEMAPS: 'false'
  
  # Database (dummy values for build)
  PG_DATABASE_URL: 'postgresql://dummy:dummy@localhost:5432/dummy'
  MONGODB_URI: '*******************************************'
  
  # Auth (dummy values for build)
  NEXTAUTH_SECRET: 'dummy-secret-for-build'
  NEXTAUTH_URL: 'http://localhost:3000'
  AUTH_SECRET: 'dummy-auth-secret-for-build'
  AUTH_BROKER_API_URL: 'https://api.nordvik.app/auth/login/broker'
  
  # Redis (dummy values for build)
  REDIS_REST_API_URL: 'https://dummy-redis-url.upstash.io'
  REDIS_REST_API_TOKEN: 'dummy-redis-token'
  
  # URLs
  NEXT_PUBLIC_URL: 'http://localhost:3000'
  NEXT_PUBLIC_NORDVIK_APP_URL: 'https://nordvik.app'
  CRAFT_CMS_URL: 'https://cms-staging.nordvikbolig.no'
  NORDVIK_APP_API_URL: 'https://api.nordvik.app'
  OLD_MEGLER_URL: 'https://megler-gammel.nordvikbolig.no'
  SYNC_API_URL: 'https://sync.nordvikbolig.no'
  NORDVIK_NO_API_URL: 'https://api.nordvikbolig.no'
  
  # SMTP/Email (dummy values for build)
  SMTP_HOST: 'smtp.dummy.com'
  SMTP_USER: 'dummy-user'
  SMTP_PASSWORD: 'dummy-password'
  SMTP_FROM: '<EMAIL>'
  NO_REPLY_EMAIL: '<EMAIL>'
  
  # External services (dummy values for build)
  MANDRILL_API_KEY: 'dummy-mandrill-key'
  ADPLENTY_LOGIN_URL: 'https://api-pro.adplenty.io/login/nordvik'
  ADPLENTY_AUTH_KEY: 'dummy-adplenty-auth-key'
  ADPLENTY_PARTNER_API_KEY: 'dummy-adplenty-partner-key'
  VITEC_USER: 'dummy-vitec-user'
  VITEC_PASSWORD: 'dummy-vitec-password'
  VITEC_INSTALLATION_ID: 'DUMMY'
  VITEC_URL: 'https://hub.megler.vitec.net'
  SIGNICAT_API_URL: 'https://api.signicat.com'
  SIGNICAT_CLIENT_ID: 'dummy-signicat-client-id'
  SIGNICAT_CLIENT_SECRET: 'dummy-signicat-client-secret'
  SIGNICAT_ACCOUNT_ID: 'dummy-signicat-account-id'
  HANDWRITTEN_SIGNATURE: 'false'
  EV_TOKEN_URL: 'https://api.eiendomsverdi.no/oauth/token'
  EV_API_URL: 'https://api.eiendomsverdi.no'
  EV_CLIENT_ID: 'dummy-ev-client-id'
  EV_CLIENT_SECRET: 'dummy-ev-client-secret'
  AWS_REGION: 'eu-west-1'
  AWS_ACCESS_KEY_ID: 'dummy-aws-access-key'
  AWS_SECRET_ACCESS_KEY: 'dummy-aws-secret-key'
  NEXT_PUBLIC_POSTHOG_KEY: 'dummy-posthog-key'
  NEXT_PUBLIC_POSTHOG_HOST: 'https://dummy-posthog.com'
  NEXT_PUBLIC_FEATURE_FLAGS: 'dummy-flag1,dummy-flag2'
  NEXT_PUBLIC_VERCEL_BRANCH_URL: 'dummy-vercel-url.vercel.app'
  NEXT_PUBLIC_VERCEL_ENV: 'preview'
  VERCEL_ENV: 'preview'
  VERCEL_API_TOKEN: 'dummy-vercel-token'
  MAILCHIMP_API_KEY: 'dummy-mailchimp-key'
