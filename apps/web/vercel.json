{"regions": ["arn1"], "crons": [{"path": "/api/cron-cache", "schedule": "*/10 * * * *"}, {"path": "/api/signicat/sync-agreements", "schedule": "0,30 6-23,0 * * *"}, {"path": "/api/signicat/updated-agreements", "schedule": "15,45 6-23,0 * * *"}, {"path": "/api/etakst-check", "schedule": "*/5 0,6-23 * * *"}, {"path": "/api/etakst-check/broker-tasks-completed", "schedule": "30 7 * * *"}, {"path": "/api/monitoring/health/update", "schedule": "*/5 * * * *"}]}