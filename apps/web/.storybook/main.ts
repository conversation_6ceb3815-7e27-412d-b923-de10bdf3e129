import { loadEnvConfig } from '@next/env'
import type { StorybookConfig } from '@storybook/nextjs'
import path, { dirname, join } from 'path'

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')))
}

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.tsx'],
  webpackFinal: async (config) => {
    config.resolve = {
      ...(config.resolve || {}),
      alias: {
        ...config.resolve?.alias,
        '@': path.resolve(__dirname, '../src'),
      },
    }
    return config
  },
  addons: [
    {
      name: getAbsolutePath('@storybook/addon-styling-webpack'),
      options: {
        rules: [
          {
            test: /\.css$/,
            sideEffects: true,
            use: [
              require.resolve('style-loader'),
              {
                loader: require.resolve('css-loader'),
                options: {
                  importLoaders: 1,
                },
              },
              {
                loader: require.resolve('postcss-loader'),
                options: {
                  implementation: require.resolve('postcss'),
                },
              },
            ],
          },
        ],
      },
    },
    '@storybook/addon-interactions',
  ],
  swc: () => ({
    jsc: {
      transform: {
        react: {
          runtime: 'automatic',
        },
      },
    },
  }),
  staticDirs: ['../public', '../../../packages/nordvik-utils/src/fonts'],
  framework: getAbsolutePath('@storybook/nextjs'),
  managerHead: (head) =>
    `
    ${head}
    <link rel="stylesheet" href="./fonts.css" />
    <link rel="icon" type="image/svg+xml" href="icon.svg" />
    <style>
      body {
        font-family: "Basel Grotesk" !important;
        font-weight: 435  !important;
      }

      /* Hide the Storybook add button */
      .sidebar-container div[role="combobox"] ~ div {
        display: none !important;
      }
      .sidebar-header img {
        margin-left: -19px !important;
      }
      [data-action="expand-all"],
      .sidebar-header button[title="Shortcuts"],
      .sidebar-container button[title="About Storybook"] {
        display: none !important;
      }
      svg[type="document"],
      svg[type="group"],
      svg[type="story"],
      svg[type="component"] {
        width: 0 !important;
      }
      div:not([data-parent-id]) .sidebar-item,
      button.sidebar-item {
        font-weight: 535;
      }

      .sidebar-subheading {
        font-weight: 535;
      }

      /* Hide the sidebar drag handle */
      div:has(~.sidebar-container) {
        opacity: 0;
      }
      div:has(>.sidebar-container) {
        border-color: #193d41;
      }
      #storybook-explorer-searchfield {
        border: transparent;
        box-shadow: none;
        background: #ffffff12;
        font-weight: 535;
        height: 38px;
      }
      #storybook-explorer-searchfield::placeholder {
        color: rgba(255, 255, 255, 0.3) !important;
      }
      #back-to-chromatic {
        display: none !important;
      }
    </style>
  `,
}
export default config
