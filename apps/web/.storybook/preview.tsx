import type { Decorator, Preview } from '@storybook/react'
import { MotionGlobalConfig } from 'framer-motion'
import MockDate from 'mockdate'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { ToastProvider } from '@nordvik/ui/toast'
import { TooltipProvider } from '@nordvik/ui/tooltip'

import '../src/app/globals.css'
import { QueryClientProvider } from '../src/lib/queryClient'

MotionGlobalConfig.skipAnimations = process.env.STORYBOOK_CI === 'true'

const themes = ['light', 'dark'] as const

const MockDateDecorator: Decorator = (Story) => {
  if (process.env.STORYBOOK_CI) MockDate.set('2025-06-06T12:20:15Z')
  return <Story />
}

const preview: Preview = {
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
    },
    options: {
      storySort: {
        order: [],
      },
    },
  },
  decorators: [
    MockDateDecorator,
    (Story, context) => {
      const theme = context.parameters.theme || 'light'
      const bothThemes = theme === 'both'
      if (!bothThemes && !themes.includes(theme)) {
        throw new Error(`Invalid theme: ${theme}`)
      }
      const rootClassname = cn(
        'antialiased bg-root-muted font-grotesk ink-default font-normal flex flex-col min-h-[100dvh]',
        !context.parameters.fullscreen && 'p-4 gap-4',
      )
      const storyClassname = cn(
        'bg-root grow font-grotesk ink-default font-normal p-[--story-padding] min-w-0',
        !context.parameters.fullscreen && '[--story-padding:1rem] rounded-sm',
        context.parameters.flex && 'flex flex-col grow',
      )
      const story = <Story />

      if (bothThemes)
        return (
          <div data-theme="dark" className={rootClassname}>
            {themes.map((theme) => (
              <div key={theme} data-theme={theme} className={storyClassname}>
                {story}
              </div>
            ))}
          </div>
        )

      return (
        <div data-theme="dark" className={rootClassname}>
          <div data-theme={theme} className={storyClassname}>
            {story}
          </div>
        </div>
      )
    },
    (Story) => (
      <QueryClientProvider>
        <TooltipProvider>
          <ToastProvider>
            <Story />
          </ToastProvider>
        </TooltipProvider>
      </QueryClientProvider>
    ),
  ],
}

export default preview
