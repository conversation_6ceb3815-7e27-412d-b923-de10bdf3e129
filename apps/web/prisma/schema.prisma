generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

model Areas {
  id          String     @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt   DateTime?  @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt   DateTime?  @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  name        String?
  parentId    String?    @db.Uuid
  weight      Int?       @default(0)
  postalCodes String[]   @default([])
  Areas       Areas?     @relation("AreasToAreas", fields: [parentId], references: [id], onDelete: Cascade)
  other_Areas Areas[]    @relation("AreasToAreas")
  UserArea    UserArea[]

  @@unique([name, parentId], map: "areas_name_parent_id")
}

model AverageSalePrice {
  id                              String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                       DateTime? @db.Timestamptz(6)
  updatedAt                       DateTime? @db.Timestamptz(6)
  averageTimeForPropertySale      Decimal?  @db.Decimal(32, 4)
  averagePricePerSquareMeter      Decimal?  @db.Decimal(32, 4)
  averagePriceAbsolute            Decimal?  @db.Decimal(32, 4)
  postalCode                      String?   @db.VarChar(255)
  averageTimeForPropertySaleCount Int       @default(0)
  averagePricePerSquareMeterCount Int       @default(0)
  averagePriceAbsoluteCount       Int       @default(0)
}

model BrokerActivity {
  id        String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime?             @db.Timestamptz(6)
  updatedAt DateTime?             @db.Timestamptz(6)
  brokerID  String                @db.Uuid
  type      broker_activity_type?
  data      Json?
  Brokers   Brokers               @relation(fields: [brokerID], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model Brokers {
  id                 String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt          DateTime?        @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt          DateTime?        @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  email              String           @unique
  password           String?
  vitecID            String
  passwordCode       String           @default(dbgenerated("uuid_generate_v4()"))
  verifyPasswordCode String?
  isVerified         Boolean          @default(false)
  verifyEmailCode    String?          @db.Uuid
  referralCode       String           @default("") @db.VarChar(255)
  BrokerActivity     BrokerActivity[]
}

model Coupons {
  id        String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime?   @db.Timestamptz(6)
  updatedAt DateTime?   @db.Timestamptz(6)
  code      String
  type      coupon_type
  rewardID  String?     @db.Uuid
  Rewards   Rewards?    @relation(fields: [rewardID], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model DismissedActions {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  actionID  String
  brokerID  String
  estateID  String
}

model EiendomsverdiAudit {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @db.Timestamptz(6)
  updatedAt DateTime? @db.Timestamptz(6)
  userID    String?   @db.Uuid
  response  String
  Users     Users?    @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model EiendomsverdiEstateCache {
  id         String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt  DateTime? @db.Timestamptz(6)
  updatedAt  DateTime? @db.Timestamptz(6)
  query      String?
  cacheType  String?
  lastSynced DateTime  @db.Timestamptz(6)
  data       Json
  queryHash  String    @unique @db.Char(64)
}

model EstateChecklistConnection {
  id               String                   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt        DateTime?                @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt        DateTime?                @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  vitecID          String?
  userID           String                   @db.Uuid
  checklistID      estate_checklist_id_enum
  doneTodoID       Int
  EstateChecklists EstateChecklists         @relation(fields: [checklistID], references: [checklistID], onDelete: NoAction, onUpdate: NoAction)
  EstateTodos      EstateTodos              @relation(fields: [doneTodoID], references: [todoID], onDelete: NoAction, onUpdate: NoAction)
  Users            Users                    @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model EstateChecklists {
  id                        String                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  checklistID               estate_checklist_id_enum?   @unique
  title                     String?
  type                      estate_checklist_type_enum?
  logName                   String?                     @db.VarChar(255)
  sortOrder                 Int                         @default(0)
  EstateChecklistConnection EstateChecklistConnection[]
  EstateTodos               EstateTodos[]
}

model EstateEventConnection {
  id           String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt    DateTime?            @db.Timestamptz(6)
  updatedAt    DateTime?            @db.Timestamptz(6)
  vitecID      String?
  userID       String               @db.Uuid
  eventID      estate_event_id_enum
  date         DateTime             @db.Timestamptz(6)
  EstateEvents EstateEvents         @relation(fields: [eventID], references: [eventID], onDelete: NoAction, onUpdate: NoAction)
  Users        Users                @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model EstateEvents {
  id                    String                  @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt             DateTime?               @db.Timestamptz(6)
  updatedAt             DateTime?               @db.Timestamptz(6)
  eventID               estate_event_id_enum?   @unique
  title                 String?
  type                  estate_event_type_enum?
  logName               String?                 @db.VarChar(255)
  EstateEventConnection EstateEventConnection[]
}

model EstateImagesFromUsers {
  userID                   String    @db.Uuid
  landIdentificationMatrix Json
  createdAt                DateTime? @db.Timestamptz(6)
  updatedAt                DateTime? @db.Timestamptz(6)
  imagePublicUrl           String?
  Users                    Users     @relation(fields: [userID], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([userID, landIdentificationMatrix])
}

model EstatePriceHistories {
  id                         String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                  DateTime? @db.Timestamptz(6)
  updatedAt                  DateTime? @db.Timestamptz(6)
  postgresEstateId           String    @db.Uuid
  landIdentificationMatrix   Json
  evPrice                    Int
  actualPriceWithVitecOffset Int

  @@index([postgresEstateId], map: "idx_postgres_estate_id")
}

model EstateTodos {
  id                        String                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  checklistID               estate_checklist_id_enum
  todoID                    Int                         @unique @default(autoincrement())
  title                     String?
  description               String?
  type                      estate_checklist_type_enum?
  logName                   String?                     @db.VarChar(255)
  EstateChecklistConnection EstateChecklistConnection[]
  EstateChecklists          EstateChecklists            @relation(fields: [checklistID], references: [checklistID], onDelete: NoAction, onUpdate: NoAction)
}

model Estates {
  id                       String                @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                DateTime?             @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt                DateTime?             @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID                   String                @db.Uuid
  type                     estate_type_enum      @default(owned)
  address                  String?
  numberOfBedrooms         Int?
  sellPreference           user_preference_enum?
  propertyType             String?
  landIdentificationMatrix Json?
  ownership                String?
  livingArea               Int?
  buildYear                Int?
  floor                    Int?
  connectToBroker          Boolean?              @default(false)
  EVEstateID               String?               @db.VarChar(255)
  EVAddressID              String?               @db.VarChar(255)
  loanAmount               Int?
  interestRate             Float?
  originalMortgage         Int?
  imageUrl                 String?               @db.VarChar(255)
  OrganizationNumber       String?               @db.VarChar(255)
  ShareNumber              String?               @db.VarChar(255)
  mortgageYears            Int?
  isArchived               Boolean?              @default(false)
  estimationOffset         Float?                @db.Real
  Users                    Users                 @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model Estates_audit {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  operation String
  oldData   Json?
  newData   Json?
  changedAt DateTime? @default(now()) @db.Timestamptz(6)
  userID    String?   @db.Uuid
  Users     Users?    @relation(fields: [userID], references: [id])
}

model ExternalLeadAudit {
  id                 Int       @id @default(autoincrement())
  createdAt          DateTime? @db.Timestamptz(6)
  updatedAt          DateTime? @db.Timestamptz(6)
  leadType           String    @db.VarChar(255)
  isSuccessful       Boolean?
  externalLeadId     String?   @db.VarChar(255)
  name               String?   @db.VarChar(255)
  email              String?   @db.VarChar(255)
  phone              String?   @db.VarChar(255)
  address            String?
  postalCode         String?   @db.VarChar(255)
  brokerId           String?   @db.VarChar(255)
  departmentOfBroker String?   @db.VarChar(255)
  data               Json?
}

model Favorites {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  estateID  String?
  userID    String?   @db.Uuid
  Users     Users?    @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model Feeds {
  id          String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt   DateTime? @db.Timestamptz(6)
  updatedAt   DateTime? @db.Timestamptz(6)
  userID      String    @db.Uuid
  text        String?
  iconName    String?   @db.VarChar(255)
  deletedAt   DateTime? @db.Timestamptz(6)
  redirectUrl String?
  Users       Users     @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model File {
  id                          String                        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                   DateTime?                     @db.Timestamptz(6)
  updatedAt                   DateTime?                     @db.Timestamptz(6)
  type                        String                        @db.VarChar(255)
  binaryContent               Bytes?
  fileName                    String                        @db.VarChar(255)
  mimeType                    String                        @db.VarChar(255)
  s3FileUrl                   String?                       @db.VarChar(255)
  OvertakeProtocol            OvertakeProtocol[]
  OvertakeProtocolMeter       OvertakeProtocolMeter[]
  OvertakeProtocolParticipant OvertakeProtocolParticipant[]
  SettlementBuyer             SettlementBuyer[]
  SettlementSeller            SettlementSeller[]
}

model LeadAudit {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID    String    @db.Uuid
  data      Json?
  response  Json?
  Users     Users     @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model LipscoreAudit {
  id            String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt     DateTime? @db.Timestamptz(6)
  updatedAt     DateTime? @db.Timestamptz(6)
  targetEmail   String    @db.VarChar(255)
  brokerId      String    @db.VarChar(255)
  estateVitecId String    @db.VarChar(255)
  data          Json?
  response      Json?
  isSuccessful  Boolean?
}

model LipscoreBrokerRating {
  id                    String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt             DateTime? @db.Timestamptz(6)
  updatedAt             DateTime? @db.Timestamptz(6)
  lipscoreId            Int?
  vitecBrokerEmployeeId String?   @db.VarChar(255)
  averageRating         Float?
  reviewCount           Int?
  reviews               Json?
  ratingCount           Int?
  ratings               Json?
}

model MailAudit {
  id        String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime?        @db.Timestamptz(6)
  updatedAt DateTime?        @db.Timestamptz(6)
  type      mail_audit_type?
  from      String?          @db.VarChar(255)
  to        String?          @db.VarChar(255)
  data      Json?
}

model Offices {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  name      String?
  zip       String?
}

model OvertakeProtocol {
  id                                String                                             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                         DateTime?                                          @db.Timestamptz(6)
  updatedAt                         DateTime?                                          @db.Timestamptz(6)
  estateVitecId                     String?                                            @db.VarChar(255)
  moneyTransferred                  Boolean?
  sellerNewAddress                  String?
  sellerNewPostcode                 String?                                            @db.VarChar(255)
  sellerNewCity                     String?                                            @db.VarChar(255)
  propertyCleaned                   Boolean?
  propertyCleanedComment            String?
  sellerPaidCosts                   Boolean?
  sellerPaidCostsComment            String?
  handedOverAllKeys                 Boolean?
  handedOverAllKeysComment          String?
  numberOfKeys                      enum_OvertakeProtocol_numberOfKeys?
  smokeAlarmAvailable               Boolean?
  fireExtinguisherAvailable         Boolean?
  fireSafetyComment                 String?
  signingStarted                    DateTime?                                          @db.Timestamptz(6)
  signingFinished                   DateTime?                                          @db.Timestamptz(6)
  idfyDocumentId                    String?
  waterInfoProvided                 Boolean?
  electricityInfoProvided           Boolean?
  electricityProviderSelected       enum_OvertakeProtocol_electricityProviderSelected?
  finalSettlement                   Boolean?
  finalSettlementComment            String?
  address                           String?
  postCode                          String?                                            @db.VarChar(255)
  city                              String?                                            @db.VarChar(255)
  billingBuyerContactId             String?                                            @db.VarChar(255)
  finalSettlementWithholding        Boolean?
  finalSettlementWithholdAmount     String?                                            @db.VarChar(255)
  finalSettlementWithholdReason     String?
  finalSettlementWithholdingComment String?
  locked                            Boolean                                            @default(false)
  handoverComment                   String?
  fileId                            String?                                            @db.Uuid
  File                              File?                                              @relation(fields: [fileId], references: [id], onUpdate: NoAction)
  OvertakeProtocolMeter             OvertakeProtocolMeter[]
  OvertakeProtocolParticipant       OvertakeProtocolParticipant[]
}

model OvertakeProtocolMeter {
  id                 String                           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt          DateTime?                        @db.Timestamptz(6)
  updatedAt          DateTime?                        @db.Timestamptz(6)
  overtakeProtocolId String                           @db.Uuid
  type               enum_OvertakeProtocolMeter_type?
  meterReading       String?                          @db.VarChar(255)
  meterNumber        String?                          @db.VarChar(255)
  fileId             String?                          @db.Uuid
  meterName          String?                          @db.VarChar(255)
  File               File?                            @relation(fields: [fileId], references: [id], onUpdate: NoAction)
  OvertakeProtocol   OvertakeProtocol                 @relation(fields: [overtakeProtocolId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model OvertakeProtocolParticipant {
  id                 String                                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt          DateTime?                                   @db.Timestamptz(6)
  updatedAt          DateTime?                                   @db.Timestamptz(6)
  overtakeProtocolId String                                      @db.Uuid
  name               String?                                     @db.VarChar(255)
  email              String?                                     @db.VarChar(255)
  phoneNumber        String?                                     @db.VarChar(255)
  isPowerOfAttorney  Boolean?
  belongsTo          enum_OvertakeProtocolParticipant_belongsTo?
  fileId             String?                                     @db.Uuid
  File               File?                                       @relation(fields: [fileId], references: [id], onUpdate: NoAction)
  OvertakeProtocol   OvertakeProtocol                            @relation(fields: [overtakeProtocolId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model PEPForm {
  id                                  String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                           DateTime?            @db.Timestamptz(6)
  updatedAt                           DateTime?            @db.Timestamptz(6)
  estateVitecId                       String               @db.VarChar(255)
  isNotificationSent                  Boolean?
  type                                String?              @db.VarChar(255)
  saleInfoTransactionReason           String?
  saleInfoInMyNameOrProxy             String?
  saleInfoUseOrInvestment             String?
  propertyInfoOwnedForTime            String?
  propertyInfoIsRenovatedByOwner      Boolean?
  propertyInfoRenovator               String?
  propertyInfoRenovationFinance       String?
  propertyInfoRenovationDocumentation String?
  equityInfoPercent                   String?
  equityInfoSource                    String?
  idfyDocumentId                      String?              @db.VarChar(255)
  signingStarted                      DateTime?            @db.Timestamptz(6)
  signingFinished                     DateTime?            @db.Timestamptz(6)
  financing                           Json?                @default("{\"newHome\": null, \"doYouWantFinancing\": null, \"participantsSelected\": null}")
  locked                              Boolean              @default(false)
  estateAssignmentTypeGroup           Int?
  valuationPurpose                    String?              @db.VarChar(255)
  valuationDescriptionOfOther         String?
  valuationSincePurchase              Boolean?
  valuationYear                       Int?
  fileId                              String?              @db.Uuid
  PEPFormParticipant                  PEPFormParticipant[]
}

model PEPFormParticipant {
  id                                       String                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                                DateTime?                   @db.Timestamptz(6)
  updatedAt                                DateTime?                   @db.Timestamptz(6)
  contactPersonVitecId                     String                      @db.VarChar(255)
  name                                     String?                     @db.VarChar(255)
  email                                    String?                     @db.VarChar(255)
  phoneNumber                              String?                     @db.VarChar(255)
  profession                               String?                     @db.VarChar(255)
  employer                                 String?                     @db.VarChar(255)
  selectedCountry                          String?                     @db.VarChar(255)
  typedCountry                             String?                     @db.VarChar(255)
  estateCount                              String?                     @db.VarChar(255)
  citizenshipDescription                   Json?
  ownPepType                               String?                     @db.VarChar(255)
  ownDescriptionsOfSelected                Json?                       @default("{\"countries\": null, \"description\": null, \"dateWhenPracticed\": null, \"isLessThanAYearAgo\": null}")
  ownComment                               String?
  employerPepType                          String?                     @db.VarChar(255)
  employerDescriptionsOfSelected           Json?                       @default("{\"name\": null, \"countries\": null, \"description\": null, \"relationship\": null, \"dateWhenPracticed\": null, \"isLessThanAYearAgo\": null}")
  employerComment                          String?
  familyPepType                            String?                     @db.VarChar(255)
  familyDescriptionsOfSelected             Json?                       @default("{\"name\": null, \"countries\": null, \"description\": null, \"relationship\": null, \"dateWhenPracticed\": null, \"isLessThanAYearAgo\": null}")
  familyComment                            String?
  pepFormId                                String?                     @db.Uuid
  settlementBuyerParticipantId             String?                     @db.Uuid
  settlementBuyerFormIdForAccountManagers  String?                     @db.Uuid
  settlementSellerFormIdForAccountManagers String?                     @db.Uuid
  PEPForm                                  PEPForm?                    @relation(fields: [pepFormId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  SettlementBuyer                          SettlementBuyer?            @relation(fields: [settlementBuyerFormIdForAccountManagers], references: [id], onDelete: Cascade, onUpdate: NoAction)
  SettlementBuyerParticipant               SettlementBuyerParticipant? @relation(fields: [settlementBuyerParticipantId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  SettlementSeller                         SettlementSeller?           @relation(fields: [settlementSellerFormIdForAccountManagers], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model Popups {
  id        String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime?        @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime?        @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID    String           @db.Uuid
  type      popup_type_enum  @default(referral)
  state     popup_state_enum @default(firstShow)
  Users     Users            @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model PriceGuessingEstates {
  id                       String                     @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                DateTime?                  @db.Timestamptz(6)
  updatedAt                DateTime?                  @db.Timestamptz(6)
  estateVitecId            String?                    @db.VarChar(255)
  dateOfGuessing           DateTime?                  @db.Timestamptz(6)
  priceSnapshot            Int?
  PriceGuessingUserGuesses PriceGuessingUserGuesses[]
}

model PriceGuessingUserGuesses {
  id                    String               @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt             DateTime?            @db.Timestamptz(6)
  updatedAt             DateTime?            @db.Timestamptz(6)
  userId                String               @db.Uuid
  priceGuessingEstateId String               @db.Uuid
  guessedPrice          Int?
  PriceGuessingEstates  PriceGuessingEstates @relation(fields: [priceGuessingEstateId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  Users                 Users                @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model ReferralCodes {
  id              String            @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt       DateTime?         @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt       DateTime?         @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  code            String            @unique @db.VarChar(255)
  free            Boolean?          @default(true)
  comment         String?
  ReferralInvites ReferralInvites[]
}

model ReferralInvites {
  id            String        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt     DateTime?     @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt     DateTime?     @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  referralID    String        @db.Uuid
  invitedUserID String        @db.Uuid
  Users         Users         @relation(fields: [invitedUserID], references: [id], onDelete: Cascade)
  ReferralCodes ReferralCodes @relation(fields: [referralID], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model RemovedUsers {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-09-24 15:20:06.951+00'::timestamp with time zone")) @db.Timestamptz(6)
  email     String
  reason    String
}

model RewardTypes {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @db.Timestamptz(6)
  updatedAt DateTime? @db.Timestamptz(6)
  type      String
  name      String    @db.VarChar(255)
  Rewards   Rewards[]
}

model Rewards {
  id             String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt      DateTime?   @db.Timestamptz(6)
  updatedAt      DateTime?   @db.Timestamptz(6)
  referralCodeID String?     @db.Uuid
  rewardTypeID   String      @db.Uuid
  isRedeemed     Boolean     @default(false)
  Coupons        Coupons[]
  RewardTypes    RewardTypes @relation(fields: [rewardTypeID], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model SequelizeMeta {
  name String @id @db.VarChar(255)
}

model SettlementBuyer {
  id                                String                       @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                         DateTime?                    @db.Timestamptz(6)
  updatedAt                         DateTime?                    @db.Timestamptz(6)
  estateVitecId                     String                       @db.VarChar(255)
  hasEquity                         Boolean?
  financeAccountNumber              String?                      @db.VarChar(255)
  financedWithLoan                  String?                      @db.VarChar(255)
  idfyDocumentId                    String?
  signingStarted                    DateTime?                    @db.Timestamptz(6)
  signingFinished                   DateTime?                    @db.Timestamptz(6)
  estateAssignmentNumber            String?                      @db.VarChar(255)
  estateAddress                     String?
  fileId                            String?                      @db.Uuid
  hasFinanceAccountManagers         Boolean?
  financeAccountManagers            Json?
  isNotificationSent                Boolean?                     @default(false)
  financeAccountOwnerName           String?                      @db.VarChar(255)
  equityAccountNumber               String?                      @db.VarChar(255)
  equityAccountHolderName           String?                      @db.VarChar(255)
  hasEquityAccountManagers          Boolean?
  equityAccountManagers             Json?
  isFinanceAndEquityAccountsTheSame Boolean?
  leads                             Json?
  saleInfoTransactionReason         String?                      @db.VarChar(255)
  saleInfoInMyNameOrProxy           String?                      @db.VarChar(255)
  saleInfoUseOrInvestment           String?                      @db.VarChar(255)
  estateBaseType                    Int?                         @db.SmallInt
  locked                            Boolean                      @default(false)
  PEPFormParticipant                PEPFormParticipant[]
  File                              File?                        @relation(fields: [fileId], references: [id], onUpdate: NoAction)
  SettlementBuyerLoan               SettlementBuyerLoan[]
  SettlementBuyerParticipant        SettlementBuyerParticipant[]
}

model SettlementBuyerEquity {
  id                           String                     @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                    DateTime?                  @db.Timestamptz(6)
  updatedAt                    DateTime?                  @db.Timestamptz(6)
  settlementBuyerParticipantId String                     @db.Uuid
  amountOfEquity               String?                    @db.VarChar(255)
  bankOfEquity                 String?                    @db.VarChar(255)
  originOfEquity               String?                    @db.VarChar(255)
  originOfEquityComment        String?
  SettlementBuyerParticipant   SettlementBuyerParticipant @relation(fields: [settlementBuyerParticipantId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SettlementBuyerLoan {
  id                String          @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt         DateTime?       @db.Timestamptz(6)
  updatedAt         DateTime?       @db.Timestamptz(6)
  settlementBuyerId String          @db.Uuid
  loanBank          String?
  bankContactName   String?
  bankContactPhone  String?
  bankContactEmail  String?
  loanTaker         String?
  SettlementBuyer   SettlementBuyer @relation(fields: [settlementBuyerId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SettlementBuyerParticipant {
  id                    String                  @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt             DateTime?               @db.Timestamptz(6)
  updatedAt             DateTime?               @db.Timestamptz(6)
  settlementBuyerId     String                  @db.Uuid
  name                  String?                 @db.VarChar(255)
  email                 String?                 @db.VarChar(255)
  phoneNumber           String?                 @db.VarChar(255)
  hasEquity             Boolean?
  PEPFormParticipant    PEPFormParticipant[]
  SettlementBuyerEquity SettlementBuyerEquity[]
  SettlementBuyer       SettlementBuyer         @relation(fields: [settlementBuyerId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SettlementSeller {
  id                          String                        @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                   DateTime?                     @db.Timestamptz(6)
  updatedAt                   DateTime?                     @db.Timestamptz(6)
  estateVitecId               String                        @db.VarChar(255)
  isMortgaged                 Boolean?
  idfyDocumentId              String?
  signingStarted              DateTime?                     @db.Timestamptz(6)
  signingFinished             DateTime?                     @db.Timestamptz(6)
  estateAssignmentNumber      String?                       @db.VarChar(255)
  estateAddress               String?
  fileId                      String?                       @db.Uuid
  isNotificationSent          Boolean?                      @default(false)
  accountsComment             String?
  leads                       Json?
  locked                      Boolean                       @default(false)
  PEPFormParticipant          PEPFormParticipant[]
  File                        File?                         @relation(fields: [fileId], references: [id], onUpdate: NoAction)
  SettlementSellerAccount     SettlementSellerAccount[]
  SettlementSellerLoan        SettlementSellerLoan[]
  SettlementSellerParticipant SettlementSellerParticipant[]
}

model SettlementSellerAccount {
  id                        String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                 DateTime?        @db.Timestamptz(6)
  updatedAt                 DateTime?        @db.Timestamptz(6)
  settlementSellerId        String           @db.Uuid
  hasFinanceAccountManagers Boolean?
  accountNumber             String?          @db.VarChar(255)
  accountOwnerName          String?          @db.VarChar(255)
  distributionInPercentage  Int?
  isAccountShared           Boolean?
  financeAccountManagers    Json?
  SettlementSeller          SettlementSeller @relation(fields: [settlementSellerId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SettlementSellerLoan {
  id                         String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                  DateTime?        @db.Timestamptz(6)
  updatedAt                  DateTime?        @db.Timestamptz(6)
  settlementSellerId         String           @db.Uuid
  loanBank                   String?
  loanTaker                  String?
  residualDebt               String?
  bankContactName            String?
  bankContactPhone           String?
  bankContactEmail           String?
  shouldMortgageLoanBeRepaid Boolean?
  loanIdNumber               String?          @db.VarChar(255)
  SettlementSeller           SettlementSeller @relation(fields: [settlementSellerId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SettlementSellerParticipant {
  id                 String           @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt          DateTime?        @db.Timestamptz(6)
  updatedAt          DateTime?        @db.Timestamptz(6)
  settlementSellerId String           @db.Uuid
  name               String?          @db.VarChar(255)
  email              String?          @db.VarChar(255)
  phoneNumber        String?          @db.VarChar(255)
  SettlementSeller   SettlementSeller @relation(fields: [settlementSellerId], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model SmsAudit {
  id          String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt   DateTime? @db.Timestamptz(6)
  updatedAt   DateTime? @db.Timestamptz(6)
  type        String?
  phoneNumber String?   @db.VarChar(255)
  data        Json?
  response    Json?
}

model StorebrandAudit {
  id                  String                 @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt           DateTime?              @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt           DateTime?              @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  userOrParticipantID String?                @db.Uuid
  type                storebrand_audit_type?
  data                Json?
}

model Urls {
  id             String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt      DateTime? @db.Timestamptz(6)
  updatedAt      DateTime? @db.Timestamptz(6)
  shortUrl       String    @unique @db.VarChar(32)
  longUrl        String
  deletedAt      DateTime? @db.Timestamptz(6)
  firstClickedAt DateTime? @db.Timestamptz(6)
}

model UserActivity {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID    String?   @db.Uuid
  type      String?   @db.VarChar(255)
  data      Json?
  Users     Users?    @relation(fields: [userID], references: [id], onDelete: Cascade)

  @@index([userID], map: "user_activity_user_i_d")
}

model UserArea {
  id        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID    String?   @db.Uuid
  areaID    String?   @db.Uuid
  Areas     Areas?    @relation(fields: [areaID], references: [id], onDelete: NoAction, onUpdate: NoAction)
  Users     Users?    @relation(fields: [userID], references: [id], onDelete: Cascade)

  @@unique([userID, areaID], map: "user_area_user_i_d_area_i_d")
}

model UserFirebaseToken {
  id        String    @id(map: "UserIosToken_pkey") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt DateTime? @db.Timestamptz(6)
  updatedAt DateTime? @db.Timestamptz(6)
  userID    String?   @db.Uuid
  token     String?
  os        String?   @default("ios")
  Users     Users?    @relation(fields: [userID], references: [id], onDelete: Cascade, map: "UserIosToken_userID_fkey")

  @@unique([userID, token], map: "user_ios_token_user_i_d_token")
}

model UserOptions {
  id                 String                 @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt          DateTime?              @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt          DateTime?              @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  userID             String                 @db.Uuid
  type               Json?
  livingArea         Int?
  area               Json?
  price              Json?
  profile            Json?
  values             String[]               @default([])
  mortgage           mortgage_options_enum?
  contactPreferences String[]               @default([])
  contactHours       String[]               @default([])
  Users              Users                  @relation(fields: [userID], references: [id], onDelete: Cascade)
}

model Users {
  id                        String                      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt                 DateTime?                   @default(dbgenerated("'2020-06-25 13:06:51.511+00'::timestamp with time zone")) @db.Timestamptz(6)
  name                      String
  email                     String                      @unique
  phoneNumber               String?
  password                  String?
  bankIdVerified            Boolean?
  birthday                  String?                     @db.VarChar(255)
  passwordCode              String                      @default(dbgenerated("uuid_generate_v4()"))
  verifyPasswordCode        String?
  annualIncome              Int?
  sumOfOtherLoans           Int?
  referralCode              String                      @default("") @db.VarChar(255)
  visitedChecklist          Boolean?
  visitedSalesProcess       Boolean?
  registeredWith            register_type_enum          @default(BANKID)
  closedTutorialAt          DateTime?                   @db.Timestamptz(6)
  pushNotificationSettings  Json?                       @default("{\"quiz\": true, \"journey\": true, \"prospect\": true, \"propertyValue\": true}")
  consentSettings           Json?                       @default("{\"financing\": false, \"searchProfile\": false, \"emailMarketing\": false}")
  EiendomsverdiAudit        EiendomsverdiAudit[]
  EstateChecklistConnection EstateChecklistConnection[]
  EstateEventConnection     EstateEventConnection[]
  EstateImagesFromUsers     EstateImagesFromUsers[]
  Estates                   Estates[]
  Estates_audit             Estates_audit[]
  Favorites                 Favorites[]
  Feeds                     Feeds[]
  LeadAudit                 LeadAudit[]
  Popups                    Popups[]
  PriceGuessingUserGuesses  PriceGuessingUserGuesses[]
  ReferralInvites           ReferralInvites[]
  UserActivity              UserActivity[]
  UserArea                  UserArea[]
  UserFirebaseToken         UserFirebaseToken[]
  UserOptions               UserOptions[]
}

model VitecEstateExtensions {
  id                   String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  createdAt            DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  updatedAt            DateTime? @default(dbgenerated("'2020-10-20 19:10:45.44+00'::timestamp with time zone")) @db.Timestamptz(6)
  vitecID              String    @db.VarChar(255)
  loanAmount           Int?
  interestRate         Float?
  originalMortgage     Int?
  mapImageUrl          String?   @db.VarChar(255)
  triedToDownloadImage Boolean   @default(false)
  mortgageYears        Int?
}

model commission_cache {
  cache_id          Int       @id @default(autoincrement())
  cache_date        DateTime  @db.Date
  employee_id       String    @db.VarChar(255)
  department        String    @db.VarChar(255)
  commission_amount Decimal   @db.Decimal(10, 2)
  signed_count      Int?      @default(0)
  sold_count        Int?      @default(0)
  last_updated      DateTime? @default(now()) @db.Timestamptz(6)

  @@unique([cache_date, employee_id])
  @@index([cache_date, employee_id, commission_amount], map: "idx_commission_cache_date_employee_amount")
}

model estate_matrix_updates_log {
  log_id           Int       @id @default(autoincrement())
  estate_id        Int
  old_matrix       Json?
  new_matrix       Json
  update_timestamp DateTime? @default(now()) @db.Timestamptz(6)
  pg_estate_id     String?   @db.Uuid
}

model magic_link_users {
  name                       String?                    @db.VarChar(255)
  email                      String                     @db.VarChar(255)
  emailVerified              DateTime?                  @db.Timestamptz(6)
  image                      String?
  role                       String?                    @default("user") @db.VarChar(255)
  id                         String                     @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  employeeId                 String?                    @unique @db.VarChar(255)
  sessions                   sessions[]
  authenticator              authenticator[]
  account                    account[]
  broker_partner             broker_partner[]
  BrokerProfileLinks         broker_profile_links[]
  pinned_broker              pinned_broker[]
  createdInspectionFolders   inspection_folders[]       @relation("CreatedByRelation")
  updatedInspectionFolders   inspection_folders[]       @relation("UpdatedByRelation")
  publishedInspectionFolders inspection_folders[]       @relation("PublishedByRelation")
  sentInspectionFolders      inspection_folders[]       @relation("SentByRelation")
  inspection_folders_audit   inspection_folders_audit[]

  @@index([employeeId])
}

model authenticator {
  id                   String  @id @default(cuid())
  credentialID         String  @unique
  userId               String  @db.Uuid
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user magic_link_users @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model account {
  userId            String  @db.Uuid
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user magic_link_users @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model sessions {
  id                   Int              @id @default(autoincrement())
  expires              DateTime         @db.Timestamptz(6)
  sessionToken         String           @db.VarChar(255)
  isimpersonated       Boolean?         @default(false)
  userId               String           @db.Uuid
  impersonatedbyuserid String?          @db.Uuid
  user                 magic_link_users @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sessions_impersonatedbyuserid_fkey")
}

model verification_token {
  identifier String
  expires    DateTime @db.Timestamptz(6)
  token      String

  @@id([identifier, token])
}

model listing_agreements {
  id                     String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id              String    @unique
  fee_percentage         Decimal?  @db.Decimal(32, 4)
  seller_insurance       Boolean?
  general_terms          String?
  created_at             DateTime  @default(now()) @db.Timestamptz(6)
  updated_at             DateTime  @default(now()) @db.Timestamptz(6)
  deleted_at             DateTime? @db.Timestamptz(6)
  marketing_package      String?
  signicat_document_id   String?   @db.VarChar
  commission             Int?
  initial_commission     Int?
  initial_fee_percentage Decimal?  @db.Decimal(32, 4)
  no_previous_brokers    Boolean?

  owner_is_seller Boolean?

  waive_withdrawal_right Boolean?

  share_info_internally Boolean?

  previous_brokers String?

  receive_loan_offer   Boolean?
  recipient_loan_offer String?
  loan_offer_comment   Json?

  married_or_in_partnership Boolean?

  budget_posts        budget_posts[]
  offer_access_tokens offer_access_tokens[]

  is_common_estate Boolean?
  suggested_price  BigInt?

  initiated_signing_at   DateTime? @db.Timestamptz(6)
  signing_finished_at    DateTime? @db.Timestamptz(6)
  s3_signed_document_key String?

  signers signicat_signer[] @relation("DocumentSigners")

  deadline_for_signing DateTime? @db.Timestamptz(6)

  separate_provision             String?                          @db.Text
  listing_agreement_interactions listing_agreement_interactions[]
  company_sign_rights            company_sign_rights[]
  inspection_folders             inspection_folders?

  seller_is_shareholder     Boolean?
  listing_agreement_history listing_agreement_history[]

  is_valuation Boolean?

  @@index([estate_id], map: "idx_listing_agreements_estate_id")
  @@index([signicat_document_id], map: "idx_listing_agreements_signicat_document_id")
}

model inspection_folders {
  id                       String                     @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id                String                     @unique
  created_at               DateTime                   @default(now()) @db.Timestamptz(6)
  created_by_id            String                     @db.Uuid
  created_by               magic_link_users           @relation("CreatedByRelation", fields: [created_by_id], references: [id])
  updated_by               magic_link_users           @relation("UpdatedByRelation", fields: [updated_by_id], references: [id])
  updated_at               DateTime                   @default(now()) @db.Timestamptz(6)
  updated_by_id            String                     @db.Uuid
  deleted_at               DateTime?                  @db.Timestamptz(6)
  title                    String?
  published_at             DateTime?                  @db.Timestamptz(6)
  published_by_id          String?                    @db.Uuid
  published_by             magic_link_users?          @relation("PublishedByRelation", fields: [published_by_id], references: [id])
  sent_at                  DateTime?                  @db.Timestamptz(6)
  sent_by_id               String?                    @db.Uuid
  sent_by                  magic_link_users?          @relation("SentByRelation", fields: [sent_by_id], references: [id])
  listing_agreement_active Boolean                    @default(false)
  listing_agreement        listing_agreements?        @relation(fields: [estate_id], references: [estate_id])
  excluded_partners        broker_partner[]           @relation("InspectionPartners")
  excluded_employees       String[]
  relevant_links           String[]
  inspection_folders_audit inspection_folders_audit[]
  version                  Int?
  notes                    String?

  inspection_event    inspection_event[]
  offer_access_tokens offer_access_tokens[]
  inspection_leads    inspection_leads[]

  @@index([estate_id])
}

model inspection_folders_audit {
  id                       Int                 @id @default(autoincrement())
  estate_id                String
  inspection_folders       inspection_folders? @relation(fields: [estate_id], references: [estate_id])
  sent_at                  DateTime            @default(now())
  sent_by_id               String              @db.Uuid
  sent_by                  magic_link_users    @relation(fields: [sent_by_id], references: [id])
  listing_agreement_active Boolean
  recipient_contact_ids    String[]
  channels                 String[]
  extra_data               Json?

  @@index([estate_id])
}

model budget_posts {
  id                   String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  next_budget_post_id  BigInt             @unique
  listing_agreement_id String             @db.Uuid
  description          String?
  initial_price        Decimal?           @db.Decimal(32, 4)
  created_at           DateTime           @default(now()) @db.Timestamptz(6)
  updated_at           DateTime           @default(now()) @db.Timestamptz(6)
  deleted_at           DateTime?          @db.Timestamptz(6)
  listing_agreements   listing_agreements @relation(fields: [listing_agreement_id], references: [id], onDelete: Cascade)
}

model offer_access_tokens {
  id                   String              @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id            String
  listing_agreement_id String?             @db.Uuid
  inspection_folder_id String?             @db.Uuid
  token                String              @unique @default(dbgenerated("uuid_generate_v4()"))
  valid                Boolean             @default(true)
  created_at           DateTime            @default(now()) @db.Timestamptz(6)
  deleted_at           DateTime?           @db.Timestamptz(6)
  listing_agreements   listing_agreements? @relation(fields: [listing_agreement_id], references: [id], onDelete: Cascade)
  inspection_folders   inspection_folders? @relation(fields: [inspection_folder_id], references: [id], onDelete: Cascade)
}

model access_token_audit {
  id               String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  created_at       DateTime @default(now()) @db.Timestamptz(6)
  token            String
  contact_id       String?
  extra_contact_id String?
  source           String?
}

model news_readers {
  id          String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  employee_id String    @db.Uuid
  article_id  String
  created_at  DateTime  @default(now()) @db.Timestamptz(6)
  updated_at  DateTime  @default(now()) @db.Timestamptz(6)
  deleted_at  DateTime? @db.Timestamptz(6)

  @@unique([employee_id, article_id])
}

model signicat_signer {
  id                    String             @id @default(uuid())
  url                   String?
  signed_at             DateTime?          @db.Timestamptz(6)
  external_signer_id    String
  listing_agreement     listing_agreements @relation("DocumentSigners", fields: [listing_agreements_id], references: [id], onDelete: Cascade)
  listing_agreements_id String             @db.Uuid
  title                 String?
  email                 String
  phone                 String?
  first_name            String?
  last_name             String?
}

model user_flag {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  flagKey   String
  flagValue Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, flagKey])
  @@index([userId, flagKey])
}

model listing_agreement_interactions {
  id                    Int                @id @default(autoincrement())
  listing_agreements_id String             @db.Uuid
  listing_agreement     listing_agreements @relation(fields: [listing_agreements_id], references: [id], onDelete: Cascade)
  seller_id             String?
  employee_id           String?
  event_type            String
  event_timestamp       DateTime           @db.Timestamptz(6)
  created_at            DateTime           @default(now())
  extra_data            Json?

  @@unique([listing_agreements_id, seller_id, event_timestamp])
}

model company_sign_rights {
  id                   String             @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  company_contact_id   String             @db.Uuid
  listing_agreement_id String             @db.Uuid
  signers              String[]
  created_at           DateTime           @default(now()) @db.Timestamptz(6)
  updated_at           DateTime           @default(now()) @db.Timestamptz(6)
  deleted_at           DateTime?          @db.Timestamptz(6)
  listing_agreements   listing_agreements @relation(fields: [listing_agreement_id], references: [id], onDelete: Cascade)

  @@unique([company_contact_id, listing_agreement_id])
}

model broker_partner {
  id              String               @id @default(uuid())
  name            String
  instagram       String?              @db.VarChar(255)
  website         String?              @db.VarChar(255)
  description     String?
  category        String?
  images          String[]
  created_at      DateTime             @default(now())
  updated_at      DateTime             @updatedAt
  userId          String               @db.Uuid
  user            magic_link_users     @relation(fields: [userId], references: [id], onDelete: Cascade)
  profile_picture String?
  hidden          Boolean              @default(false)
  sequence        Int                  @default(0)
  inspections     inspection_folders[] @relation("InspectionPartners")
}

model pinned_broker {
  id         String           @id @default(cuid())
  userId     String           @db.Uuid
  user       magic_link_users @relation(fields: [userId], references: [id], onDelete: Cascade)
  employeeId String
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  @@index([userId])
  @@index([userId, employeeId])
}

model page_visit {
  id             Int       @id @default(autoincrement())
  contact_id     String?
  employee_id    String?
  page_id        String
  start_time     DateTime  @default(now())
  last_heartbeat DateTime  @default(now())
  end_time       DateTime?
  source         String?
  user_agent     Json?
  geolocation    Json?
  estate_id      String

  @@index([estate_id])
}

model broker_profile_links {
  id          Int              @id @default(autoincrement())
  media_links String[]
  ad_links    String[]
  userId      String           @unique @db.Uuid
  user        magic_link_users @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model inspection_event {
  id                 String              @id @default(uuid())
  estate_id          String?
  inspection_folders inspection_folders? @relation(fields: [estate_id], references: [estate_id], onDelete: Cascade)
  event_type         String
  created_at         DateTime            @default(now())
  updated_at         DateTime            @default(now())
  deleted_at         DateTime?           @db.Timestamptz(6)
  start              DateTime            @db.Timestamptz(6)
  end                DateTime?           @db.Timestamptz(6)
  title              String?

  @@index([estate_id])
}

model listing_agreement_history {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id  String
  employeeId String?
  contact_id String?
  logged_in  Boolean  @default(false)
  changes    Json?
  createdAt  DateTime @default(now()) @db.Timestamptz(6)

  listing_agreement listing_agreements @relation(fields: [estate_id], references: [estate_id])

  @@index([estate_id])
}

model vitec_estate_link {
  id        String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id String
  category  String
  link      String
  created   DateTime @default(now())

  @@unique([estate_id, category], map: "idx_vitec_estate_link_estate_id_category_unique")
}

model inspection_leads {
  id                     String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  lead_type              String
  source                 String?
  estate_id              String
  contact_id             String
  created_at             DateTime @default(now())
  created_by_contact_id  String?
  created_by_employee_id String?
  updated_at             DateTime @updatedAt
  success                Boolean  @default(false)
  vitec_tip_id           String?
  comment                String?

  inspection_folders inspection_folders? @relation(fields: [estate_id], references: [estate_id])

  @@index([estate_id])
}

model estate_extra_contact {
  id                    String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id             String
  contact_id            String
  contact_relation_name String
  deleted_at            DateTime? @db.Timestamptz(6)
  created_at            DateTime  @default(now())
}

model etakst_check_queue {
  id               String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id        String    @unique
  created_at       DateTime  @default(now()) @db.Timestamptz(6)
  last_checked_at  DateTime? @db.Timestamptz(6)
  check_count      Int       @default(0)
  is_complete      Boolean   @default(false)
  completed_at     DateTime? @db.Timestamptz(6)
  document_id      String?
  reminder_count   Int       @default(0)
  last_reminder_at DateTime? @db.Timestamptz(6)
  s3_document_key  String?
}

model etakst_audit {
  id                String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  estate_id         String
  document_id       String?
  employee_id       String?
  seller_id         String?
  created_at        DateTime @default(now()) @db.Timestamptz(6)
  updated_at        DateTime @default(now()) @db.Timestamptz(6)
  notification_type String?  @db.VarChar(255)
  is_successful     Boolean  @default(false)
  data              Json?
  response          Json?

  @@index([estate_id])
}

model estate_etakst {
  id                 String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  created_at         DateTime  @default(now()) @db.Timestamptz(6)
  updated_at         DateTime  @default(now()) @db.Timestamptz(6)
  estate_id          String    @db.VarChar(50)
  reference_number   String    @db.VarChar(10)
  doc_created_date   DateTime  @db.Timestamptz(6)
  doc_activated_date DateTime? @db.Timestamptz(6)
  estate_agent_name  String?
  contract_name      String?
  total_price        Int
  common_debt        Int
  bra_i              Int?
  text_array         String[]
  s3_document_key    String?

  @@unique([reference_number], map: "idx_estate_etakst_reference_number_unique")
  @@index([estate_id])
}

enum broker_activity_type {
  signup
  login
  getStatistics
  getSellers
  getEstates
}

enum coupon_type {
  hmh_box
  kolonial_250
  kolonial_400
  kolonial_500
}

enum enum_OvertakeProtocolMeter_type {
  electricity
  water
}

enum enum_OvertakeProtocolParticipant_belongsTo {
  SELLER
  BUYER
}

enum enum_OvertakeProtocol_electricityProviderSelected {
  fortum
  norgesEnergy
  hafslund
  none
  hafslundFortum
  fjordkraft
  trondelagSpot
  trondelagTobbSpot
}

enum enum_OvertakeProtocol_numberOfKeys {
  one      @map("1")
  two      @map("2")
  three    @map("3")
  four     @map("4")
  fiveplus @map("5+")
}

enum enum_PEPForm_pepType {
  SELLER
  BUYER
}

enum estate_checklist_id_enum {
  appraiser
  contractSigning
  handover
  informationGathering
  photographer
  viewing
  buyerContracSigning
  takeover
  move_in              @map("move-in")
}

enum estate_checklist_type_enum {
  buyer
  seller
}

enum estate_event_id_enum {
  interiorGuidance
  photographer
  appraiser
  adLiveInCoreChannels
  viewing
  biddingRound
  contractSigning
  takeOver
}

enum estate_event_type_enum {
  user
  vitec
}

enum estate_type_enum {
  forSale
  owned
}

enum mail_audit_type {
  sendMail
  sendTemplate
  otp
  settlement
  pep
}

enum mortgage_options_enum {
  no
  yes
  preApproved
}

enum popup_state_enum {
  firstShow
  neverShow
  showAfterDay
  showAfterWeek
}

enum popup_type_enum {
  referral
  first_friend_referred
  third_friend_referred
  hmh_box_sent_confirm
}

enum register_type_enum {
  BANKID
  PHONE_NUMBER
  VIPPS
}

enum reward_type {
  third_friend_referred
}

enum storebrand_audit_type {
  clientJwtCreate
  serverJwtExchange
  clientLeadCreate
  serverLeadSend
}

enum undefined {
  minister
  national_assembly_member
  government_member
  court_member
  national_audit_office_member
  ambassador_of_military_member
  state_enterprise_member
  international_boss
  none
}

enum user_preference_enum {
  now
  rightPrice
  withinSixMonths
  dont
}
