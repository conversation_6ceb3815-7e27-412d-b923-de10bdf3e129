-- CreateTable
CREATE TABLE "estate_etakst" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "reference_number" TEXT NOT NULL,
    "doc_created_date" TIMESTAMPTZ(6) NOT NULL,
    "doc_activated_date" TIMESTAMPTZ(6),
    "estate_agent_name" TEXT,
    "contract_name" TEXT,
    "total_price" INTEGER NOT NULL,
    "common_debt" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "estate_etakst_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "estate_etakst_estate_id_idx" ON "estate_etakst"("estate_id");

-- CreateIndex
CREATE UNIQUE INDEX "idx_estate_etakst_reference_number_unique" ON "estate_etakst"("reference_number");
