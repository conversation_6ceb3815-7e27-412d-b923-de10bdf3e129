-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "budget_posts" DROP CONSTRAINT "budget_posts_listing_agreement_id_fkey";

-- DropForeignKey
ALTER TABLE "offer_access_tokens" DROP CONSTRAINT "offer_access_tokens_listing_agreement_id_fkey";

-- DropFore<PERSON><PERSON>ey
ALTER TABLE "signicat_signer" DROP CONSTRAINT "signicat_signer_listing_agreements_id_fkey";

-- AddForeignKey
ALTER TABLE "budget_posts" ADD CONSTRAINT "budget_posts_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "offer_access_tokens" ADD CONSTRAINT "offer_access_tokens_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddFore<PERSON><PERSON>ey
ALTER TABLE "signicat_signer" ADD CONSTRAINT "signicat_signer_listing_agreements_id_fkey" FOREIGN KEY ("listing_agreements_id") REFERENCES "listing_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;
