-- AlterTable
ALTER TABLE "offer_access_tokens" ADD COLUMN     "inspection_folder_id" UUID,
ALTER COLUMN "listing_agreement_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "page_visit" (
    "id" SERIAL NOT NULL,
    "contact_id" TEXT,
    "employee_id" TEXT,
    "page_id" TEXT NOT NULL,
    "start_time" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_heartbeat" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_time" TIMESTAMP(3),
    "source" TEXT,
    "user_agent" JSONB,
    "geolocation" JSONB,
    "estate_id" TEXT NOT NULL,

    CONSTRAINT "page_visit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "page_visit_estate_id_idx" ON "page_visit"("estate_id");

-- AddForeignKey
ALTER TABLE "offer_access_tokens" ADD CONSTRAINT "offer_access_tokens_inspection_folder_id_fkey" FOREIGN KEY ("inspection_folder_id") REFERENCES "inspection_folders"("id") ON DELETE CASCADE ON UPDATE CASCADE;
