-- CreateTable
CREATE TABLE "inspection_folders" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID NOT NULL,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by_id" UUID NOT NULL,
    "deleted_at" TIMESTAMPTZ(6),
    "title" TEXT,
    "published_at" TIMESTAMPTZ(6),
    "published_by_id" UUID,
    "sent_at" TIMESTAMPTZ(6),
    "sent_by_id" UUID,
    "listing_agreement_active" BOOLEAN NOT NULL DEFAULT false,
    "relevant_links" TEXT [],
    CONSTRAINT "inspection_folders_pkey" PRIMARY KEY ("id")
);
-- CreateTable
CREATE TABLE "_InspectionPartners" ("A" TEXT NOT NULL, "B" UUID NOT NULL);
-- CreateIndex
CREATE UNIQUE INDEX "inspection_folders_estate_id_key" ON "inspection_folders"("estate_id");
-- CreateIndex
CREATE UNIQUE INDEX "_InspectionPartners_AB_unique" ON "_InspectionPartners"("A", "B");
-- CreateIndex
CREATE INDEX "_InspectionPartners_B_index" ON "_InspectionPartners"("B");
-- AddForeignKey
ALTER TABLE "inspection_folders"
ADD CONSTRAINT "inspection_folders_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "inspection_folders"
ADD CONSTRAINT "inspection_folders_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "inspection_folders"
ADD CONSTRAINT "inspection_folders_published_by_id_fkey" FOREIGN KEY ("published_by_id") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "inspection_folders"
ADD CONSTRAINT "inspection_folders_sent_by_id_fkey" FOREIGN KEY ("sent_by_id") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "inspection_folders"
ADD CONSTRAINT "inspection_folders_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "listing_agreements"("estate_id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "_InspectionPartners"
ADD CONSTRAINT "_InspectionPartners_A_fkey" FOREIGN KEY ("A") REFERENCES "broker_partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AddForeignKey
ALTER TABLE "_InspectionPartners"
ADD CONSTRAINT "_InspectionPartners_B_fkey" FOREIGN KEY ("B") REFERENCES "inspection_folders"("id") ON DELETE CASCADE ON UPDATE CASCADE;
-- AlterTable
ALTER TABLE "_InspectionPartners"
ADD CONSTRAINT "_InspectionPartners_AB_pkey" PRIMARY KEY ("A", "B");
-- DropIndex
DROP INDEX "_InspectionPartners_AB_unique";