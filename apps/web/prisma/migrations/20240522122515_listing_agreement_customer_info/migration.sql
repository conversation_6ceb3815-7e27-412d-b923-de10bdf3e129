-- CreateEnum
CREATE TYPE "seller_marital_status" AS ENUM ('marriedCommonHome', 'marriedSeparateHome', 'notMarried');

-- AlterTable
ALTER TABLE "estate_sellers" ADD COLUMN     "email" TEXT,
ADD COLUMN     "first_name" TEXT,
ADD COLUMN     "last_name" TEXT,
ADD COLUMN     "phone_number" TEXT;

-- AlterTable
ALTER TABLE "listing_agreements" ADD COLUMN     "marital_status" "seller_marital_status",
ADD COLUMN     "more_than_one_seller" BOOLEAN,
ADD COLUMN     "no_previous_brokers" BOOLEAN,
ADD COLUMN     "owner_is_seller" BOOLEAN,
ADD COLUMN     "previous_brokers" TEXT,
ADD COLUMN     "receive_loan_offer" BOOLEAN,
ADD COLUMN     "share_info_internally" BOOLEAN,
ADD COLUMN     "waive_withdrawal_right" BOOLEAN;
