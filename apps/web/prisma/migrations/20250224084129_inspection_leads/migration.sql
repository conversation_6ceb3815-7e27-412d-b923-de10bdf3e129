-- CreateTable
CREATE TABLE "inspection_leads" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "lead_type" TEXT NOT NULL,
    "source" TEXT,
    "estate_id" TEXT NOT NULL,
    "contact_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_contact_id" TEXT,
    "created_by_employee_id" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "success" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "inspection_leads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "inspection_leads_estate_id_idx" ON "inspection_leads"("estate_id");

-- AddForeignKey
ALTER TABLE "inspection_leads" ADD CONSTRAINT "inspection_leads_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "inspection_folders"("estate_id") ON DELETE RESTRICT ON UPDATE CASCADE;
