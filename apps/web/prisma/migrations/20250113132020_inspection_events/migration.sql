-- CreateTable
CREATE TABLE "inspection_event" (
    "id" TEXT NOT NULL,
    "estate_id" TEXT,
    "event_type" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),
    "start" TIMESTAMPTZ(6) NOT NULL,
    "end" TIMESTAMPTZ(6),
    "title" TEXT,
    CONSTRAINT "inspection_event_pkey" PRIMARY KEY ("id")
);
-- AddForeignKey
ALTER TABLE "inspection_event"
ADD CONSTRAINT "inspection_event_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "inspection_folders"("estate_id") ON DELETE CASCADE ON UPDATE CASCADE;