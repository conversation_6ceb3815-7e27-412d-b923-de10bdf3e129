-- CreateTable
CREATE TABLE "pinned_broker" (
    "id" TEXT NOT NULL,
    "userId" UUID NOT NULL,
    "employeeId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pinned_broker_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pinned_broker_userId_idx" ON "pinned_broker"("userId");

-- CreateIndex
CREATE INDEX "pinned_broker_userId_employeeId_idx" ON "pinned_broker"("userId", "employeeId");

-- AddForeignKey
ALTER TABLE "pinned_broker" ADD CONSTRAINT "pinned_broker_userId_fkey" FOREIGN KEY ("userId") REFERENCES "magic_link_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
