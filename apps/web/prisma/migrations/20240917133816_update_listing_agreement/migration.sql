-- AlterTable
ALTER TABLE "listing_agreements"
ADD COLUMN "deadline_for_signing" TIMESTAMPTZ(6);
-- CreateTable
CREATE TABLE "signicat_signer" (
    "id" TEXT NOT NULL,
    "url" TEXT,
    "signed_at" TIMESTAMPTZ(6),
    "external_signer_id" TEXT NOT NULL,
    "listing_agreements_id" UUID NOT NULL,
    "title" TEXT,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "first_name" TEXT,
    "last_name" TEXT,
    CONSTRAINT "signicat_signer_pkey" PRIMARY KEY ("id")
);
-- AddForeignKey
ALTER TABLE "signicat_signer"
ADD CONSTRAINT "signicat_signer_listing_agreements_id_fkey" FOREIGN KEY ("listing_agreements_id") REFERENCES "listing_agreements"("id") ON DELETE RESTRICT ON UPDATE CASCADE;