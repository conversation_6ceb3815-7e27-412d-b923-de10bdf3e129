/*
  Warnings:

  - You are about to drop the `estate_sellers` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "estate_sellers" DROP CONSTRAINT "estate_sellers_listing_agreement_id_fkey";

-- DropTable
DROP TABLE "estate_sellers";

-- CreateTable
CREATE TABLE "offer_access_tokens" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "listing_agreement_id" UUID NOT NULL,
    "token" TEXT NOT NULL DEFAULT uuid_generate_v4(),
    "valid" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ(6),

    CONSTRAINT "offer_access_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "offer_access_tokens_token_key" ON "offer_access_tokens"("token");

-- AddForeignKey
ALTER TABLE "offer_access_tokens" ADD CONSTRAINT "offer_access_tokens_listing_agreement_id_fkey" FOREIGN KEY ("listing_agreement_id") REFERENCES "listing_agreements"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
