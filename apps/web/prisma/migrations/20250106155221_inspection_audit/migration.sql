-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "inspection_folders" DROP CONSTRAINT "inspection_folders_created_by_id_fkey";

-- DropForeignKey
ALTER TABLE "inspection_folders" DROP CONSTRAINT "inspection_folders_estate_id_fkey";

-- DropForeignKey
ALTER TABLE "inspection_folders" DROP CONSTRAINT "inspection_folders_published_by_id_fkey";

-- DropForeignKey
ALTER TABLE "inspection_folders" DROP CONSTRAINT "inspection_folders_sent_by_id_fkey";

-- DropForeignKey
ALTER TABLE "inspection_folders" DROP CONSTRAINT "inspection_folders_updated_by_id_fkey";

-- CreateTable
CREATE TABLE "inspection_folders_audit" (
    "id" SERIAL NOT NULL,
    "estate_id" TEXT NOT NULL,
    "sent_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sent_by_id" UUID NOT NULL,
    "listing_agreement_active" BOOLEAN NOT NULL,
    "recipient_contact_ids" TEXT[],
    "channels" TEXT[],

    CONSTRAINT "inspection_folders_audit_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "inspection_folders" ADD CONSTRAINT "inspection_folders_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "magic_link_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders" ADD CONSTRAINT "inspection_folders_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "magic_link_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders" ADD CONSTRAINT "inspection_folders_published_by_id_fkey" FOREIGN KEY ("published_by_id") REFERENCES "magic_link_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders" ADD CONSTRAINT "inspection_folders_sent_by_id_fkey" FOREIGN KEY ("sent_by_id") REFERENCES "magic_link_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders" ADD CONSTRAINT "inspection_folders_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "listing_agreements"("estate_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders_audit" ADD CONSTRAINT "inspection_folders_audit_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "inspection_folders"("estate_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "inspection_folders_audit" ADD CONSTRAINT "inspection_folders_audit_sent_by_id_fkey" FOREIGN KEY ("sent_by_id") REFERENCES "magic_link_users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
