-- CreateTable
CREATE TABLE "listing_agreement_history" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "estate_id" TEXT NOT NULL,
    "employeeId" TEXT,
    "contact_id" TEXT,
    "logged_in" BOOLEAN NOT NULL DEFAULT false,
    "changes" JSONB,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "listing_agreement_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "listing_agreement_history_estate_id_idx" ON "listing_agreement_history"("estate_id");

-- CreateIndex
CREATE INDEX "inspection_event_estate_id_idx" ON "inspection_event"("estate_id");

-- CreateIndex
CREATE INDEX "inspection_folders_estate_id_idx" ON "inspection_folders"("estate_id");

-- CreateIndex
CREATE INDEX "inspection_folders_audit_estate_id_idx" ON "inspection_folders_audit"("estate_id");

-- AddF<PERSON>ignK<PERSON>
ALTER TABLE "listing_agreement_history" ADD CONSTRAINT "listing_agreement_history_estate_id_fkey" FOREIGN KEY ("estate_id") REFERENCES "listing_agreements"("estate_id") ON DELETE RESTRICT ON UPDATE CASCADE;
