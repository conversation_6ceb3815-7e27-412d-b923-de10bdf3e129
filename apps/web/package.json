{"name": "web", "version": "0.1.0", "private": true, "scripts": {"build": "npm run prisma:generate && next build", "build:analyze": "ANALYZE=true npm run build", "bundle:analyze": "npx @next/bundle-analyzer", "bundle:size": "npx bundlesize", "bundle:report": "node scripts/bundle-analysis.js", "check:format": "prettier --check src", "dev": "PORT=${PORT:=8040} next dev --turbopack", "check:type": "pnpm run prisma:generate && tsc --noEmit", "format": "prettier --write src", "build-storybook": "STORYBOOK_CI=true storybook build", "storybook": "storybook dev -p 8060 --no-version-updates --no-open --disable-telemetry", "storybook:snapshot": "npx chromatic --project-token=chpt_17fcfd5c3570ccb -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded", "storybook:snapshot:accept": "npx chromatic --project-token=chpt_17fcfd5c3570ccb -b build-storybook --only-changed --exit-zero-on-changes --exit-once-uploaded --auto-accept-changes", "storybook:skip": "npx chromatic --project-token=chpt_17fcfd5c3570ccb --skip", "prisma:generate": "dotenv -e .env -e .env.local -- prisma generate", "prisma:dbpull": "dotenv -e .env -e .env.local -- prisma db pull", "prisma:migrate": "dotenv -e .env -e .env.local -- prisma migrate dev", "lint": "eslint src --ext .ts,.tsx --max-warnings 0", "start": "next start"}, "dependencies": {"@ai-sdk/openai": "0.0.72", "@apollo/server": "5.0.0", "@as-integrations/next": "4.0.0", "@aws-sdk/client-lambda": "3.614.0", "@aws-sdk/client-s3": "3.614.0", "@aws-sdk/s3-request-presigner": "3.685.0", "@hookform/resolvers": "3.9.0", "@mailchimp/mailchimp_transactional": "1.0.59", "@nordvik/eslint-config": "workspace:*", "@nordvik/signicat-express-sdk": "workspace:*", "@nordvik/theme": "workspace:*", "@nordvik/ui": "workspace:*", "@nordvik/utils": "workspace:*", "@number-flow/react": "0.5.10", "@prisma/client": "6.5.0", "@radix-ui/react-collapsible": "1.1.11", "@sentry/nextjs": "8.55.0", "@sentry/node": "8.55.0", "@simplewebauthn/browser": "9.0.1", "@simplewebauthn/server": "9.0.3", "@tailwindcss/container-queries": "0.1.1", "@tanstack/react-query": "5.69.0", "@tsparticles/confetti": "3.8.1", "@vercel/analytics": "1.5.0", "@vercel/functions": "2.0.0", "@vercel/kv": "2.0.0", "ai": "3.4.33", "babel-plugin-react-compiler": "19.1.0-rc.2", "calendar-utils": "0.11.0", "cheerio": "1.0.0", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "dataloader": "2.2.3", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "dotenv-cli": "7.4.2", "emoji-regex": "10.4.0", "framer-motion": "12.4.7", "fuse.js": "7.1.0", "graphql": "16.11.0", "graphql-request": "7.2.0", "graphql-tag": "2.12.6", "input-otp": "1.4.2", "jsonwebtoken": "9.0.2", "lodash": "4.17.21", "lucide-react": "0.510.0", "marked": "15.0.7", "mongodb": "6.18.0", "next": "15.4.5", "next-auth": "5.0.0-beta.29", "nodemailer": "6.10.1", "norwegian-utils": "0.4.1", "nuqs": "2.4.3", "pdf-parse": "1.1.1", "pdfjs-dist": "5.2.133", "posthog-js": "1.248.1", "posthog-node": "4.18.0", "quill": "2.0.3", "raw-loader": "4.0.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.53.0", "react-pdf": "9.2.1", "react-use-intercom": "5.5.0", "react-virtuoso": "4.12.3", "recharts": "2.15.3", "reflect-metadata": "0.2.2", "server-only": "0.0.1", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "yup": "1.4.0", "zod": "3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "15.4.2", "@playwright/test": "1.51.1", "@savvywombat/tailwindcss-grid-areas": "4.0.0", "@storybook/addon-styling-webpack": "1.0.0", "@storybook/blocks": "8.2.8", "@storybook/manager-api": "8.2.8", "@storybook/nextjs": "8.2.8", "@storybook/react": "8.2.8", "@storybook/react-webpack5": "8.2.8", "@storybook/test": "8.2.8", "@storybook/test-runner": "0.22.0", "@storybook/testing-library": "0.2.2", "@storybook/theming": "8.2.8", "@tanstack/react-query-devtools": "5.75.5", "@types/lodash": "4.17.7", "@types/mailchimp__mailchimp_transactional": "1.0.10", "@types/node": "22.17.0", "@types/nodemailer": "6.4.15", "@types/pdf-parse": "1.1.5", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "10.4.21", "bundlesize": "0.18.2", "eslint": "8.57.0", "eslint-config-next": "15.4.2", "eslint-plugin-storybook": "0.8.0", "mockdate": "3.0.5", "postcss": "8.4.16", "prisma": "6.5.0", "storybook": "8.2.8", "tailwindcss": "3.4.4", "tsconfig-paths-webpack-plugin": "4.1.0", "typescript": "5.8.3", "webpack-bundle-analyzer": "4.10.2"}, "bundlesize": [{"path": ".next/static/js/**/*.js", "maxSize": "300kb", "compression": "gzip"}, {"path": ".next/static/css/**/*.css", "maxSize": "50kb", "compression": "gzip"}], "pnpm": {"overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}}