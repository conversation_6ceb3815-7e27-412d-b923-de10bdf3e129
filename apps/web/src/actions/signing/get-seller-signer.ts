import isEmpty from 'lodash/isEmpty'

import {
  AuthenticationMechanism,
  MobileCountryCode,
  NotificationSetting,
  RedirectMode,
  SignatureMechanism,
  SignatureMethod,
  SignatureType,
  type SignerOptions,
} from '@nordvik/signicat-express-sdk/types'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { isProd } from '@/lib/getBaseUrl'

import type { SigningSeller } from './signing-seller'
import { defaultUI } from './ui'

export interface SigningSellerProps {
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  seller: SigningSeller
  skipInitialNotification?: boolean
  redirect: string
  viewToken?: string | null
  authMechanism: AuthenticationMechanism
  signatureType: SignatureType
}

export default function getSellerSigner({
  estate,
  redirect,
  seller,
  viewToken,
  skipInitialNotification,
  authMechanism,
  signatureType,
}: SigningSellerProps): SignerOptions {
  if (!isProd) {
    seller.email = process.env.TEST_EMAIL?.split(';')[0] ?? ''
    seller.mobilePhone = process.env.TEST_PHONE?.split(';')[0] ?? ''
  }

  const hasEmail = !isEmpty(seller.email)
  const hasPhoneNr = !isEmpty(seller.mobilePhone)

  const hasBoth = hasEmail && hasPhoneNr

  const redirectUrl = new URL(redirect)
  redirectUrl.searchParams.set('cid', seller.contactId)

  if (viewToken) {
    redirectUrl.searchParams.set('viewToken', viewToken)
  }

  // Copy the redirect URL and change the pathname for the success URL
  const successUrl = new URL(redirectUrl.toString())
  successUrl.pathname = `${successUrl.pathname}/suksess`

  // Keep the same search parameters
  redirectUrl.searchParams.forEach((value, key) =>
    successUrl.searchParams.set(key, value),
  )

  const SSN = seller.socialSecurity?.replaceAll(' ', '')

  const config: SignerOptions = {
    externalSignerId: seller.contactId,
    signerInfo: {
      firstName: seller.firstName,
      lastName: seller.lastName,
      title: 'Selger',
      socialSecurityNumber: SSN,
    },
    authentication: {
      mechanism: authMechanism,
      socialSecurityNumber: SSN,
    },
    redirectSettings: {
      redirectMode: RedirectMode.Redirect,
      success: successUrl.toString(),
      cancel: `${redirectUrl.toString()}&signicatReason=cancel`,
      error: `${redirectUrl.toString()}&signicatReason=error`,
    },
    signatureType,
    ui: defaultUI,
    getSocialSecurityNumber: true,
    notifications: {
      setup: {
        canceled: NotificationSetting.Off,
        signatureReceipt: NotificationSetting.Off,
        request: NotificationSetting.SendEmail,
        reminder: NotificationSetting.Off,
        expired: NotificationSetting.Off,
        finalReceipt: NotificationSetting.Off,
      },
      mergeFields: {
        '{name}': seller.firstName,
        '{address}': estate.address?.streetAddress ?? 'ukjent',
      },
    },
  }

  if (hasEmail) {
    config.signerInfo!.email = seller.email
    config.notifications!.setup.request = NotificationSetting.SendEmail

    if (!estate.isValuation) {
      config.notifications!.setup.signatureReceipt =
        NotificationSetting.SendEmail
    }

    config.notifications!.setup.reminder = NotificationSetting.SendEmail
  }

  if (hasPhoneNr) {
    if (seller.mobilePhone.startsWith('0047')) {
      seller.mobilePhone = seller.mobilePhone.substring(4)
    }
    config.signerInfo!.mobile = {
      countryCode: MobileCountryCode.Norway,
      number: seller.mobilePhone.replaceAll(' ', '').replace('+47', ''),
    }

    config.notifications!.setup.request = NotificationSetting.SendSms
    config.notifications!.setup.reminder = NotificationSetting.SendSms
  }

  if (hasBoth) {
    config.notifications!.setup.request = NotificationSetting.SendBoth
    config.notifications!.setup.reminder = NotificationSetting.SendBoth
  }

  if (skipInitialNotification) {
    config.notifications!.setup.request = NotificationSetting.Off
  }

  return config
}

export function getAuthMechanism({
  useEid,
  handwrittenSignature,
}: {
  useEid?: boolean
  handwrittenSignature?: boolean
}) {
  if (!isProd && handwrittenSignature) {
    return AuthenticationMechanism.Off
  }

  return useEid ? AuthenticationMechanism.Eid : AuthenticationMechanism.SmsOtp
}

export function getSignatureType({
  useEid,
  handwrittenSignature,
}: {
  useEid?: boolean
  handwrittenSignature?: boolean
}): SignerOptions['signatureType'] {
  if (!isProd && handwrittenSignature) {
    return { mechanism: SignatureMechanism.Handwritten }
  }

  if (!useEid) {
    return { mechanism: SignatureMechanism.PkiSignature }
  }

  return {
    mechanism: SignatureMechanism.Identification,
    signatureMethods: [SignatureMethod.NoBankIdOidc],
  }
}

export function validSSN(ssn?: string): boolean {
  return Boolean(ssn?.length === 11)
}
