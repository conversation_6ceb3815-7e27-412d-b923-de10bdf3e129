import {
  MobileCountryCode,
  NotificationSetting,
  RedirectMode,
  type SignerOptions,
} from '@nordvik/signicat-express-sdk/types'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { encodeBase64 } from '@/lib/base64'
import { getBrokerName } from '@/lib/get-broker-name'
import { getBaseUrl, isProd } from '@/lib/getBaseUrl'

import { getSignatureType } from './get-seller-signer'
import { defaultUI } from './ui'

const baseUrlOppragsavtale = `${getBaseUrl()}/oppdragsavtale`
const baseUrlVerdivurdering = `${getBaseUrl()}/verdivurdering`

export const SIGNICAT_BROKER_TITLE = 'Megler'

export default function getBrokerSigner(
  broker: {
    employeeId: string
    email: string
    name: string
    mobilePhone?: string
  },
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>,
  handwritten: boolean,
  useEid: boolean,
): SignerOptions {
  if (!isProd) {
    broker.email = process.env.TEST_EMAIL?.split(';')[0] ?? ''
  }

  const { firstNames, lastName } = getBrokerName(broker.name)
  const baseUrl = estate.isValuation
    ? baseUrlVerdivurdering
    : baseUrlOppragsavtale

  const config: SignerOptions = {
    externalSignerId: encodeBase64(broker.employeeId),
    signatureType: getSignatureType({
      handwrittenSignature: handwritten,
      useEid,
    }),
    signerInfo: {
      email: broker.email,
      title: SIGNICAT_BROKER_TITLE,
      firstName: firstNames,
      lastName: lastName,
    },
    redirectSettings: {
      redirectMode: RedirectMode.Redirect,
      success: `${baseUrl}/${estate.estateId}/suksess`,
      cancel: `${baseUrl}/${estate.estateId}?signicatReason=cancel`,
      error: `${baseUrl}/${estate.estateId}?signicatReason=error`,
    },
    ui: defaultUI,
    getSocialSecurityNumber: true,
    notifications: {
      setup: {
        request: NotificationSetting.SendEmail,
        expired: NotificationSetting.SendEmail,
        finalReceipt: NotificationSetting.SendEmail,
      },
      mergeFields: {
        '{name}': broker.name,
        '{address}': estate.address?.streetAddress ?? 'ukjent',
      },
    },
  }

  if (!estate.isValuation && broker.mobilePhone) {
    config.signerInfo!.mobile = {
      number: broker.mobilePhone.replaceAll(' ', ''),
      countryCode: MobileCountryCode.Norway,
    }

    config.notifications!.setup.finalReceipt = NotificationSetting.SendBoth
  }

  return config
}
