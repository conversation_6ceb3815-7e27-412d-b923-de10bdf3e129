import { Geo } from '@vercel/functions'

import prisma from '@/db/prisma'

import { logListingAgreementInteraction } from './log-listing-agreement-interaction'

export async function viewTokenUsed({
  token,
  cid,
  ecid,
  ...extra_data
}: {
  token: string
  cid?: string | null
  ecid?: string | null
  source?: string | null
  ip_address?: string
  geolocation?: Geo
  userAgent?: {
    browser: {
      name?: string
      version?: string
    }
    device: {
      model?: string
      type?: string
      vendor?: string
    }
  }
}) {
  const tokenEntry = await prisma.offer_access_tokens.findUnique({
    where: { token },
  })

  if (tokenEntry?.listing_agreement_id) {
    try {
      await logListingAgreementInteraction({
        listing_agreements_id: tokenEntry.listing_agreement_id,
        event_type: 'viewed',
        seller_id: cid || ecid || null,
        extra_data,
      })
    } catch (error) {
      console.warn('Failed to log listing agreement interaction:', error)
    }
  } else if (tokenEntry?.inspection_folder_id) {
    try {
      const inspection = await prisma.inspection_folders.findUnique({
        where: { id: tokenEntry.inspection_folder_id },
        select: {
          listing_agreement: {
            select: {
              id: true,
            },
          },
          listing_agreement_active: true,
        },
      })
      if (inspection?.listing_agreement?.id) {
        try {
          await logListingAgreementInteraction({
            listing_agreements_id: inspection.listing_agreement?.id,
            event_type: 'viewed',
            seller_id: cid || ecid || null,
            extra_data: {
              ...extra_data,
              listing_agreement_active: inspection.listing_agreement_active,
            },
          })
        } catch (error) {
          console.warn('Failed to log inspection folder interaction:', error)
        }
      }
    } catch (error) {
      console.warn('Failed to log inspection folder interaction:', error)
    }
  }

  if (token) {
    await prisma.access_token_audit.create({
      data: {
        token,
        contact_id: cid,
        extra_contact_id: ecid,
        source: extra_data.source,
      },
    })
  }
}
