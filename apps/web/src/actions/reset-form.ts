'use server'

import { EstateFormType } from '@/utils/forms'
import { isDevelopment } from '@/utils/isDevelopment'

export async function resetForm(estateId: string, formType: EstateFormType) {
  if (isDevelopment) {
    console.info('DEVELOPMENT: Reset form server action', estateId, formType)
    return true
  }
  let type = formType

  const searchParams = new URLSearchParams()

  // add extra params and change type to app api for this type
  if (formType === EstateFormType.SettlementBuyerProject) {
    type = EstateFormType.SettlementBuyer
    searchParams.append('estateBaseType', '4')
  }

  const url = new URL(
    `${estateId}/${type}/reset?${searchParams.toString()}`,
    process.env.NORDVIK_APP_LINK,
  )

  try {
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'x-api-key': 'Q0Rbk136gl',
      },
    })

    if (!response.ok) {
      throw new Error('Failed to reset form')
    }

    return response.json()
  } catch (error) {
    console.error(error)

    throw new Error('Failed to reset form')
  }
}
