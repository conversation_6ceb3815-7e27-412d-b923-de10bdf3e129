'use server'

import mailChimp, {
  MessagesSendTemplateRequest,
} from '@mailchimp/mailchimp_transactional'

import { isProd } from '@/lib/getBaseUrl'
import nordvikApi from '@/server/nordvik-client-adaptor'
import { isNotNull } from '@/server/utils'

import { employeeMailSignature } from './employee-signature'

const mailchimpTx = mailChimp(process.env.MAILCHIMP_API_KEY!)

export async function sendMail({
  title,
  body,
  subject,
  from,
  emails,
  attachments,
  includeSignatureForEmployeeId,
  blindCopyTo,
  initialReceiver,
}: {
  title: string
  body: string
  subject: string
  from: { email: string; name: string }
  emails: { email: string; name?: string }[]
  includeSignatureForEmployeeId?: string
  attachments?: MessagesSendTemplateRequest['message']['attachments']
  blindCopyTo?: string
  initialReceiver?:
    | { email?: string; name?: string; role?: string }
    | { email?: string; name?: string; role?: string }[]
}) {
  if (!isProd) {
    body = appendDebugInfo(
      body,
      Array.isArray(initialReceiver)
        ? initialReceiver.filter(isNotNull)
        : [initialReceiver].filter(isNotNull),
      blindCopyTo,
    )

    emails =
      process.env.TEST_EMAIL?.split(';').map((email) => ({
        email,
        name: 'Test User',
      })) ?? []

    // It has to use @norvikbolig.no to be able to send to the test email
    blindCopyTo = '<EMAIL>'
  }

  if (includeSignatureForEmployeeId) {
    const apiResponse = await nordvikApi.employeeSessionInfo({
      employeeId: includeSignatureForEmployeeId,
    })

    const employee = apiResponse.employee

    if (employee) {
      const signature = employeeMailSignature({
        ...employee,
        ...from,
        image: employee?.image?.small,
      })

      body += signature
    } else {
      console.error(
        `Failed to fetch employee data for employeeId: ${includeSignatureForEmployeeId}`,
      )
    }
  }

  const content = [
    {
      name: 'text',
      content: body,
    },
    {
      name: 'source',
      content: `<a href="https://www.nordvikbolig.no/" style="color: #656565;">nordvikbolig.no</a>`,
    },
  ]

  content.push({
    name: 'header',
    content: title,
  })

  return mailchimpTx.messages.sendTemplate({
    template_name: 'transactional-template',
    template_content: content,
    message: {
      subject,
      from_email: from.email,
      from_name: from.name,
      to: emails,
      bcc_address: blindCopyTo,
      headers: {
        'Reply-To': from.email,
      },
      attachments,
    },
  })
}

function appendDebugInfo(
  body: string,
  initialReceiver: { email?: string; name?: string; role?: string }[],
  blindCopyTo: string | undefined,
) {
  const debugHeader = `
  <div style="padding: 10px; background-color: #f0f0f0; border-radius: 5px; border: 1px solid red; margin-bottom: 10px;">
  <p style="color: black;">Initial receivers:</p>
  ${initialReceiver.length > 0 ? `<ul>${initialReceiver.map((r) => `<li style="color: black;">${r.email ?? 'Unknown email'} ${r.name ?? ' '} (${r.role ?? ' '})</li>`).join('')}</ul>` : ''}
  ${blindCopyTo ? `<p style="color: black;">Blind copy to: ${blindCopyTo}</p>` : ''}
  </div>
  `

  return debugHeader + body
}
