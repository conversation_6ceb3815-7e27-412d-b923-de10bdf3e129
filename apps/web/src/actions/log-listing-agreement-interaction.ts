'use server'

import { Prisma } from '@prisma/client'

import { GQLListingAgreementInteractionType } from '@/api/generated-client'
import prisma from '@/db/prisma'
import { getGeo } from '@/lib/get-geo'
import { getCurrentUser } from '@/lib/session'

type EventType =
  | GQLListingAgreementInteractionType
  | `${GQLListingAgreementInteractionType}`

export interface OfferInteraction {
  listing_agreements_id: string
  seller_id?: string | null
  employee_id?: string | null
  event_type: EventType
  event_timestamp: Date
  extra_data?: Record<string, unknown>
}

export async function logListingAgreementInteraction(
  interaction: Omit<OfferInteraction, 'event_timestamp'>,
) {
  try {
    const eventTimestamp = new Date()

    if (!interaction.extra_data) {
      interaction.extra_data = {
        geolocation: await getGeo().catch(() => null),
      }
    } else if (!interaction.extra_data.geolocation) {
      interaction.extra_data.geolocation = await getGeo().catch(() => null)
    }

    if (!interaction.employee_id) {
      // check if there is a user in the session
      try {
        const user = await getCurrentUser()
        interaction.employee_id = user?.employeeId
      } catch (error) {
        console.error('No logged in user', error)
      }
    }

    await prisma.listing_agreement_interactions.create({
      data: {
        ...interaction,
        event_timestamp: eventTimestamp,
        extra_data: interaction.extra_data as Prisma.InputJsonObject,
      },
    })
  } catch (error) {
    console.error('Failed to log listing agreement interaction', error)
  }
}

export async function getListingAgreementInteractions(agreementId: string) {
  return await prisma.listing_agreement_interactions.findMany({
    where: {
      listing_agreements_id: agreementId,
    },
    orderBy: {
      event_timestamp: 'desc',
    },
  })
}
