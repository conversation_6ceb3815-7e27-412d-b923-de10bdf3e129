'use server'

import { fetchVitecHub<PERSON>pi, vitecHubApiBaseUrl } from '@/api/vitec-hub-api'
import prisma from '@/db/prisma'

import type { EstatePageCategory } from './types'

/**
 * PROD - https://hub.megler.vitec.net/swagger/index.html#/Next/GetNextPath
 *
 * TEST - https://hubtest.megler.vitec.net/swagger/index.html#/Next/GetNextPath
 */
export async function nextGetPath(
  estateId: string,
  pageCategory: EstatePageCategory,
): Promise<string | null | undefined> {
  if (!estateId) {
    console.error('Missing estateId')
    return null
  }

  const entry = await prisma.vitec_estate_link.findUnique({
    where: {
      estate_id_category: {
        estate_id: estateId,
        category: pageCategory,
      },
    },
    select: {
      link: true,
    },
  })

  if (entry?.link) {
    return entry.link
  }

  const url = new URL(`${vitecHubApiBaseUrl}/Next/GetPath`)
  url.searchParams.append('pageCategory', pageCategory)
  url.searchParams.append('mainId', estateId)

  try {
    const response = await fetchVitecHubApi<string>(url.toString())
    if (response) {
      try {
        await prisma.vitec_estate_link.upsert({
          where: {
            estate_id_category: {
              estate_id: estateId,
              category: pageCategory,
            },
          },
          create: {
            estate_id: estateId,
            category: pageCategory,
            link: response,
          },
          update: {},
        })
      } catch (error) {
        console.error('Failed to save Vitec URL', error)
      }
      return response
    }
  } catch (error) {
    console.info('[nextGetPath]: Failed to fetch Vitec URL', error)
    return null
  }
}
