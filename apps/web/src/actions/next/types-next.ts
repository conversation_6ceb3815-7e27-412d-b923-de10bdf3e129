import type { budget_posts } from '@prisma/client'

import type { ReplaceDecimalWithNumber } from '@/utils/typeHelpers'

export enum CreateActivityType {
  Showing = 1, // Visning
  PhoneIn = 2, // Telefon inn
  PhoneOut = 3, // Telefon ut
  BidFollow = 4, // Budoppfølging
  Document = 5, // Dokument
  Mailing = 6, // Utsendelse
  Sms = 7, // SMS
  Note = 8, // Notat
  Inspection = 9, // Befaring
  PrivateViewing = 10, // Privatvisning
  FollowUp = 11, // Oppfølging
  Contract = 12, // Kontraktsmøte
  Takeover = 13, // Overtakelse
  Ad = 14, // Nettannonse
  SearchProfile = 15, // Søkeprofil
  Depot = 16, // Depot
  AssignmentPeriod = 17, // Oppdragsperiode
  Task = 18, // Gjøremål
  ProjectGuarantee = 19, // Garantistillelse
  ProjectMeeting = 20, // Prosjektmøte
  Kartverket = 21, // Kartverket
  Leads = 22, // Tips
  PreInspection = 23, // Forhåndsbefaring
  ETinglysing = 24, // eTingLysing
  CallList = 25, // Ringelister
  YearInspection = 26, // Ettårsbefaring
  SettlementFolder = 27, // Oppgjørsmappe
  TakeoverLog = 28, // Overtakelsesprotokoll
  Settlement = 29, // Oppgjør
  Consent = 30, // Samtykke
  EstateData = 31, // Oppdragsdata
  EmailIn = 32, // E-post inn
  EmailOut = 33, // E-post ut
  Photography = 34, // Fotografering
  Appraisal = 35, // Taksering
  MoneyLaunderingControl = 36, // Hvitvaskingskontroll
  InternalControl = 37, // Internkontroll
  SellerTask = 38, // Oppgave for selger
  BuyerTask = 39, // Oppgave for kjøper
}

export interface NextAssignmentBudget {
  type: number
  estimatedCommission: number
  baseCommission: number
  commonDebtIncluded: boolean
  sumCommission: number
  sumOtherIncome: number
  sumOutlay: number
  sumOtherExpenses: number
  sumTotal: number
  otherIncomeBudgetPosts: NextBudgetPost[]
  outlayBudgetPosts: NextBudgetPost[]
  otherExpensesBudgetPosts: NextBudgetPost[]
  status: number
  feePercent: number | null
  fixedPriceIncludes: string
  hourlyPrice: number | null
  noCommissionBase: boolean
}

export interface NextBudgetPost {
  budgetpostId: number
  name: string
  userId: string
  status: number
  lastChanged: string
  changedBy: string
  amountWithTaxExcluded: number
  amountWithTaxIncluded: number
}

interface Broker {
  employeeId: string
  brokerRole: number
}

interface Showing {
  start: string
  end: string
  showingId: string
}

interface RentalCost {
  perYear: number
  perMonth: number
  perWeek: number
  perDay: number
  deposit: number
  includes: string
  perQuarter: number
  perSqmPerMonth: number
  perSqmPerYear: number
  rentIncludes: string
  rentGuaranteeAmount: number
  rentGuaranteeAgreement: string
  rentTermination: string
  sublease: string
  tenantDuties: string
}

interface RentalTimeSpan {
  from: string
  to: string
}

interface Plot {
  owned: boolean
  size: number
  description: string
}

interface PartOwnership {
  partName: string
  partOrgNumber: string
  partNumber: number
  estateHousingCooperativeStockHousingUnitNumber: number
  partAbout: string
  contactId: string
  businessManagerContactId: string
  preemptive: string
  animals: string
  guaranteeFund: string
  loanTermsCollectiveDebt: string
  accountingBudgeting: string
  amountYearsInstallmentFree: string
  boardApproval: string
  changeOfOwnershipFee: number
  deposit: number
  estateCorporationPartialBondNumber: number
  estateCorporationPartialDenominatedBond: number
  interestGracePeriod: string
  joinCostAfterGracePeriod: string
  jointDebtDescription: string
  jointDebtInstallment: string
  jointDebtInterest: string
  preEmptiveDeadline: string
  preEmptiveFee: number
  residentCommitments: string
  shareJointCapital: number
  shareJointCapitalDate: string
  shareJointDebtToDate: string
  shareJointDebtYear: number
  statutes: string
  estateJointOwnershipFraction: string
  estateHousingCooperativeStockNumber: string
  estateHousingCooperativeNominalStockValue: number
}

interface Rental {
  rentPrMonth: number
  rentIncludes: string
}

interface EstatePrice {
  priceSuggestion: number | null
  soldPrice: number | null
  estimatedValue: number | null
  collectiveDebt: number | null
  collectiveAssets: number | null
  loanFare: number | null
  communityTax: number | null
  communityTaxYear: number | null
  salesCostDescription: string | null
  rent: Rental | null
  purchaseCostsAmount: number | null
  totalPrice: number | null
  totalPriceExclusiveCostsAndDebt: number | null
  waterRate: number | null
  waterRateDescription: string | null
  waterRateYear: number | null
  yearlySocietyTax: number | null
  yearlyLeaseFee: number | null
  leasingPartyTransportFee: number | null
  originalAgreementPrice: number | null
  additionalAgreementOptions: number | null
  originalExpensesPrice: number | null
  transportAgreementCosts: number | null
  otherExpenses: string | null
}

interface Link {
  linkType: number
  url: string
  text: string
}

interface Address {
  apartmentNumber: string
  streetAdress: string
  zipCode: string
  city: string
  municipality: string
}

interface Matrikkel {
  knr: number
  gnr: number
  bnr: number
  fnr: number
  snr: number
  ownPart: string
  coOwnershipType: number
}

type TextFields = Record<string, string>

interface AreaInformation {
  floorNumber: number
  areaSize: number
  areaDescription: string
}

interface BuildingArea {
  areaType: number
  areaInformation: AreaInformation[]
}

interface Building {
  name: string
  buildingArea: BuildingArea[]
}

interface SumArea {
  bra: number
  braI: number
  braE: number
  braB: number
  braS: number
  pRom: number
  tba: number
  sRom: number
  bta: number
}

interface ValuationTax {
  primaryValue: number
  primaryYear: number
  secondaryValue: number
  secondaryYear: number
  comment: string
  propertyTaxAmount: number
  propertyTaxYear: number
  valuationDate: string
  valuationType: string
}

export interface Estate {
  heading: string
  estateId: string
  assignmentNum: string
  systemId: string
  departmentId: number
  settleDepartmentId: number
  brokersIdWithRoles: Broker[]
  status: number
  assignmentType: number
  assignmentTypeGroup: number
  ownership: number
  estateTypeExternal: number
  estateBaseType: number
  estatePreferences: string[]
  estateFacilities: { Name: string }[]
  showings: Showing[]
  showingNote: string
  noOfRooms: number
  noOfBedRooms: number
  noOfBathRooms: number
  floor: number
  constructionYear: number
  energyLetter: number
  energyColorCode: number
  rentalCost: RentalCost
  rentalTimeSpan: RentalTimeSpan
  availableDate: string
  plot: Plot
  partOwnership: PartOwnership
  estatePrice: EstatePrice
  links: Link[]
  address: Address
  matrikkel: Matrikkel[]
  textFields: TextFields
  createdDate: string
  soldDate: string
  commissionAcceptedDate: string
  takeOverDate: string
  contractMeetingDate: string
  expireDate: string
  finnCode: string
  finnPublishDate: string
  finnExpireDate: string
  leasingContractDate: string
  valuationTax: ValuationTax
  municipality: string
  municipalityId: string
  projectId: string
  projectRelation: number
  publicApartmentNumber: string
  customerPortal: boolean
  estateTypeId: string
  estateType: string
  longitude: number
  latitude: number
  takeoverComment: string
  appraiserContactId: string
  areaId: string
  completionCertificateDate: string
  facilities: number[]
  objectFacilities: { Name: string }[]
  landOwnerEstateDocumentDate: string
  liveAndManagementDuty: boolean
  managementDuty: boolean
  odel: boolean
  ownAssignmentType: string
  ownAdvertisementType: string
  requiresConcession: boolean
  tag: string
  buildings: Building[]
  sumArea: SumArea
  estateSettlementStatusGroup: number
  estateBackOfficeStatusGroup: number
  farm: boolean
  inSettlement: boolean
  lastDocumentChangeDate: string
  lastImageChangeDate: string
  changedDate: string
}

export type MergedBudgetPost = ReplaceDecimalWithNumber<budget_posts> &
  NextBudgetPost

export interface BudgetDataSection {
  posts: MergedBudgetPost[]
  sum: number | null | undefined
}

export interface NextPrivateContact {
  socialSecurity?: string
  departmentId: number
  contactType: number
  contactId: string
  companyName?: string
  organisationNumber?: string
  firstName?: string
  lastName?: string
  mobilePhone?: string
  privatePhone?: string
  workPhone?: string
  email?: string
  address?: string
  postalAddress?: string
  postalCode?: string
  city?: string
  consents?: unknown[]
  changedDate?: string
  customerReview?: string
  webSite?: string
  relationType?: number
}

export type NextPrivateContactWithProxy = NextPrivateContact & {
  proxy?: NextPrivateContact | null
  relationType?: number
}

export enum TipStatus {
  Undefined = 0,
  Received = 1,
  Activated = 2,
  OfferMade = 3,
  NotApplicable = 4,
  Won = 5,
  Lost = 6,
  Valuation = 7,
  Read = 8,
  NotHandled = 9,
}

export interface GetTipByIdResponse {
  created: string
  firstName: string
  lastName: string
  mobilePhone: string
  originType: number
  postalCode: string
  email?: string
  estateId?: string
  streetAdress?: string
  comment?: string
  source?: string
  productId?: string
  recipientId?: string
  modified?: string
  status: TipStatus
  userId?: string
  departmentId?: string
  contactId?: string
}

export enum DocumentTypeEnum {
  SelfDeclaration = 1,
  Shareholders = 2,
  AdDraft = 3,
  PropertyInformation = 5,
  BidForm = 6,
  Various = 7,
  PropertyProfile = 9,
  PropertyValuation = 10,
  EnergyCertificate = 11,
  EmailReceived = 12,
  EmailSent = 13,
  ElectronicValuation = 14,
  Invoice = 15,
  CompletionCertificate = 16,
  PlotContract = 17,
  CompanyCertificate = 18,
  FinancingInformation = 19,
  PreemptiveInformation = 20,
  ListingAgreementRenewal = 21,
  Authorization = 22,
  Warranties = 23,
  LandregistryTranscript = 24,
  LandownerInformation = 25,
  OwnerChange = 26,
  Contract = 27,
  MunicipalInformation = 28,
  ConcessionForm = 29,
  ValuationTax = 31,
  ListingAgreement = 32,
  Settlement = 33,
  SettlementForm = 34,
  AcquisitionProtocol = 35,
  LienOther = 36,
  Lien = 37,
  LienRevokedConfirmation = 38,
  LienLock = 39,
  ZoningplanMap = 40,
  Floorplan = 41,
  RemainingDebt = 42,
  PropertyDescription = 43,
  PropertyDescriptionDraft = 44,
  SectioningRequest = 45,
  Easement = 46,
  Deed = 47,
  BoardApproval = 48,
  Drawing = 49,
  SupplementaryAgreement = 50,
  Transport = 51,
  Statute = 52,
  OpenHouseList = 53,
  AuthorizationBuyer = 54,
  SettlementBuyer = 55,
  BrokerValuation = 56,
  AcceptanceLetterBuyer = 57,
  AcceptanceLetterSeller = 58,
  Lease = 59,
  InsuranceSeller = 60,
  InsuranceBuyer = 61,
  Pricelist = 62,
  Declaration = 63,
  CoverLetter = 64,
  SettlementLineupSeller = 65,
  SettlementLineupBuyer = 66,
  ProAndContraScheme = 67,
  AccountingInformation = 69,
  Requisition = 70,
  Offer = 71,
  InterestReport = 72,
  LienBuyer = 73,
  PrerequisiteLetterBank = 74,
  ContractAttachment = 75,
  PropertyDescriptionAttachment = 76,
  MoneyLaunderingDocument = 77,
  CompanyDocument = 78,
  ShareDocument = 79,
  Will = 80,
  MutualWill = 81,
  GrantOfProbate = 82,
  EstateAuthorization = 83,
  DocumentFeeRationale = 84,
  BidJournal = 85,
  BidAttachment = 86,
  ConfirmedLandregistryTranscript = 88,
  ConsentFromLessor = 89,
  ConsentFromRightsHolderToEncumbrance = 90,
  HouseRules = 91,
  BuildingDrawings = 92,
  DeliveryDescription = 93,
  SettlementFormSeller = 94,
  SettlementFormBuyer = 95,
  ConditionReportArchive = 96,
  SignedPurchaseContract = 97,
  PropertyDescriptionDocuments = 1000,
}

export type DocumentType = DocumentTypeEnum | `${DocumentTypeEnum}`
