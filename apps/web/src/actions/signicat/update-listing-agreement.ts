'use server'

import { differenceInDays, differenceInMinutes } from 'date-fns'
import { isBefore } from 'date-fns/isBefore'
import { subMinutes } from 'date-fns/subMinutes'

import { DocumentStatus } from '@nordvik/signicat-express-sdk/types'

import { EstateStatusNumber } from '@/app/(protected)/(sidebar)/oppdrag/util'
import { cancelEstateDocuments } from '@/app/api/signicat/util/cancel-estate-documents'
import { getEstateStatus } from '@/app/api/signicat/util/get-estate-status'
import { processSignedDocumentEvent } from '@/app/api/signicat/util/process-signed-document-event'
import prisma from '@/db/prisma'
import signicat from '@/external-services/signicat/client'

const SIGNED_BACKOFF_MINUTES = 10

export async function updateListingAgreement(listingAgreement: {
  id: string
  estate_id: string
  signicat_document_id: string
}) {
  try {
    const summary = await signicat.getDocumentSummary(
      listingAgreement.signicat_document_id,
    )

    await checkIfDocumentIsSignedOrDeadlineChanged({
      estateId: listingAgreement.estate_id,
      signedDate: summary.signedDate,
      deadline: summary.deadline,
      documentId: listingAgreement.signicat_document_id,
    })

    const estateStatus = await getEstateStatus(listingAgreement.estate_id)

    if (
      estateStatus === EstateStatusNumber.InPreperation &&
      summary.status.documentStatus !== DocumentStatus.Signed &&
      differenceInDays(new Date(), summary.lastUpdated) > 2
    ) {
      console.info(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and document status ${summary.status.documentStatus}`,
      )

      try {
        await cancelEstateDocuments(
          listingAgreement.estate_id,
          'Signed agreement in Vitec Next',
        )
      } catch (error) {
        console.error(`Failed to cancel estate documents: ${error}`)
      }
    } else if (estateStatus === EstateStatusNumber.Lost) {
      console.info(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and has an unsigned document in Signicat`,
      )
      try {
        await cancelEstateDocuments(
          listingAgreement.estate_id,
          'Estate lost in Vitec Next',
        )
      } catch (error) {
        console.error(`Failed to cancel estate documents: ${error}`)
      }
    } else if (estateStatus !== 0) {
      console.warn(
        `Estate ${listingAgreement.estate_id} has status ${estateStatus} and has an unsigned document in Signicat`,
      )
    }
  } catch (error) {
    console.error(`Sync Signicat summary error: ${error}`)
  }
}

export async function checkIfDocumentIsSignedOrDeadlineChanged({
  estateId,
  signedDate,
  deadline,
  documentId,
}: {
  estateId: string
  signedDate?: string | null
  deadline: string | null
  documentId: string
}) {
  if (
    signedDate &&
    // Give the event the chance to be fired, don't be too eager
    isBefore(
      new Date(signedDate),
      subMinutes(new Date(), SIGNED_BACKOFF_MINUTES),
    )
  ) {
    console.info(
      `Found signed document for estate ${estateId} during sync, signed ${differenceInMinutes(signedDate, new Date())} minutes ago`,
    )
    await processSignedDocumentEvent({
      externalId: estateId,
      id: documentId,
    })
  } else {
    await prisma.listing_agreements.updateMany({
      where: { estate_id: estateId },
      data: {
        deadline_for_signing: deadline,
      },
    })
  }
}
