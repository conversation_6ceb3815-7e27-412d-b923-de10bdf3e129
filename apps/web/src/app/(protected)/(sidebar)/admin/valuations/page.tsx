import { endOfDay, isAfter, startOfDay, startOfMonth } from 'date-fns'
import { endOfToday } from 'date-fns/endOfToday'
import type { Metadata } from 'next'
import { Suspense } from 'react'

import { get, set } from '@/db/kv'
import { MINUTE } from '@/db/util'

import DateFilter from '../../toppliste/components/filters/date-filter'
import { AdminTabs } from '../components/admin-tabs'

import { getValuationInsights } from './get-valuation-insights'
import Loading from './loading'
import { ValuationChart, ValuationInsightData } from './valuation-chart'
import { ValuationTable } from './valuation-table'

export const metadata: Metadata = {
  title: 'Valuations - Admin',
}

export default async function ValuationsPage(props: {
  searchParams: Promise<{
    from: string
    to: string
  }>
}) {
  const searchParams = await props.searchParams
  const { from, to } = searchParams

  return (
    <div className="flex flex-col gap-4 py-4">
      <div className="mt-8 mb-2 flex items-baseline justify-between">
        <h1 className="typo-display-lg">Admin</h1>
        <div className="flex items-stretch gap-4 flex-wrap">
          <DateFilter align="end" shallow={false} />
        </div>
      </div>
      <AdminTabs currentTab="valuations" />

      {/* Charts Section */}
      <div className="mb-6">
        <Suspense key={`valuation-${from}-${to}`} fallback={<Loading />}>
          <ValuationInsights from={from} to={to} />
        </Suspense>
      </div>

      {/* Table Section */}
      <div className="mt-6">
        <Suspense key={`valuation-table-${from}-${to}`} fallback={<Loading />}>
          <ValuationTable from={from} to={to} />
        </Suspense>
      </div>
    </div>
  )
}

async function ValuationInsights({ from, to }: { from: string; to: string }) {
  const now = endOfToday()

  const providedToDate = to ? new Date(to) : now
  const toDate = isAfter(providedToDate, now) ? now : providedToDate
  const fromDate = from ? new Date(from) : startOfMonth(now)

  const cacheKey = `valuation-insights-${from}-${to}`

  let data = await get<ReturnType<typeof getValuationInsights>>(cacheKey)

  if (!data) {
    data = await getValuationInsights({
      from: startOfDay(fromDate),
      to: endOfDay(toDate),
    })

    try {
      await set(cacheKey, data, MINUTE * 15)
    } catch (error) {
      console.error('Error setting cache:', error)
    }
  }

  return (
    <div className="grid grid-cols-1 gap-4">
      <ValuationChart data={data.insights as ValuationInsightData[]} />
    </div>
  )
}
