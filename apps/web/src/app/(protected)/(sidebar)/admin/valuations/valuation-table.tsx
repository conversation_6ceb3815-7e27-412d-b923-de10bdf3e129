import { JsonObject } from '@prisma/client/runtime/library'
import { endOfDay, isAfter, startOfDay, startOfMonth } from 'date-fns'
import { endOfToday } from 'date-fns/endOfToday'
import { CheckCircle, Clock, SquareArrowOutUpRightIcon, X } from 'lucide-react'
import { Suspense } from 'react'

import { Badge } from '@nordvik/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@nordvik/ui/table'
import { TextButton } from '@nordvik/ui/text-button'

import { SIGNICAT_BROKER_TITLE } from '@/actions/signing/get-broker-signer'
import mongo from '@/db/mongo'
import prisma from '@/db/prisma'
import { encodeBase64 } from '@/lib/base64'
import { formatDate } from '@/lib/dates'

import { getBrokerForEstate } from '../get-broker-for-estate'

const base64TestBrokers = ['UNNO', 'ROBU', 'BJSK', 'AW', 'ROBJE'].map(
  encodeBase64,
)

type ValuationListingAgreement = {
  id: string
  estateId: string
  createdAt: Date
  sentToSellerAt: Date | null
  isSigned: boolean
  signingFinishedAt: Date | null
  etakstSentAt: Date | null
  wasDownloaded: boolean
  refinancingRequested: boolean
  refinancingRequestedAt: Date | null
  refinancingOffer: JsonObject | null
  isPartiallySigned: boolean
}

async function getValuationListingAgreements(
  fromDate: Date,
  toDate: Date,
): Promise<ValuationListingAgreement[]> {
  // Get all listing agreements with is_valuation true and have been sent to seller
  const results = await prisma.listing_agreements.findMany({
    where: {
      is_valuation: true,
      listing_agreement_interactions: {
        some: {
          event_type: {
            in: ['sent_to_seller', 'etakst_sent', 'start_signing'],
          },
          event_timestamp: {
            gte: fromDate,
            lte: toDate,
          },
        },
      },
    },
    include: {
      listing_agreement_interactions: {
        orderBy: {
          event_timestamp: 'desc',
        },
      },
      signers: {
        select: {
          signed_at: true,
          external_signer_id: true,
          title: true,
        },
      },
    },
    orderBy: {
      updated_at: 'desc',
    },
  })

  await mongo.connect()
  const tips = await mongo
    .db('nordvik')
    .collection('tips')
    .find({
      estateId: { $in: results.map((agreement) => agreement.estate_id) },
    })
    .toArray()

  const valuations: ValuationListingAgreement[] = results
    .filter((entry) => {
      return !entry.signers.some(
        (signer) =>
          signer.external_signer_id &&
          signer.title === SIGNICAT_BROKER_TITLE &&
          base64TestBrokers.includes(signer.external_signer_id),
      )
    })
    .map((agreement) => {
      const interactions = agreement.listing_agreement_interactions

      // Find the latest 'sent_to_seller' interaction
      const sentToSellerInteraction = interactions.find(
        (i) => i.event_type === 'sent_to_seller',
      )

      // Find etakst related interactions
      const etakstSentInteraction = interactions.find(
        (i) => i.event_type === 'etakst_sent',
      )

      // 'etakst_clicked' represents when a user has clicked/accessed the etakst (closest to download behavior)
      const wasDownloaded = interactions.some(
        (i) => i.event_type === 'etakst_clicked',
      )

      // Check for refinancing request interactions
      // 'tip_sent' represents when a seller has requested refinancing information
      const refinancingInteraction = interactions.find(
        (i) => i.event_type === 'tip_sent',
      )

      const tip = tips.find((t) => t.estateId === agreement.estate_id)

      return {
        id: agreement.id,
        estateId: agreement.estate_id,
        createdAt: agreement.created_at,
        sentToSellerAt: sentToSellerInteraction?.event_timestamp || null,
        isSigned: !!agreement.signing_finished_at,
        isPartiallySigned: agreement.signers.some(
          (signer) =>
            signer.signed_at && signer.title !== SIGNICAT_BROKER_TITLE,
        ),
        signingFinishedAt: agreement.signing_finished_at,
        etakstSentAt: etakstSentInteraction?.event_timestamp || null,
        wasDownloaded,
        refinancingOffer:
          agreement.receive_loan_offer && agreement.loan_offer_comment
            ? (agreement.loan_offer_comment as JsonObject)
            : tip
              ? { source: `${tip.source} (mongo)` }
              : null,
        refinancingRequested: !!(refinancingInteraction || tip),
        refinancingRequestedAt:
          refinancingInteraction?.event_timestamp || tip?.created,
      }
    })

  return valuations
}

async function BrokerWithImage({ estateId }: { estateId: string }) {
  const broker = await getBrokerForEstate(estateId)

  return (
    <>
      <TableCell>
        <div className="flex items-center gap-1">
          <img
            src={broker.image}
            alt={broker.name}
            className="w-6 h-6 rounded-full"
          />
          <span>{broker.name}</span>
        </div>
      </TableCell>
      <TableCell>{broker.department}</TableCell>
    </>
  )
}

function LeadStatusBadge({
  refinancingRequested,
  refinancingRequestedAt,
  refinancingOffer,
}: {
  refinancingRequested: boolean
  refinancingRequestedAt: Date | null
  refinancingOffer: JsonObject | null
}) {
  if (refinancingRequested) {
    return (
      <div className="flex flex-col gap-1">
        <Badge variant="bright-green" className="gap-2 flex w-fit">
          <CheckCircle className="h-3 w-3" />
          {refinancingRequestedAt
            ? formatDate(refinancingRequestedAt, "d MMM 'kl. 'HH:mm")
            : 'Requested'}
        </Badge>
        {refinancingOffer?.source && (
          <span className="text-xs ink-muted">
            source: {refinancingOffer.source as string}
          </span>
        )}
      </div>
    )
  }

  if (refinancingOffer) {
    return (
      <div className="flex flex-col gap-1">
        <Badge variant="beige" className="gap-2 w-fit">
          <Clock className="h-3 w-3" />
          Pending
        </Badge>
        {refinancingOffer?.source && (
          <span className="text-xs ink-muted">
            source: {refinancingOffer.source as string}
          </span>
        )}
      </div>
    )
  }

  return (
    <Badge variant="grey" className="gap-2 w-fit">
      <X className="h-3 w-3" />
      No
    </Badge>
  )
}

function SimpleStatusBadge({
  status,
  trueText = 'Yes',
  falseText = 'No',
}: {
  status: boolean
  trueText?: string
  falseText?: string
}) {
  return (
    <Badge variant={status ? 'bright-green' : 'grey'} className="gap-2">
      {status ? <CheckCircle className="h-3 w-3" /> : <X className="h-3 w-3" />}
      {status ? trueText : falseText}
    </Badge>
  )
}

function SignedStatusBadge({
  isSigned,
  isPartiallySigned,
  signingFinishedAt,
}: {
  isSigned: boolean
  isPartiallySigned: boolean
  signingFinishedAt: Date | null
}) {
  if (isSigned && signingFinishedAt) {
    return (
      <DateBadge
        date={signingFinishedAt}
        fallbackText="Yes"
        variant="bright-green"
      />
    )
  }

  if (isSigned) {
    return (
      <Badge variant="bright-green" className="gap-2">
        <CheckCircle className="h-3 w-3" />
        Yes
      </Badge>
    )
  }

  if (isPartiallySigned) {
    return (
      <Badge variant="beige" className="gap-2">
        <Clock className="h-3 w-3" />
        Partially
      </Badge>
    )
  }

  return (
    <Badge variant="grey" className="gap-2">
      <X className="h-3 w-3" />
      No
    </Badge>
  )
}

// Date Badge for date-related columns
function DateBadge({
  date,
  fallbackText = 'No',
  variant = 'bright-green',
  fallbackVariant = 'grey',
}: {
  date: Date | null
  fallbackText?: string
  variant?: 'bright-green' | 'grey' | 'beige' | 'blue'
  fallbackVariant?: 'bright-green' | 'grey' | 'beige' | 'blue'
}) {
  if (date) {
    return (
      <Badge variant={variant} className="gap-2">
        <CheckCircle className="h-3 w-3" />
        {formatDate(date, "d MMM 'kl. 'HH:mm")}
      </Badge>
    )
  }

  return (
    <Badge variant={fallbackVariant} className="gap-2">
      <X className="h-3 w-3" />
      {fallbackText}
    </Badge>
  )
}

export async function ValuationTable({
  from,
  to,
}: {
  from?: string
  to?: string
}) {
  // Process dates the same way as ValuationInsights
  const now = endOfToday()
  const providedToDate = to ? new Date(to) : now
  const toDate = isAfter(providedToDate, now) ? now : providedToDate
  const fromDate = from ? new Date(from) : startOfMonth(now)

  const valuations = await getValuationListingAgreements(
    startOfDay(fromDate),
    endOfDay(toDate),
  )

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Estate ID</TableHead>
          <TableHead>OA sent</TableHead>
          <TableHead>Signed</TableHead>
          <TableHead>Etakst sent</TableHead>
          <TableHead>Downloaded</TableHead>
          <TableHead>Lead</TableHead>
          <TableHead>Broker</TableHead>
          <TableHead>Department</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {valuations.length === 0 ? (
          <TableRow>
            <TableCell colSpan={8} className="text-center py-6 justify-center">
              No valuations found
            </TableCell>
          </TableRow>
        ) : (
          valuations.map((valuation) => (
            <TableRow key={valuation.id}>
              <TableCell className="flex items-center gap-1 justify-center">
                <TextButton
                  target="_blank"
                  href={`https://megler.nordvikbolig.no/oppdrag/detaljer/${valuation.estateId}`}
                >
                  {valuation.estateId.slice(0, 5)}
                </TextButton>
                <SquareArrowOutUpRightIcon className="size-2 -mt-1 ink-muted" />
              </TableCell>
              <TableCell className="justify-center">
                <DateBadge
                  date={valuation.sentToSellerAt}
                  fallbackText="N/A"
                  variant="blue"
                />
              </TableCell>
              <TableCell className="justify-center">
                <SignedStatusBadge
                  isSigned={valuation.isSigned}
                  isPartiallySigned={valuation.isPartiallySigned}
                  signingFinishedAt={valuation.signingFinishedAt}
                />
              </TableCell>
              <TableCell className="justify-center">
                <DateBadge date={valuation.etakstSentAt} fallbackText="No" />
              </TableCell>
              <TableCell className="justify-center">
                <SimpleStatusBadge status={valuation.wasDownloaded} />
              </TableCell>
              <TableCell className="flex flex-col gap-1 justify-center">
                <LeadStatusBadge
                  refinancingRequested={valuation.refinancingRequested}
                  refinancingRequestedAt={valuation.refinancingRequestedAt}
                  refinancingOffer={valuation.refinancingOffer}
                />
              </TableCell>
              <Suspense
                fallback={
                  <>
                    <TableCell>
                      <span>...</span>
                    </TableCell>
                    <TableCell>
                      <span>...</span>
                    </TableCell>
                  </>
                }
                key={valuation.estateId}
              >
                <BrokerWithImage estateId={valuation.estateId} />
              </Suspense>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
