import { MessageCircleIcon, SquareArrowOutUpRightIcon } from 'lucide-react'
import { Suspense } from 'react'

import { Badge } from '@nordvik/ui/badge'
import { TextButton } from '@nordvik/ui/text-button'
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import { LeadsChart } from '@/app/(protected)/(sidebar)/admin/leads/LeadsChart'
import prisma from '@/db/prisma'
import { formatDate } from '@/lib/dates'
import retry from '@/lib/retry'

import { AdminTabs } from '../components/admin-tabs'
import { getBrokerForEstate } from '../get-broker-for-estate'

function prepareLeadChartData(
  leads: {
    id: string
    estate_id: string
    lead_type: string
    created_at: Date | null
    source: string | null
    success: boolean | null
    vitec_tip_id?: string | null
    comment: string | null
  }[],
) {
  const leadsByDate = new Map<string, number>()

  leads.forEach((lead) => {
    if (lead.created_at) {
      const date = new Date(lead.created_at).toISOString().split('T')[0]
      leadsByDate.set(date, (leadsByDate.get(date) || 0) + 1)
    }
  })

  const sortedDates = Array.from(leadsByDate.keys()).sort()

  let cumulativeCount = 0
  return sortedDates.map((date) => {
    cumulativeCount += leadsByDate.get(date) || 0
    return {
      date: date,
      count: leadsByDate.get(date) || 0,
      cumulative: cumulativeCount,
    }
  })
}

export default async function Page() {
  const leads = await prisma.inspection_leads.findMany({
    orderBy: { created_at: 'desc' },
  })

  const chartData = prepareLeadChartData(leads)

  return (
    <div className="flex flex-col gap-4 py-4">
      <div className="mt-8 mb-2 flex items-baseline justify-between">
        <h1 className="typo-display-lg">Admin</h1>
      </div>
      <AdminTabs currentTab="leads" />

      <LeadsChart data={chartData} />

      <div className="overflow-x-auto">
        <table className="mt-6 [&_:is(th,td):not(:first-child)]:pl-1 [&_:is(th,td):not(:last-child)]:pr-1 w-full">
          <thead className="text-left [&_th]:typo-detail-md [&_th]:py-2 sticky top-0 bg-[white]">
            <tr className="border-b border-muted">
              <th>Estate ID</th>
              <th>Type</th>
              <th>Created at</th>
              <th>Source</th>
              <th>Broker</th>
              <th>Department</th>
              <th>Sent</th>
              {/* <th>Status</th> */}
            </tr>
          </thead>
          <tbody>
            {leads.map((lead) => (
              <tr
                key={lead.id}
                className="border-b border-muted [&_td]:py-2 [&_td]:typo-body-sm"
              >
                <td className="flex items-center gap-1">
                  <TextButton
                    target="_blank"
                    href={
                      isVoa(lead.source)
                        ? `https://megler.nordvikbolig.no/verdivurdering/${lead.estate_id}`
                        : `https://megler.nordvikbolig.no/oppdragsavtale/${lead.estate_id}`
                    }
                  >
                    {lead.estate_id.slice(0, 5)}
                  </TextButton>
                  <SquareArrowOutUpRightIcon className="size-2 -mt-1 ink-muted" />
                </td>
                <td className="px-4 py-2">
                  <Badge
                    variant={getLeadTypeBadgeVariant(lead.lead_type)}
                    size="lg"
                  >
                    {formatLeadType(lead.lead_type)}
                  </Badge>
                </td>
                <td>
                  {lead.created_at ? formatDate(lead.created_at) : 'No date'}
                </td>
                <td className="px-4 py-2">
                  <Badge variant={getSourceBadgeVariant(lead.source)} size="lg">
                    {transformSource(lead.source)}
                  </Badge>
                </td>
                <Suspense
                  fallback={
                    <>
                      <td>
                        <span>...</span>
                      </td>
                      <td>
                        <span>...</span>
                      </td>
                    </>
                  }
                  key={lead.estate_id}
                >
                  <BrokerWithImage estateId={lead.estate_id} />
                </Suspense>
                <td>
                  <div className="flex items-center gap-2">
                    {lead.success ? '✅' : '⛔️'}
                    {lead.comment && (
                      <TooltipProvider delayDuration={0}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button className="p-1 hover:bg-interactive-muted rounded">
                              <MessageCircleIcon className="size-4 ink-muted cursor-pointer" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs whitespace-pre-wrap">
                            {lead.comment
                              .replaceAll('"', '')
                              .split('\\n')
                              .map((line, index) => (
                                <div key={index}>{line || '\u00A0'}</div>
                              ))}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

async function BrokerWithImage({ estateId }: { estateId: string }) {
  const broker = await retry(() => getBrokerForEstate(estateId))

  return (
    <>
      <td className="px-4 py-2">
        <div className="flex items-center gap-1">
          <img
            src={broker.image}
            alt={broker.name}
            className="w-6 h-6 rounded-full"
          />
          <span>{broker.name}</span>
        </div>
      </td>
      <td>{broker.department}</td>
    </>
  )
}

function isVoa(source: string | null) {
  return (
    source?.includes('verdivurdering') ||
    source?.includes('e-takst') ||
    source?.includes('etakst')
  )
}

function transformSource(source: string | null) {
  if (!source) return 'Unknown'
  if (source.includes('salgsprosess')) return 'Salgsprosess'
  if (isVoa(source)) return 'Verdivurdering'
  if (source.includes('oppdragsavtale')) return 'Oppdragsavtale'
  return source
}

function formatLeadType(leadType: string) {
  return leadType.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}

function getLeadTypeBadgeVariant(leadType: string) {
  if (leadType.includes('inspection')) return 'blue'
  if (leadType.includes('valuation')) return 'beige'
  if (leadType.includes('contact')) return 'grey'
  if (leadType.includes('viewing')) return 'light-green'
  return 'bright-green'
}

function getSourceBadgeVariant(source: string | null) {
  if (!source) return 'grey'
  if (source.includes('salgsprosess')) return 'bright-green'
  if (source.includes('etakst') || source.includes('verdivurdering'))
    return 'beige'
  if (source.includes('oppdragsavtale')) return 'blue'
  return 'grey'
}
