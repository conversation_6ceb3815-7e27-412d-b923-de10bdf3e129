import { useQueryClient } from '@tanstack/react-query'
import {
  MoreHorizontalIcon,
  PinIcon,
  PinOffIcon,
  UsersRoundIcon,
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@nordvik/ui/table'

import { impersonate } from '@/actions/impersonate'
import { UserAvatar } from '@/components/user-avatar'

import { EmployeeEntry } from '../page'
import { usePinnedBrokers } from '../use-pinned-brokers'

const MAX_ROWS = 30

export function TableOfBrokers({ brokers }: { brokers: EmployeeEntry[] }) {
  const [showAll, setShowAll] = React.useState(false)
  const { pinnedBrokers, addPinn, removePinn } = usePinnedBrokers()

  const queryClient = useQueryClient()
  const router = useRouter()

  async function impersonateEmployee(employeeId: string) {
    await impersonate(employeeId)
    queryClient.invalidateQueries()
    router.push('/')
  }

  return (
    <>
      <Table className="w-full">
        <TableHeader>
          <TableRow inset>
            <TableHead className="min-w-[60px]" />
            <TableHead className="max-w-[150px]">Navn</TableHead>
            <TableHead className="hidden md:table-cell">E-post</TableHead>
            <TableHead className="hidden md:table-cell">Avdeling</TableHead>
            <TableHead className="hidden md:table-cell">
              <p className="w-max">ID</p>
            </TableHead>

            <TableHead />
          </TableRow>
        </TableHeader>
        <TableBody>
          {brokers
            .slice(0, showAll ? brokers.length : MAX_ROWS)
            .map((employee) => {
              const isPinned = pinnedBrokers.some(
                (b) => b.employeeId === employee.employeeId,
              )
              return (
                <TableRow
                  inset
                  key={employee.id}
                  className={cn({ 'ink-muted': !employee.employeeActive })}
                >
                  <TableCell className="flex-center">
                    <UserAvatar
                      className="size-9"
                      user={{
                        name: employee.name ?? '',
                        image:
                          employee.image?.small ?? '/placeholder-avatar.jpg',
                      }}
                    />
                  </TableCell>
                  <TableCell className="typo-label-md">
                    <div className="flex items-center gap-2">
                      {employee.name}
                      {!employee.employeeActive ? (
                        <div className="typo-label-sm bg-root-muted px-2 py-0.5 rounded-full ink-[black]">
                          Inaktiv
                        </div>
                      ) : null}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <Link href={`mailto:${employee.email}`}>
                      {employee.email}
                    </Link>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {employee.department?.departmentId} -{' '}
                    {employee.department?.name}
                  </TableCell>
                  <TableCell className="typo-label-md hidden md:table-cell">
                    {employee.employeeId}
                  </TableCell>

                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          iconOnly={<MoreHorizontalIcon />}
                          variant="ghost"
                          size="lg"
                        >
                          Handlinger
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            impersonateEmployee(employee.employeeId)
                          }
                        >
                          <UsersRoundIcon className="size-4" /> Opptre som{' '}
                          {employee.name}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(event) => {
                            event.preventDefault()
                            if (
                              !employee.name ||
                              !employee.image?.small ||
                              !employee.department?.name
                            ) {
                              throw new Error(
                                'Employee name, image or department is missing',
                              )
                            }

                            if (isPinned) {
                              removePinn(employee.employeeId)
                            } else {
                              addPinn({
                                employeeId: employee.employeeId,
                                name: employee.name,
                                image: employee.image.small,
                                department: employee.department.name,
                              })
                            }
                          }}
                        >
                          {isPinned ? (
                            <PinOffIcon className="size-4" />
                          ) : (
                            <PinIcon className="size-4" />
                          )}
                          {isPinned
                            ? 'Fjern fra hurtigvalg'
                            : 'Legg til hurtigvalg'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
        </TableBody>
      </Table>
      {brokers.length > MAX_ROWS && (
        <Button
          variant="outline"
          onClick={() => setShowAll(!showAll)}
          size="lg"
        >
          {showAll ? 'Vis færre' : 'Vis alle'}
        </Button>
      )}
    </>
  )
}
