import { addDays, format, startOfDay } from 'date-fns'
import { CalendarIcon } from 'lucide-react'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@nordvik/ui/card'

import mongo from '@/db/mongo'

interface UpcomingTakeoverCardProps {
  days?: number // Number of days to look ahead (default: 7)
}

interface TakeoverEstate {
  takeOverDate: string
}

async function getUpcomingTakeovers(
  days: number = 7,
): Promise<TakeoverEstate[]> {
  try {
    await mongo.connect()
  } catch (error) {
    console.warn('MongoDB connection error:', error)
    return []
  }

  const today = startOfDay(new Date())
  const endDate = addDays(today, days)

  try {
    const estates = await mongo
      .db('nordvik')
      .collection('estates')
      .find({
        takeOverDate: {
          $gte: today,
          $lte: endDate,
        },
      })
      .project({
        takeOverDate: 1,
      })
      .toArray()

    return estates.map((estate) => ({
      takeOverDate: estate.takeOverDate,
    }))
  } catch (error) {
    console.error('Error fetching upcoming takeovers:', error)
    return []
  }
}

function getDaysUntilTakeover(takeOverDate: string): number {
  const today = startOfDay(new Date())
  const takeover = startOfDay(new Date(takeOverDate))
  return Math.ceil(
    (takeover.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
  )
}

export async function UpcomingTakeoverCard({
  days = 7,
}: UpcomingTakeoverCardProps) {
  const upcomingTakeovers = await getUpcomingTakeovers(days)

  const totalCount = upcomingTakeovers.length

  // Count estates for each of the next 5 days
  const dailyCounts = Array.from({ length: 5 }, (_, index) => {
    const dayCount = upcomingTakeovers.filter(
      (estate) => getDaysUntilTakeover(estate.takeOverDate) === index,
    ).length

    const date = addDays(startOfDay(new Date()), index)
    let label = ''
    if (index === 0) label = 'I dag'
    else if (index === 1) label = 'I morgen'
    else {
      const dayNames = ['Søn', 'Man', 'Tir', 'Ons', 'Tor', 'Fre', 'Lør']
      label = dayNames[date.getDay()]
    }

    return {
      count: dayCount,
      label,
      date: format(date, 'dd.MM'),
    }
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="w-5 h-5" />
          Kommende Overtakelser
        </CardTitle>
        <CardDescription>
          Fordeling av overtakelser de neste 5 dagene
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary stats for next 5 days */}
          <div className="grid grid-cols-5 gap-2 p-4 bg-root-muted rounded-lg border border-stroke-muted">
            {dailyCounts.map((day, index) => (
              <div key={index} className="text-center">
                <div className="typo-display-xs">{day.count}</div>
                <div className="typo-detail-sm ink-muted">{day.label}</div>
                <div className="typo-detail-sm ink-muted text-xs">
                  {day.date}
                </div>
              </div>
            ))}
          </div>

          {/* Total count */}
          <div className="text-center p-3 bg-root rounded-lg border border-stroke-muted">
            <div className="typo-display-sm">{totalCount}</div>
            <div className="typo-body-sm ink-muted">
              Totalt de neste {days} dagene
            </div>
          </div>

          {/* Empty state message */}
          {totalCount === 0 && (
            <div className="text-center py-8 ink-muted">
              <CalendarIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>Ingen planlagte overtakelser de neste {days} dagene</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
