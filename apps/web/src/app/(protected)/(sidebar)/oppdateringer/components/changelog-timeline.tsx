import Image from 'next/image'
import { Fragment } from 'react'

import { cn } from '@nordvik/theme/cn'
import { SimpleLabel } from '@nordvik/ui/simple-label'

import { GQLCmsChangelogsQuery } from '@/api/generated-client'
import { renderModule } from '@/components/articles/article-content'
import { formatDate } from '@/lib/dates'

export function ChangelogsTimeline({
  changelogs,
  isLoading,
}: {
  changelogs?: GQLCmsChangelogsQuery['cmsChangelogs']['items']
  isLoading: boolean
}) {
  const items = changelogs ?? []
  return (
    <div className="space-y-[6.25rem]">
      {items.map((changelog, index) => (
        <TimelineItem
          key={changelog.slug + index}
          slug={changelog.slug}
          date={changelog.postDate}
          author={changelog.author}
          title={changelog.title}
          isLoading={isLoading}
          category={changelog.categories.at(0)?.title}
          image={changelog.image?.large}
        >
          {changelog.modules?.map((entry, index) => (
            <Fragment key={index}>{renderModule(entry)}</Fragment>
          ))}
        </TimelineItem>
      ))}
    </div>
  )
}

type TimelineItemProps = {
  date?: string
  author?: GQLCmsChangelogsQuery['cmsChangelogs']['items'][0]['author']
  title?: string
  children: React.ReactNode
  slug: string
  isLoading: boolean
  category?: string
  image?: string
}

const TimelineItem = ({
  date,
  title,
  children,
  slug,
  isLoading,
  category,
  image,
}: TimelineItemProps) => {
  const formattedDate = date ? formatDate(date, 'd. MMMM yyyy') : null

  return (
    <div
      className="grid grid-cols-1 md:grid-cols-[minmax(150px,2fr)_10fr] w-full md:gap-10 relative z-0"
      id={slug}
      aria-busy={isLoading}
    >
      <div className="md:sticky top-4 h-fit bg-root max-md:flex max-md:mb-2 gap-2">
        <p
          className={cn(
            'typo-body-sm ink-subtle ',
            isLoading && 'masked-placeholder-text',
          )}
        >
          {formattedDate}
        </p>

        {category && (
          <SimpleLabel color="outline" size="sm" className="w-max md:mt-2">
            {category}
          </SimpleLabel>
        )}
      </div>

      <ChangelogItemContent title={title} image={image} isLoading={isLoading}>
        {children}
      </ChangelogItemContent>
    </div>
  )
}

export function ChangelogItemContent({
  title,
  image,
  isLoading,
  children,
}: {
  title?: string
  image?: string
  isLoading?: boolean
  children: React.ReactNode
}) {
  return (
    <div className="max-w-[725px]">
      {title && (
        <h3
          className={cn(
            'typo-title-md mb-2 inline-block',
            isLoading &&
              'masked-placeholder-text max-w-full whitespace-pre-wrap',
          )}
        >
          {title}
        </h3>
      )}
      {image && (
        <div className="w-full h-auto rounded-md overflow-hidden mt-4 mb-6">
          <Image
            src={image}
            alt={title ?? ''}
            className="w-full h-full object-cover"
            width={1000}
            height={700}
          />
        </div>
      )}
      <div
        className={cn(
          isLoading &&
            '[&_p]:masked-placeholder-text [&_li]:masked-placeholder-text max-w-full',
        )}
      >
        {children}
      </div>
    </div>
  )
}
