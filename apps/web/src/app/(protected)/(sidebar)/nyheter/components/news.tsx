'use client'

import first from 'lodash/first'
import { useQueryState } from 'nuqs'

import { Button } from '@nordvik/ui/button'

import {
  type GQLArticleCategoryFragment,
  GQLCmsArticleType,
  useCmsArticleCategoriesQuery,
  useInfiniteCmsArticlesQuery,
} from '@/api/generated-client'
import { ArticlesList } from '@/components/articles/articles-list/articles-list'
import { ArticlesSkeleton } from '@/components/articles/articles-list/articles-skeleton'
import { EmptyState } from '@/components/empty-state'
import { useSearchQuery } from '@/hooks/useSearchQuery'
import { useUserContext } from '@/lib/UserContext'

import { ArticleFilter } from './news-filter'

const DEFAULT_LIMIT = 10

export default function Articles() {
  const { data: categoriesData } = useCmsArticleCategoriesQuery({
    type: GQLCmsArticleType.News,
  })
  const { user } = useUserContext()
  const [categorySlug] = useQueryState('kategori', {
    defaultValue: 'alle',
  })
  const [searchQuery] = useSearchQuery()
  const showAllCategories = categorySlug === 'alle'
  const { data, hasNextPage, isFetchingNextPage, isLoading, fetchNextPage } =
    useInfiniteCmsArticlesQuery(
      {
        categorySlug: showAllCategories ? undefined : categorySlug,
        limit: DEFAULT_LIMIT,
        page: 1,
        searchQuery,
        type: GQLCmsArticleType.News,
        userId: user?.id,
        includeViewerHasRead: true,
      },

      {
        initialPageParam: { page: 1 },
        getNextPageParam: ({ cmsArticles }) => {
          const currentPage = cmsArticles.meta.currentPage
          const totalPages = cmsArticles.meta.totalPages
          const nextPage = currentPage + 1

          if (nextPage > totalPages) {
            return null
          }

          return {
            page:
              cmsArticles.meta.currentPage < cmsArticles.meta.totalPages
                ? Number(cmsArticles.meta.currentPage) + 1
                : null,
          }
        },
      },
    )

  const articles = data?.pages.flatMap((page) => page.cmsArticles.items) ?? []
  const total = first(data?.pages)?.cmsArticles.meta.total ?? 0

  const hasMore = total > DEFAULT_LIMIT && hasNextPage

  if (isLoading)
    return (
      <ListLayout categories={categoriesData?.cmsArticleCategories}>
        <ArticlesSkeleton />
      </ListLayout>
    )

  if (total > 0)
    return (
      <ListLayout categories={categoriesData?.cmsArticleCategories}>
        <div className="flex flex-col gap-4">
          <ArticlesList articles={articles} source="/nyheter" />
        </div>
        {hasMore && (
          <Button
            variant="tertiary"
            onClick={() => fetchNextPage()}
            loading={isFetchingNextPage}
            size="lg"
          >
            Last flere
          </Button>
        )}
      </ListLayout>
    )

  if (searchQuery)
    return (
      <ListLayout categories={categoriesData?.cmsArticleCategories}>
        <EmptyState
          className="mt-10"
          illustration="no-search-results"
          description={`Søket på «${searchQuery}» ga ingen treff ${showAllCategories ? '' : 'i denne kategorien'}`}
        />
      </ListLayout>
    )

  return (
    <ListLayout categories={categoriesData?.cmsArticleCategories}>
      <EmptyState
        className="mt-10"
        illustration="no-search-results"
        description={`Det ser ut som det ikke er noen nyheter for øyeblikket ${showAllCategories ? '' : 'i denne kategorien'}`}
      />
    </ListLayout>
  )
}

function ListLayout({
  children,
  categories,
}: {
  children: React.ReactNode
  categories?: GQLArticleCategoryFragment[]
}) {
  return (
    <div className="mb-16 flex w-full flex-col gap-8">
      <div className="flex flex-col gap-4">
        <ArticleFilter categories={categories} />

        {children}
      </div>
    </div>
  )
}
