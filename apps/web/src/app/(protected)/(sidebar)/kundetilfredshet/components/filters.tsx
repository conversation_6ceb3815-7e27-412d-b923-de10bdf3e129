import { startOfMonth } from 'date-fns'
import { AnimatePresence, motion } from 'framer-motion'
import { CalendarIcon, Settings2Icon, XIcon } from 'lucide-react'
import { useQueryStates } from 'nuqs'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Calendar } from '@nordvik/ui/calendar'
import { Checkbox } from '@nordvik/ui/checkbox'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@nordvik/ui/drawer'
import { Label } from '@nordvik/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'
import { selectTriggerVariants } from '@nordvik/ui/select'

import {
  GQLCurrentBrokerRatingsTotal,
  GQLSortDirection,
  useBrokerRatingsTotalQuery,
} from '@/api/generated-client'
import { Stars } from '@/components/stars'
import { formatDate } from '@/lib/dates'

import { RatingsCount } from './ratings-count'
import { Period } from './types'
import { RATING_QUERY_STATES, useDateRange } from './utils'

const placeholderRatingsTotal: GQLCurrentBrokerRatingsTotal = {
  allDates: 20,
  allRatings: 20,
  fiveStar: 20,
  fourStar: 20,
  threeStar: 20,
  twoStar: 20,
  oneStar: 20,
  currentYear: 20,
  lastYear: 20,
  last30Days: 20,
  withReview: 20,
}

export function Filters({ onClear }: { onClear: () => void }) {
  const [filters] = useQueryStates(RATING_QUERY_STATES, {
    clearOnDefault: true,
  })

  const { fra, periode, til, harAnmeldelse, fremhevet, vurdering, sortDir } =
    filters

  const { from, to } = useDateRange({ from: fra, to: til, periode })
  const { data, isLoading } = useBrokerRatingsTotalQuery(
    {
      dateFrom: from && to ? from : undefined,
      dateTo: from && to ? to : undefined,
      hasReview: harAnmeldelse || null,
      featured: fremhevet || null,
      rating: vurdering || null,
    },
    {
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60,
    },
  )
  const initialLoading = useRef(true)
  const [ratingsTotal, setRatingsTotal] = useState<
    GQLCurrentBrokerRatingsTotal | undefined
  >(placeholderRatingsTotal)

  useEffect(() => {
    if (!isLoading && data) {
      setRatingsTotal(data.currentBrokerRatingsTotal)
      initialLoading.current = false
    }
  }, [isLoading, data])

  const hasFilters = Boolean(
    fra ||
      til ||
      periode ||
      harAnmeldelse ||
      fremhevet ||
      vurdering ||
      sortDir === GQLSortDirection.Asc,
  )

  return (
    <div className="flex flex-col gap-6">
      <div>
        <div className="flex justify-between items-baseline mb-2">
          <p className="typo-body-md font-medium">Vis</p>
          <AnimatePresence>
            {hasFilters && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  variant="unstyled"
                  size="sm"
                  iconEnd={<XIcon />}
                  onClick={onClear}
                  className="typo-body-xs ink-subtle font-normal h-max py-0.5 -mr-4 max-md:hidden hover:ink-default"
                >
                  Nullstill
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <CustomFilters
          ratingsTotal={ratingsTotal}
          isLoading={initialLoading.current}
        />
      </div>
      <DateFilters
        ratingsTotal={ratingsTotal}
        isLoading={initialLoading.current}
      />
      <StarFilters
        ratingsTotal={ratingsTotal}
        isLoading={initialLoading.current}
      />
      <SortFilters />
    </div>
  )
}

function FilterGroup({
  title,
  children,
}: {
  title?: string
  children: React.ReactNode
}) {
  return (
    <div className="flex flex-col gap-2">
      {title && <p className="typo-body-md font-medium">{title}</p>}
      {children}
    </div>
  )
}

function FilterItem({
  label,
  children,
  total,
  loading,
  className,
}: {
  label?: string
  children: React.ReactNode
  total?: number
  loading?: boolean
  className?: string
}) {
  return (
    <Label className={cn('flex items-center gap-2', className)}>
      {children}
      {label && <span className="typo-body-md">{label}</span>}
      {typeof total === 'number' ? (
        <div
          aria-busy={loading}
          className={cn(
            'ml-auto typo-body-xs ink-muted',
            loading && 'masked-placeholder-text',
            total === 0 && 'opacity-50',
          )}
        >
          {total || 0}
        </div>
      ) : null}
    </Label>
  )
}

export function DrawerFilters({
  filtersCount,
  clearFilters,
  currentFilterTotal,
}: {
  filtersCount: number
  clearFilters: () => void
  currentFilterTotal: number
}) {
  return (
    <Drawer>
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="rounded-md font-normal"
          iconStart={<Settings2Icon />}
        >
          <span>
            Filter{' '}
            {filtersCount > 0 && (
              <span className="ink-subtle">({filtersCount})</span>
            )}
          </span>
        </Button>
      </DrawerTrigger>

      <DrawerContent className="pb-0">
        <DrawerHeader className="pt-3 px-6 pb-0">
          <DrawerTitle className="flex justify-between">
            <div className="flex items-baseline gap-1">Filter</div>
            <RatingsCount currentFilterTotal={currentFilterTotal} />
          </DrawerTitle>
        </DrawerHeader>
        <div className="py-4 px-6 overflow-y-auto">
          <Filters onClear={clearFilters} />
        </div>
        <DrawerFooter className="px-6 py-4 my-0 flex flex-row gap-2.5">
          <Button
            variant="outline"
            onClick={clearFilters}
            disabled={!filtersCount}
            className="grow"
            size="lg"
          >
            Nullstill
          </Button>
          <DrawerClose asChild>
            <Button className="grow" size="lg">
              Vis resultat
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

function CustomFilters({
  ratingsTotal,
  isLoading,
}: {
  ratingsTotal?: GQLCurrentBrokerRatingsTotal
  isLoading: boolean
}) {
  const [{ fremhevet, harAnmeldelse }, setQuery] = useQueryStates(
    RATING_QUERY_STATES,
    {
      clearOnDefault: true,
    },
  )

  return (
    <FilterGroup>
      <FilterItem label="Vises på min profil">
        <Checkbox
          checked={Boolean(fremhevet)}
          onCheckedChange={(checked) =>
            setQuery({ fremhevet: Boolean(checked) })
          }
        />
      </FilterItem>
      <FilterItem
        label="Kun med omtale"
        total={ratingsTotal?.withReview}
        loading={isLoading}
      >
        <Checkbox
          checked={Boolean(harAnmeldelse)}
          onCheckedChange={(checked) =>
            setQuery({ harAnmeldelse: Boolean(checked) })
          }
        />
      </FilterItem>
    </FilterGroup>
  )
}

function DateFilters({
  ratingsTotal,
  isLoading,
}: {
  ratingsTotal?: GQLCurrentBrokerRatingsTotal
  isLoading: boolean
}) {
  const [{ fra, til, periode }, setQuery] = useQueryStates(RATING_QUERY_STATES)

  const handleSelectDate = (value: Period) => {
    if (value === Period.CUSTOM) {
      setQuery({ fra: null, til: null, periode: value })
      return
    }

    if (value === Period.UNTIL_NOW) {
      setQuery({ fra: null, til: null, periode: null })
    } else {
      setQuery({ fra: null, til: null, periode: value })
    }
  }

  return (
    <RadioGroup onValueChange={handleSelectDate} value={periode ?? ''}>
      <FilterGroup title="Periode">
        <FilterItem
          label="Alle"
          total={ratingsTotal?.allDates}
          loading={isLoading}
        >
          <RadioGroupItem
            value={Period.UNTIL_NOW}
            checked={fra === null && til === null && periode === null}
          />
        </FilterItem>
        <FilterItem
          label="I år"
          total={ratingsTotal?.currentYear}
          loading={isLoading}
        >
          <RadioGroupItem value={Period.CURRENT_YEAR} />
        </FilterItem>
        <FilterItem
          label="I fjor"
          total={ratingsTotal?.lastYear}
          loading={isLoading}
        >
          <RadioGroupItem value={Period.LAST_YEAR} />
        </FilterItem>
        <FilterItem
          label="Siste 30 dager"
          total={ratingsTotal?.last30Days}
          loading={isLoading}
        >
          <RadioGroupItem value={Period.LAST_30_DAYS} />
        </FilterItem>
        <FilterItem label="Egendefinert" className="max-md:hidden">
          <RadioGroupItem value={Period.CUSTOM} />
        </FilterItem>
        <AnimatePresence>
          {periode === Period.CUSTOM && (
            <Popover>
              <motion.div
                key="custom-date"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.1, ease: 'linear' }}
                className="overflow-hidden"
              >
                <PopoverTrigger
                  className={cn(
                    'min-w-max w-full max-md:hidden',
                    selectTriggerVariants({ variant: 'fill' }),
                    'py-1.5 ',
                  )}
                >
                  <CalendarIcon className="mr-2 size-4" />
                  {fra && til
                    ? `${formatDate(fra, 'dd MMM')} - ${formatDate(til, 'dd MMM')}`
                    : 'Velg dato'}
                </PopoverTrigger>
              </motion.div>
              <PopoverContent align="start" className="w-auto overflow-y-auto">
                <Calendar
                  defaultMonth={startOfMonth(fra ? new Date(fra) : new Date())}
                  initialFocus
                  mode="range"
                  className="px-4 ink-default"
                  disabled={{ after: new Date() }}
                  numberOfMonths={2}
                  onSelect={(value) =>
                    setQuery({
                      fra: value?.from?.toISOString() ?? '',
                      til: value?.to?.toISOString() ?? '',
                    })
                  }
                  selected={{
                    from: fra ? new Date(fra) : undefined,
                    to: til ? new Date(til) : undefined,
                  }}
                />
              </PopoverContent>
            </Popover>
          )}
        </AnimatePresence>
      </FilterGroup>
    </RadioGroup>
  )
}

function StarFilters({
  ratingsTotal,
  isLoading,
}: {
  ratingsTotal?: GQLCurrentBrokerRatingsTotal
  isLoading: boolean
}) {
  const [{ vurdering }, setQuery] = useQueryStates(RATING_QUERY_STATES)

  return (
    <RadioGroup
      onValueChange={(value) =>
        setQuery({ vurdering: value === 'all' ? null : parseInt(value) })
      }
      value={vurdering?.toString() ?? ''}
    >
      <FilterGroup title="Vurderinger">
        <FilterItem
          label="Alle"
          total={ratingsTotal?.allRatings}
          loading={isLoading}
        >
          <RadioGroupItem value="all" checked={vurdering === null} />
        </FilterItem>
        <FilterItem total={ratingsTotal?.fiveStar} loading={isLoading}>
          <RadioGroupItem value="5" />
          <Stars value={5} />
        </FilterItem>
        <FilterItem total={ratingsTotal?.fourStar} loading={isLoading}>
          <RadioGroupItem value="4" />
          <Stars value={4} />
        </FilterItem>
        <FilterItem total={ratingsTotal?.threeStar} loading={isLoading}>
          <RadioGroupItem value="3" />
          <Stars value={3} />
        </FilterItem>
        <FilterItem total={ratingsTotal?.twoStar} loading={isLoading}>
          <RadioGroupItem value="2" />
          <Stars value={2} />
        </FilterItem>
        <FilterItem total={ratingsTotal?.oneStar} loading={isLoading}>
          <RadioGroupItem value="1" />
          <Stars value={1} />
        </FilterItem>
      </FilterGroup>
    </RadioGroup>
  )
}

function SortFilters() {
  const [{ sortDir }, setQuery] = useQueryStates(RATING_QUERY_STATES, {
    clearOnDefault: true,
  })
  return (
    <RadioGroup
      onValueChange={(value: GQLSortDirection) => setQuery({ sortDir: value })}
      defaultValue={GQLSortDirection.Desc}
      value={sortDir ?? GQLSortDirection.Desc}
    >
      <FilterGroup title="Sortering">
        <FilterItem label="Nyeste først">
          <RadioGroupItem value={GQLSortDirection.Desc} />
        </FilterItem>
        <FilterItem label="Eldste først">
          <RadioGroupItem value={GQLSortDirection.Asc} />
        </FilterItem>
      </FilterGroup>
    </RadioGroup>
  )
}
