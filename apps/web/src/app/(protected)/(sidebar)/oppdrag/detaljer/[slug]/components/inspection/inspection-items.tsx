'use client'

import { ListingAgreementResponse } from '@befaring/actions/types'
import { BudgetProvider } from '@befaring/context/budget-context'
import UnlockDialog from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/unlock-dialog'
import {
  AgreementMissingDataValues,
  validateMissingValues,
} from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/utils'
import {
  FileTextIcon,
  InfoIcon,
  MoreHorizontalIcon,
  PresentationIcon,
} from 'lucide-react'
import Link from 'next/link'
import React, { ReactNode } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Badge, BadgeProps } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'
import {
  Popover,
  PopoverContent,
  PopoverPortal,
  PopoverTrigger,
} from '@nordvik/ui/popover'
import { TextButton } from '@nordvik/ui/text-button'

import { NextPrivateContact } from '@/actions/next/types-next'
import {
  GQLAgreementAndInspectionQuery,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { EditInspectionSheet } from '@/components/edit-inspection/edit-inspection-sheet'
import { formatDate } from '@/lib/dates'
import { joinNames } from '@/lib/join-names'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

import { InteractionsWrapper } from './interactions-wrapper'
import { StartSigningButton } from './start-signing-button'

export function InspectionItems({
  estate,
  listingAgreementBudget,
  sellers,
  isLoading,
}: {
  estate: GQLAgreementAndInspectionQuery['estate']
  listingAgreementBudget?: ListingAgreementResponse | null
  sellers?: NextPrivateContact[]
  isLoading?: boolean
}) {
  return (
    <div className="space-y-6 pt-3 my-6">
      <PresentationItem isLoading={isLoading} estate={estate} />
      <ListingAgreementItem
        isLoading={isLoading}
        estate={estate}
        listingAgreementBudget={listingAgreementBudget}
        sellers={sellers}
      />
    </div>
  )
}

function PresentationItem({
  isLoading,
  estate,
}: {
  isLoading?: boolean
  estate: GQLAgreementAndInspectionQuery['estate']
}) {
  if (!estate) return null
  const inspectionSharedAt = estate?.inspectionFolder?.sentAt

  return (
    <div className={cn('flex items-center gap-2')}>
      <div
        className={cn(
          'size-12 flex-center rounded-md bg-light-green-subtle',
          isLoading && 'opacity-50',
        )}
      >
        <PresentationIcon />
      </div>
      <div className={cn('grow', isLoading && 'masked-placeholder-text')}>
        <p className={cn('typo-body-md font-medium')}>Presentasjon</p>
        <Badge
          className={cn(
            'inline-flex @lg/inspection:hidden',
            isLoading && 'masked-placeholder-text',
          )}
          variant={inspectionSharedAt ? 'blue' : 'grey'}
        >
          {inspectionSharedAt
            ? formatDate(inspectionSharedAt, "'Delt' d. MMM")
            : 'Ikke delt'}
        </Badge>
      </div>
      <Badge
        className={cn(
          'hidden @lg/inspection:inline-flex',
          isLoading && 'masked-placeholder-text',
        )}
        variant={inspectionSharedAt ? 'blue' : 'grey'}
      >
        {inspectionSharedAt
          ? formatDate(inspectionSharedAt, "'Delt' d. MMM")
          : 'Ikke delt'}
      </Badge>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isLoading}>
          <Button variant="ghost" size="sm" iconOnly={<MoreHorizontalIcon />} />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <EditInspectionSheet
            estateId={estate?.estateId}
            isValuation={Boolean(estate.isValuation)}
            trigger={
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                Rediger presentasjon
              </DropdownMenuItem>
            }
          />
          <DropdownMenuItem asChild>
            <Link href={`/oppdragsavtale/${estate?.estateId}/start`}>
              Vis presentasjon
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export function ListingAgreementItem({
  isLoading,
  estate,
  listingAgreementBudget,
  sellers,
}: {
  isLoading?: boolean
  estate: GQLAgreementAndInspectionQuery['estate']
  listingAgreementBudget?: ListingAgreementResponse | null
  sellers?: NextPrivateContact[]
}) {
  if (!estate) return null

  const { listingAgreement } = estate

  const listingAgreementSentToClientAt =
    estate.inspectionFolder?.listingAgreementSentAt ||
    listingAgreement?.sentToClientAt

  const isCreated = !!listingAgreement?.createdAt
  const missingValues =
    listingAgreementBudget && sellers && !estate.isValuation
      ? validateMissingValues(listingAgreementBudget, sellers, estate)
      : []

  const hasMissingValues = missingValues.length > 0

  const allFields: AgreementMissingDataValues[] = [
    'ansvarlig megler',
    'selgere',
    'prisantydning',
    'provisjon',
    'budsjett',
  ]

  const checklist: { label: AgreementMissingDataValues; checked: boolean }[] =
    allFields.map((field) => ({
      label: field,
      checked: !missingValues.includes(field),
    }))

  const signingStartedAt = listingAgreement?.initiatedSigningAt
  const signedAt = listingAgreement?.signedAt
  const hasExpired =
    listingAgreement?.status === GQLListingAgreementStatus.Expired
  let badgeText = 'Ikke delt'
  let badgeVariant: BadgeProps['variant'] = 'grey'
  if (signedAt) {
    badgeText = formatDate(signedAt, "'Signert' d. MMM")
    badgeVariant = 'bright-green'
  } else if (hasExpired) {
    badgeText = 'Utgått'
    badgeVariant = 'red'
  } else if (
    signingStartedAt &&
    estate.listingAgreement?.status === GQLListingAgreementStatus.Signing
  ) {
    badgeText = formatDate(signingStartedAt, "'Signering startet' d. MMM")
    badgeVariant = 'beige'
  } else if (listingAgreementSentToClientAt) {
    badgeText = formatDate(listingAgreementSentToClientAt, "'Delt' d. MMM")
    badgeVariant = 'blue'
  }

  if (isLoading) {
    badgeText = 'Ikke delt'
    badgeVariant = 'grey'
  }

  const lockedStatuses = [
    GQLListingAgreementStatus.Signing,
    GQLListingAgreementStatus.Signed,
    GQLListingAgreementStatus.PartialSigned,
    GQLListingAgreementStatus.Expired,
    GQLListingAgreementStatus.SignedViaNext,
  ]

  const isAgreementLocked = Boolean(
    listingAgreement?.status &&
      lockedStatuses.includes(listingAgreement?.status),
  )

  return (
    <div className={cn('flex items-center gap-2')}>
      <div
        className={cn(
          'size-12 flex-center self-start rounded-md bg-light-green-subtle shrink-0',
          hasMissingValues && 'bg-root-muted',
          isLoading && 'opacity-50',
        )}
      >
        <FileTextIcon className={cn(hasMissingValues && 'ink-muted')} />
      </div>
      <div className="grow">
        <p
          className={cn(
            'typo-body-md font-medium',
            hasMissingValues && 'ink-muted',
            isLoading && 'masked-placeholder-text',
          )}
        >
          {estate.isValuation
            ? 'Oppdragsavtale og presentasjon'
            : 'Oppdragsavtale'}
        </p>
        {!hasMissingValues && (
          <Badge
            className={cn(
              'inline-flex @lg/inspection:hidden',
              isLoading && 'masked-placeholder-text',
            )}
            variant={badgeVariant}
          >
            {badgeText}
          </Badge>
        )}
        {hasMissingValues && (
          <p>
            <span className="ink-muted ">
              Mangler {joinNames(missingValues)}{' '}
            </span>
            <Popover>
              <PopoverTrigger>
                <InfoIcon className="size-4 -mb-0.5" />
              </PopoverTrigger>
              <PopoverPortal>
                <PopoverContent className="max-w-56 space-y-1">
                  <p className="typo-label-md">
                    For å bruke oppdragsavtalen må du først fylle ut dette i
                    Next:
                  </p>
                  <ul>
                    {checklist.map((item) => (
                      <li key={item.label} className="flex items-center gap-2">
                        {item.checked ? (
                          <div className="size-2 rounded-full border border-success-emphasis bg-stroke-success-emphasis" />
                        ) : (
                          <div className="size-2 rounded-full border border-danger-emphasis" />
                        )}
                        <p
                          className={cn(
                            'first-letter:capitalize typo-body-md',
                            !item.checked && 'ink-danger',
                          )}
                        >
                          {item.label}
                        </p>
                      </li>
                    ))}
                  </ul>
                </PopoverContent>
              </PopoverPortal>
            </Popover>
          </p>
        )}
      </div>
      {hasMissingValues && (
        <TextButton
          href={estate?.linkToNext ?? 'https://nop.vitecnext.no/'}
          target="_blank"
          iconEnd="external"
        >
          Gå til Next
        </TextButton>
      )}
      {!hasMissingValues && (
        <Badge
          className={cn(
            'hidden @lg/inspection:inline-flex',
            isLoading && 'masked-placeholder-text',
          )}
          variant={badgeVariant}
        >
          {badgeText}
        </Badge>
      )}

      {signedAt && !isLoading && (
        <InteractionsWrapper estateId={estate.estateId} signersData={sellers} />
      )}

      {!hasMissingValues && (
        <ItemDropdownMenu
          estate={estate}
          listingAgreementBudget={listingAgreementBudget}
          sellers={sellers}
          signedAt={signedAt}
          hasExpired={hasExpired}
          isAgreementLocked={isAgreementLocked}
          isCreated={isCreated}
          disabled={isLoading}
        />
      )}
    </div>
  )
}

function AbortSigning({
  listingAgreementBudget,
  estate,
}: {
  listingAgreementBudget?: ListingAgreementResponse | null
  estate?: GQLAgreementAndInspectionQuery['estate']
}) {
  if (!listingAgreementBudget || !estate) return null
  return (
    <BudgetProvider
      listingAgreementData={listingAgreementBudget}
      estate={estate}
    >
      <UnlockDialog
        trigger={(isLoading: boolean) => (
          <Button
            disabled={isLoading}
            loading={isLoading}
            variant="unstyled-for-real"
            className="px-0 h-auto"
            size="sm"
          >
            Avbryt signering
          </Button>
        )}
      />
    </BudgetProvider>
  )
}

function ItemDropdownMenu({
  estate,
  listingAgreementBudget,
  sellers,
  signedAt,
  hasExpired,
  isAgreementLocked,
  isCreated,
  disabled,
}: {
  estate: NonNullable<GQLAgreementAndInspectionQuery['estate']>
  listingAgreementBudget?: ListingAgreementResponse | null
  sellers?: NextPrivateContact[]
  signedAt?: string
  hasExpired: boolean
  isAgreementLocked: boolean
  isCreated: boolean
  disabled?: boolean
}) {
  const items: ReactNode[] = []

  const href = estate?.isValuation
    ? `/verdivurdering/${estate?.estateId}`
    : `/oppdragsavtale/${estate?.estateId}`

  if (isCreated) {
    items.push(
      <DropdownMenuItem asChild>
        <Link href={href}>Vis oppdragsavtale</Link>
      </DropdownMenuItem>,
    )
  } else if (estate?.status === 0) {
    items.push(
      <DropdownMenuItem asChild>
        <Link href={href}>Lag oppdragsavtale</Link>
      </DropdownMenuItem>,
    )
  }

  if (signedAt && estate.linkToNext) {
    items.push(
      <DropdownMenuItem asChild>
        <Link
          href={getLinkToObjectTabInNext(estate.linkToNext, 'documents')}
          target="_blank"
        >
          Vis dokument i Next
        </Link>
      </DropdownMenuItem>,
    )
  }

  if (!isAgreementLocked && estate?.listingAgreement?.canStartSigning) {
    items.push(
      <DropdownMenuItem
        className="empty:hidden"
        onSelect={(event) => event.preventDefault()}
      >
        <StartSigningButton
          estate={estate}
          listingAgreementBudget={listingAgreementBudget}
          sellersData={sellers}
          triggerProps={{
            variant: 'unstyled-for-real',
            className: 'p-0 typo-body-sm h-auto',
            size: 'md',
          }}
        />
      </DropdownMenuItem>,
    )
  }

  if (
    estate?.listingAgreement?.status === GQLListingAgreementStatus.Signing ||
    hasExpired
  ) {
    items.push(
      <DropdownMenuItem onSelect={(event) => event.preventDefault()}>
        <AbortSigning
          listingAgreementBudget={listingAgreementBudget}
          estate={estate}
        />
      </DropdownMenuItem>,
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled || items.length === 0}>
        <Button
          disabled={disabled || items.length === 0}
          variant="ghost"
          size="sm"
          iconOnly={<MoreHorizontalIcon />}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {items.map((item, index) => (
          <React.Fragment key={index}>{item}</React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
