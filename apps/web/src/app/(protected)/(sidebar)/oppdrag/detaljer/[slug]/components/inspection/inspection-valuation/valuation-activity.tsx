'use client'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import {
  useActivitySummaryQuery,
  useEstateInteractionsAndPageVisitsQuery,
} from '@/api/generated-client'

import { ActivitySection } from '../activity/activity-section'
import { ActivitySummary } from '../activity/activity-summary'
import { Interactions } from '../activity/interactions'
import { getAgreementPhase } from '../utils'

export function ValuationActivity({
  estateId,
  signersData,
  disabled,
}: {
  estateId: string
  signersData?: NextPrivateContactWithProxy[]
  disabled?: boolean
}) {
  const { data: interactionData, isLoading: isInteractionLoading } =
    useEstateInteractionsAndPageVisitsQuery(
      { estateId, includeSubPages: true },
      {
        refetchInterval: 10000,
      },
    )

  const agreementPhase = getAgreementPhase(
    interactionData?.listingAgreementByEstateId?.status,
  )

  const { data, isLoading: isActivityLoading } = useActivitySummaryQuery(
    { estateId },
    { refetchInterval: 5000 },
  )

  const summary = data?.activitySummary

  const hasActivity = summary?.visitsCount
  const sharedAt = Boolean(
    interactionData?.listingAgreementByEstateId?.sentToClientAt,
  )

  const isLoading = isInteractionLoading || isActivityLoading

  return (
    <ActivitySection>
      {hasActivity || isLoading ? (
        <div>
          <ActivitySummary
            summary={summary}
            isLoading={isLoading}
            phase={agreementPhase!}
          />
        </div>
      ) : (
        <NoActivity sharedAt={sharedAt} />
      )}
      <Interactions
        estateId={estateId}
        signersData={signersData}
        isLoading={isLoading}
        pageVisits={interactionData?.pageVisits}
        buttonProps={{
          disabled: disabled,
          size: 'sm',
        }}
      />
    </ActivitySection>
  )
}

function NoActivity({ sharedAt }: { sharedAt: boolean }) {
  if (sharedAt) {
    return (
      <div className="typo-body-md">
        <p className="font-medium">Ingen besøk</p>
        <p className="ink-muted">Selger har ikke åpnet lenken ennå</p>
      </div>
    )
  }

  return (
    <div className="typo-body-md">
      <p className="font-medium">Oppdragsavtale ikke delt</p>
      <p className="ink-muted">Del med selger for å se aktivitet </p>
    </div>
  )
}
