'use client'

import { ListingAgreementResponse } from '@befaring/actions/types'
import { BudgetProvider } from '@befaring/context/budget-context'
import { SendContractDialog } from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/send-contract-dialog'

import { cn } from '@nordvik/theme/cn'
import { ButtonProps } from '@nordvik/ui/button'

import { NextPrivateContact } from '@/actions/next/types-next'
import { GQLAgreementAndInspectionQuery } from '@/api/generated-client'

import { InspectionBankIdButton } from '../inspection-bank-id-button'

import { AgreementPhase, getAgreementPhase } from './utils'

export function StartSigningButton({
  listingAgreementBudget,
  estate,
  sellersData,
  triggerProps,
  brokerSignerUrl,
}: {
  listingAgreementBudget?: ListingAgreementResponse | null
  estate?: GQLAgreementAndInspectionQuery['estate']
  sellersData?: NextPrivateContact[]
  triggerProps?: ButtonProps
  brokerSignerUrl?: string
}) {
  const agreementPhase = getAgreementPhase(estate?.listingAgreement?.status)
  if (!listingAgreementBudget || !sellersData || !estate) return null

  const showSigningBtn =
    brokerSignerUrl && agreementPhase === AgreementPhase.Signing

  const canStartSigning = Boolean(estate?.inspectionFolder?.publishedAt)

  const { className, ...restProps } = triggerProps ?? {}

  return (
    <BudgetProvider
      listingAgreementData={listingAgreementBudget}
      estate={estate}
    >
      {showSigningBtn ? (
        <InspectionBankIdButton href={brokerSignerUrl} />
      ) : canStartSigning ? (
        <>
          <SendContractDialog
            estate={estate}
            sellers={sellersData}
            triggerProps={{
              ...restProps,
              size: 'sm',
              className: cn('flex @lg/inspection:hidden', className),
            }}
            buttonText="Start signering"
          />
          <SendContractDialog
            estate={estate}
            sellers={sellersData}
            triggerProps={{
              ...restProps,
              size: 'lg',
              className: cn('hidden @lg/inspection:flex', className),
            }}
            buttonText="Start signering"
          />
        </>
      ) : null}
    </BudgetProvider>
  )
}
