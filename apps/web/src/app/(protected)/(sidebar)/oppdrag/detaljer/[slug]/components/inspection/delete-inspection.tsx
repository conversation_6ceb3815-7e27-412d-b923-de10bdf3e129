'use client'

import { Trash2Icon } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'

export function DeleteInspection({
  estateId,
  redirectTo,
  deleteInspection,
}: {
  estateId: string
  redirectTo: string
  deleteInspection: (estateId: string) => Promise<boolean>
}) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [open, setOpen] = useState(false)

  const handleDeleteInspection = async () => {
    setIsDeleting(true)
    const success = await deleteInspection(estateId)

    if (success) {
      router.push(redirectTo)
      return
    }

    setIsDeleting(false)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="ghost" iconOnly={<Trash2Icon />} />
      </DialogTrigger>
      <DialogContent title="Slett befaringsmappe" size="md">
        <DialogDescription className="flex flex-col gap-2">
          <p>
            Du er i ferd med å slette befaringsmappen og alle tilhørende data
            permanent. Denne handlingen kan ikke angres.
          </p>
          <p>
            Følgende informasjon vil bli slettet: presentasjonen,
            oppdragsavtalen, sporing av sidebesøk, interaksjoner i
            oppdragsavtalen og tilgangen for selgere.
          </p>
          <p>
            Dette er en permanent sletting. Du kan opprette en ny befaringsmappe
            senere hvis nødvendig.
          </p>
        </DialogDescription>
        <DialogFooter>
          <DialogClose asChild>
            <Button size="md" variant="ghost">
              Avbryt
            </Button>
          </DialogClose>
          <Button
            size="md"
            variant="default"
            onClick={handleDeleteInspection}
            loading={isDeleting}
          >
            Slett befaringsmappe
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
