'use client'

import { ChartColumnIcon } from 'lucide-react'
import { useMemo } from 'react'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { usePageVisitsQuery } from '@/api/generated-client'

import { Interactions } from './activity/interactions'

export function InteractionsWrapper({
  estateId,
  signersData,
}: {
  estateId: string
  signersData?: NextPrivateContactWithProxy[]
}) {
  const { data, isLoading } = usePageVisitsQuery(
    { estateId, includeSubPages: true },
    {
      refetchInterval: 10000,
    },
  )

  const pageVisits = useMemo(() => data?.pageVisits ?? [], [data?.pageVisits])

  return (
    <Interactions
      estateId={estateId}
      isLoading={isLoading}
      pageVisits={pageVisits}
      signersData={signersData}
      buttonProps={{
        iconOnly: <ChartColumnIcon />,
        variant: 'ghost',
        size: 'sm',
      }}
    />
  )
}
