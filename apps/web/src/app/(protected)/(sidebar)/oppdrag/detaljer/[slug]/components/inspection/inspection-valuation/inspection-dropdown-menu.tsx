'use client'

import { MoreHorizontalIcon } from 'lucide-react'
import React, { ReactNode } from 'react'

import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'
import { Link } from '@nordvik/ui/global-navigation-progress/link'

import { NextPrivateContact } from '@/actions/next/types-next'
import {
  GQLAgreementAndInspectionQuery,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { ListingAgreementResponse } from '@/app/(befaring)/actions/types'
import { BudgetProvider } from '@/app/(befaring)/context/budget-context'
import UnlockDialog from '@/app/(befaring)/oppdragsavtale/[estateId]/(agreement)/components/unlock-dialog'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

import { StartSigningButton } from '../start-signing-button'

export function InspectionDropdownMenu({
  estate,
  listingAgreementBudget,
  sellers,
  signedAt,
  hasExpired,
  isAgreementLocked,
  isCreated,
  disabled,
}: {
  estate: NonNullable<GQLAgreementAndInspectionQuery['estate']>
  listingAgreementBudget?: ListingAgreementResponse | null
  sellers?: NextPrivateContact[]
  signedAt?: string
  hasExpired: boolean
  isAgreementLocked: boolean
  isCreated: boolean
  disabled?: boolean
}) {
  const items: ReactNode[] = []

  const href = `/verdivurdering/${estate?.estateId}`

  if (isCreated) {
    items.push(
      <DropdownMenuItem asChild>
        <Link href={href}>Vis oppdragsavtale</Link>
      </DropdownMenuItem>,
    )

    items.push(
      <DropdownMenuItem asChild>
        <Link href={`${href}/din-bolig`}>Vis presentasjon</Link>
      </DropdownMenuItem>,
    )
  }

  if (signedAt && estate.linkToNext) {
    items.push(
      <DropdownMenuItem asChild>
        <Link
          href={getLinkToObjectTabInNext(estate.linkToNext, 'documents')}
          target="_blank"
        >
          Vis dokument i Next
        </Link>
      </DropdownMenuItem>,
    )
  }

  if (!isAgreementLocked && estate?.listingAgreement?.canStartSigning) {
    items.push(
      <DropdownMenuItem
        className="empty:hidden"
        onSelect={(event) => event.preventDefault()}
      >
        <StartSigningButton
          estate={estate}
          listingAgreementBudget={listingAgreementBudget}
          sellersData={sellers}
          triggerProps={{
            variant: 'unstyled-for-real',
            className: 'p-0 typo-body-sm h-auto',
            size: 'md',
          }}
        />
      </DropdownMenuItem>,
    )
  }

  // Server action
  if (
    estate?.listingAgreement?.status === GQLListingAgreementStatus.Signing ||
    hasExpired
  ) {
    items.push(
      <DropdownMenuItem onSelect={(event) => event.preventDefault()}>
        <AbortSigning
          listingAgreementBudget={listingAgreementBudget}
          estate={estate}
        />
      </DropdownMenuItem>,
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled || items.length === 0}>
        <Button
          disabled={disabled || items.length === 0}
          variant="ghost"
          size="sm"
          iconOnly={<MoreHorizontalIcon />}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {items.map((item, index) => (
          <React.Fragment key={index}>{item}</React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

function AbortSigning({
  listingAgreementBudget,
  estate,
}: {
  listingAgreementBudget?: ListingAgreementResponse | null
  estate?: GQLAgreementAndInspectionQuery['estate']
}) {
  if (!listingAgreementBudget || !estate) return null
  return (
    <BudgetProvider
      listingAgreementData={listingAgreementBudget}
      estate={estate}
    >
      <UnlockDialog
        trigger={(isLoading: boolean) => (
          <Button
            disabled={isLoading}
            loading={isLoading}
            variant="unstyled-for-real"
            className="px-0 h-auto"
            size="sm"
          >
            Avbryt signering
          </Button>
        )}
      />
    </BudgetProvider>
  )
}
