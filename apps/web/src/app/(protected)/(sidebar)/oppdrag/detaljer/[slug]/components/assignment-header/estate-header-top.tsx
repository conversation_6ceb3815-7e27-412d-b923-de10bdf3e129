'use client'

import { RefreshCwIcon } from 'lucide-react'
import React from 'react'

import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import { TextButton } from '@nordvik/ui/text-button'

import type { GQLEstateByIdQuery } from '@/api/generated-client'
import { NextSyncEstate } from '@/components/next-sync-estate'

import {
  getBadgeVariantForStatus,
  getStatusNameByNumber,
  isActiveOnFinn,
} from '../../../../util'

export function EstateHeaderTop({
  estate,
}: {
  estate: NonNullable<GQLEstateByIdQuery['estate']>
}) {
  const activeOnFinn = React.useMemo(
    () => isActiveOnFinn(estate.finn),
    [estate.finn],
  )
  const address = estate.address?.streetAddress
  const formattedAddress = address ? encodeURIComponent(address) : undefined

  const googleMapsUrl = formattedAddress
    ? `https://www.google.com/maps/search/?api=1&query=${formattedAddress}`
    : undefined

  return (
    <div className="flex flex-col items-start pb-4" data-theme="dark">
      <Badge
        variant={getBadgeVariantForStatus(
          estate.status,
          activeOnFinn,
          estate.isValuation,
          estate.isEtakstPublished,
        )}
      >
        {getStatusNameByNumber(estate.status, activeOnFinn, estate.isValuation)}
      </Badge>
      <div className="flex justify-between gap-4 w-full">
        <h1 className="typo-display-md">{estate.address?.streetAddress}</h1>
        <NextSyncEstate
          estateId={estate.estateId}
          name={estate.address?.streetAddress}
        >
          <Button variant="outline" size="sm" iconOnly={<RefreshCwIcon />}>
            Oppdater oppdrag fra Next
          </Button>
        </NextSyncEstate>
      </div>
      <div className="flex w-full items-center gap-2">
        {estate.linkToNext && (
          <>
            <TextButton size="lg" href={estate.linkToNext} target="_blank">
              Vis i Next
            </TextButton>
            <DotSeparator />
          </>
        )}
        <TextButton size="lg" href={googleMapsUrl} target="_blank">
          Kart
        </TextButton>
        <DotSeparator />
      </div>
    </div>
  )
}

function DotSeparator() {
  return (
    <div className="first:hidden last:hidden flex-center">
      <div className="size-1 rounded-full bg-ink-muted" />
    </div>
  )
}
