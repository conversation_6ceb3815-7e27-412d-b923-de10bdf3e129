import { ListingAgreementResponse } from '@befaring/actions/types'
import { MoreHorizontalIcon, PencilLineIcon, RotateCcwIcon } from 'lucide-react'
import { Session } from 'next-auth'

import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'

import { NextPrivateContact } from '@/actions/next/types-next'
import {
  GQLAgreementAndInspectionQuery,
  GQLListingAgreementStatus,
} from '@/api/generated-client'
import { decodeBase64 } from '@/lib/base64'

import { InspectionBankIdButton } from '../../inspection-bank-id-button'
import { StartSigningButton } from '../start-signing-button'

import { MenuItemWithCopy } from './menu-item-with-copy'
import { SendOfferButton } from './send-offer-button'

export function CallToAction({
  isLoading,
  listingAgreementBudget,
  isCurrent<PERSON>roker<PERSON>igner,
  brokerSigner,
  sellersData,
  estate,
  isSignedValuation,
  user,
}: {
  isLoading?: boolean
  estate: NonNullable<GQLAgreementAndInspectionQuery['estate']>
  listingAgreementBudget?: ListingAgreementResponse | null
  isCurrentBrokerSigner: boolean
  brokerSigner?: NonNullable<
    NonNullable<GQLAgreementAndInspectionQuery['estate']>['listingAgreement']
  >['signers'][0]
  sellersData?: NextPrivateContact[]
  isSignedValuation: boolean
  user?: Session['user'] | null
}) {
  const isSignedByBroker = Boolean(brokerSigner?.signedAt)

  const decodedBrokerSignerId = brokerSigner?.externalSignerId
    ? decodeBase64(brokerSigner?.externalSignerId)
    : null

  const listingAgreementStatus = estate?.listingAgreement?.status

  const documentSignedByAll =
    estate?.listingAgreement?.status === GQLListingAgreementStatus.Signed

  if (isLoading) {
    return (
      <Button disabled className="ml-auto" loading size="lg">
        Del med kunde
      </Button>
    )
  }

  if (isSignedValuation) {
    return (
      <div className="ml-auto">
        <SendOfferButton
          listingAgreementBudget={listingAgreementBudget}
          estate={estate}
          sellersData={sellersData}
          isLoading={isLoading}
          hasSigningButton={true}
          buttonText="Del på nytt"
          buttonIcon={<RotateCcwIcon />}
        />
      </div>
    )
  }

  if (documentSignedByAll) {
    return <Badge variant="bright-green">Dokument signert</Badge>
  }

  if (
    GQLListingAgreementStatus.Signing === listingAgreementStatus &&
    !isSignedByBroker &&
    !isCurrentBrokerSigner
  ) {
    return (
      <>
        <Badge variant="grey">Ikke signert</Badge>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              iconOnly={<MoreHorizontalIcon />}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {brokerSigner?.url && (
              <MenuItemWithCopy url={brokerSigner.url}>
                Kopier lenken til signering
              </MenuItemWithCopy>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    )
  }

  if (isSignedByBroker) {
    return null
  }

  const currentBorkerCanSign =
    GQLListingAgreementStatus.Signing === listingAgreementStatus &&
    decodedBrokerSignerId === user?.employeeId

  if (currentBorkerCanSign) {
    return (
      <div className="flex justify-end w-full items-center gap-2">
        <ShowBefaringButton
          estateId={estate?.estateId}
          isValuation={Boolean(estate?.isValuation)}
        />
        <InspectionBankIdButton href={brokerSigner?.url} />
      </div>
    )
  }

  const showStartSigningButton = Boolean(
    estate?.inspectionFolder?.sentAt &&
      estate.listingAgreement?.canStartSigning &&
      estate.inspectionFolder.listingAgreementActive &&
      estate.status === 0,
  )

  return (
    <div className="flex items-center justify-end gap-2 w-full">
      <ShowBefaringButton
        estateId={estate?.estateId}
        isValuation={Boolean(estate?.isValuation)}
      />
      <SendOfferButton
        listingAgreementBudget={listingAgreementBudget}
        estate={estate}
        sellersData={sellersData}
        isLoading={isLoading}
        hasSigningButton={showStartSigningButton}
      />

      {showStartSigningButton && (
        <StartSigningButton
          listingAgreementBudget={listingAgreementBudget}
          estate={estate}
          sellersData={sellersData}
          brokerSignerUrl={brokerSigner?.url}
          triggerProps={{
            iconStart: <PencilLineIcon />,
            size: 'md',
          }}
        />
      )}
    </div>
  )
}

function ShowBefaringButton({
  estateId,
  isValuation,
}: {
  estateId?: string
  isValuation: boolean
}) {
  if (!estateId) return null
  const href = isValuation
    ? `/verdivurdering/${estateId}/start`
    : `/oppdragsavtale/${estateId}/start`
  return (
    <>
      <Button
        variant="outline"
        size="md"
        href={href}
        className="flex @lg/inspection:hidden"
      >
        Vis
      </Button>
      <Button
        variant="outline"
        href={href}
        className="hidden @lg/inspection:flex"
        size="lg"
      >
        Vis
      </Button>
    </>
  )
}
