import { addHours } from 'date-fns'
import { ClockIcon, Trash2Icon } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import { Calendar } from '@nordvik/ui/calendar'
import { Input } from '@nordvik/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@nordvik/ui/select'
import { TextButton } from '@nordvik/ui/text-button'

import { toZonedDate } from '@/lib/dates'

function formatTime(date: Date) {
  const zonedDate = toZonedDate(date)
  return zonedDate.toLocaleTimeString('nb-NO', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
  })
}

function getDuration(start: Date, end: Date) {
  const duration = end.getTime() - start.getTime()
  const hours = duration / (1000 * 60 * 60)
  // Round to nearest 10 minutes (1/6 of an hour)
  return Math.round(hours * 6) / 6
}

const DURATION_OPTIONS = [
  { value: '0.5', label: '30 minutter' },
  { value: '1', label: '1 time' },
  { value: '1.5', label: '1,5 time' },
  { value: '2', label: '2 timer' },
]

export function InspectionDateSelection({
  start,
  end,
  onSet,
  onRemove,
}: {
  start?: Date
  end?: Date
  onSet: (start: Date, end: Date) => Promise<void>
  onRemove?: () => void
}) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [date, setDate] = React.useState(start)
  const [time, setTime] = React.useState(start ? formatTime(start) : '17:00')
  const [duration, setDuration] = React.useState(
    start && end ? getDuration(start, end).toString() : '1',
  )

  const customDuration = !DURATION_OPTIONS.some(
    (option) => option.value === duration,
  )
  const options = !customDuration
    ? DURATION_OPTIONS
    : [
        ...DURATION_OPTIONS,
        {
          value: duration,
          label:
            Number(duration) < 1
              ? `${Math.round(Number(duration) * 60)} minutter`
              : `${duration} timer`,
        },
      ].sort((a, b) => a.value.localeCompare(b.value))

  const onSetHandler = async () => {
    if (!date) return null
    setIsLoading(true)
    const start = new Date(date)
    start.setHours(Number(time.split(':')[0]))
    start.setMinutes(Number(time.split(':')[1]))
    await onSet(start, addHours(start, Number(duration)))
    setIsLoading(false)
  }

  return (
    <div>
      <Calendar
        className="px-1.5 pt-3"
        mode="single"
        selected={date}
        onSelect={setDate}
        disabled={{ before: new Date() }}
      />
      <div className="grid grid-cols-2 gap-2 mt-3 px-3">
        <Input
          type="time"
          value={time}
          variant="fill"
          size="sm"
          onChange={(e) => setTime(e.target.value)}
          addonLeft={<ClockIcon />}
          className="[&::-webkit-calendar-picker-indicator]:hidden"
        />
        <Select defaultValue={duration} onValueChange={setDuration}>
          <SelectTrigger variant="fill" size="sm">
            <SelectValue placeholder="Velg tid" />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <hr className="border-muted mt-3" />
      <div className="flex gap-4 justify-between p-3">
        {onRemove && (
          <TextButton
            variant="destructive"
            size="lg"
            onClick={onRemove}
            bold
            subtle
            iconStart={<Trash2Icon />}
          >
            Fjern
          </TextButton>
        )}
        <Button
          disabled={!date}
          size="sm"
          onClick={onSetHandler}
          loading={isLoading}
          className="only:ml-auto"
        >
          {start ? 'Oppdater' : 'Legg til'}
        </Button>
      </div>
    </div>
  )
}
