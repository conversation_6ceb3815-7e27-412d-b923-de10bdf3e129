'use client'

import { ArrowUpR<PERSON>, MoreHorizontal, RotateCcw } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  DrawerTrigger,
} from '@nordvik/ui/drawer'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import { Separator } from '@nordvik/ui/separator'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import {
  type GQLBrokerEstate,
  useEstateResetFormMutation,
} from '@/api/generated-client'
import { CopyButton } from '@/components/copy-button'
import { isDevelopment } from '@/utils/isDevelopment'

export function FormItemActions({
  form,
  estateId,
}: {
  form: GQLBrokerEstate['forms'][number]
  estateId: string
}) {
  const { mutateAsync, isPending } = useEstateResetFormMutation()
  const { toast } = useToast()

  const handleResetForm = async () => {
    if (form.type) {
      try {
        if (!isDevelopment) {
          await mutateAsync({
            estateId,
            formType: form.type,
          })
        }
        toast({
          title: 'Skjemaet er nullstilt',
          variant: 'success',
        })
      } catch (error) {
        toast({
          title: 'Kunne ikke nullstille skjemaet',
          variant: 'destructive',
        })
      }
    }
  }
  return (
    <>
      <div className="ml-2 flex items-center gap-2 max-md:hidden">
        <TextButton onClick={handleResetForm} className="mr-2 px-0">
          Nullstill skjema
        </TextButton>
        {form.link ? <CopyButton value={form.link} size="sm" /> : null}
        {form.link ? (
          <Button
            href={form.link}
            size="sm"
            iconOnly={<ArrowUpRight />}
            variant="ghost"
          >
            Gå til skjema
          </Button>
        ) : null}
      </div>
      <div className={cn('md:hidden')}>
        <Drawer>
          <DrawerTrigger asChild>
            <Button variant="ghost" size="sm" iconOnly={<MoreHorizontal />}>
              Mer
            </Button>
          </DrawerTrigger>
          <DrawerContent>
            <DrawerHeader className="text-center">
              <h3 className="typo-title-sm">Befaringsmappe</h3>
              <p className="typo-body-sm ink-muted">{form.name}</p>
            </DrawerHeader>
            <Separator />
            <div className="px-2">
              {form.link ? (
                <Link href={form.link} className="w-full">
                  <Button
                    variant="unstyled"
                    className="typo-label-md w-full justify-start px-2"
                    iconStart={<ArrowUpRight />}
                    size="lg"
                  >
                    Gå til skjema
                  </Button>
                </Link>
              ) : null}
              <Button
                variant="unstyled"
                onClick={handleResetForm}
                className="typo-label-md w-full justify-start px-2"
                iconStart={<RotateCcw />}
                disabled={isPending}
                size="lg"
              >
                Nullstill skjema
              </Button>

              {form.link ? (
                <CopyButton
                  value={form.link}
                  buttonText="Kopier lenke til skjema"
                  className="typo-label-md w-full justify-start px-2"
                />
              ) : null}
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    </>
  )
}
