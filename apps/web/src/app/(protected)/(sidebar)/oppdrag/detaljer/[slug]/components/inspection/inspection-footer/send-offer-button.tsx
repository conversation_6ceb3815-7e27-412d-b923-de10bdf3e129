import { ListingAgreementResponse } from '@befaring/actions/types'
import { RotateCcwIcon, UploadIcon } from 'lucide-react'
import { ReactElement, useMemo } from 'react'

import { cn } from '@nordvik/theme/cn'

import { NextPrivateContact } from '@/actions/next/types-next'
import { GQLAgreementAndInspectionQuery } from '@/api/generated-client'
import {
  SendOfferDialog,
  SendOfferProps,
} from '@/components/send-offer-dialog/send-offer-dialog'

import { AgreementPhase, getAgreementPhase } from '../utils'

export function SendOfferButton({
  estate,
  sellersData,
  listingAgreementBudget,
  isLoading,
  hasSigningButton,
  buttonText,
  buttonIcon,
}: {
  estate: GQLAgreementAndInspectionQuery['estate']
  sellersData?: NextPrivateContact[]
  listingAgreementBudget?: ListingAgreementResponse | null
  isLoading?: boolean
  hasSigningButton?: boolean
  buttonText?: string
  buttonIcon?: ReactElement
}) {
  const hasBeenSent = estate?.inspectionFolder?.sentAt
  const agreementPhase = getAgreementPhase(estate?.listingAgreement?.status)

  const triggerClassName =
    hasBeenSent && hasSigningButton ? 'order-[-1] mr-auto' : undefined

  const variant = useMemo(() => {
    if (estate?.isValuation && agreementPhase === AgreementPhase.Signed) {
      return 'outline'
    }

    if (hasSigningButton) {
      return 'ghost'
    }

    if (estate?.isValuation && !hasSigningButton) {
      return 'default'
    }

    return 'default'
  }, [estate?.isValuation, hasSigningButton, agreementPhase])

  const defaultIcon =
    hasBeenSent && hasSigningButton ? <RotateCcwIcon /> : <UploadIcon />
  const triggerProps = {
    variant,
    loading: isLoading,
    iconStart: buttonIcon || defaultIcon,
    size: 'md',
  } satisfies SendOfferProps['triggerProps']

  const defaultTriggerText =
    hasBeenSent && hasSigningButton ? 'Del på nytt' : 'Del med kunde'
  const sendOfferProps = {
    estate,
    listingAgreementBudget,
    sellers: sellersData,
    isLoading,
    triggerText: buttonText || defaultTriggerText,
  }

  if (
    !estate?.inspectionFolder?.id ||
    !listingAgreementBudget?.listingAgreement?.id
  ) {
    return null
  }

  const hideOnSigning = agreementPhase === AgreementPhase.Signing
  const hideOnStatuses = ![0, 5].includes(estate.status)
  const exceptOnValuation = estate.isValuation && estate.status === 1
  if ((hideOnSigning || hideOnStatuses) && !exceptOnValuation) {
    return null
  }

  return (
    <>
      <SendOfferDialog
        {...sendOfferProps}
        triggerProps={{
          ...triggerProps,
          size: 'md',
          className: cn('flex @lg/inspection:hidden', triggerClassName),
        }}
      />
      <SendOfferDialog
        {...sendOfferProps}
        triggerProps={{
          ...triggerProps,
          size: 'lg',
          className: cn('hidden @lg/inspection:flex', triggerClassName),
        }}
      />
    </>
  )
}
