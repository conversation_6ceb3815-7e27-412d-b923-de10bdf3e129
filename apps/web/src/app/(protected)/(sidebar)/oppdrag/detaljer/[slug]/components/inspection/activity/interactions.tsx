'use client'

import { useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button, ButtonProps } from '@nordvik/ui/button'
import { Sheet } from '@nordvik/ui/sheet'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { Visit } from '@/lib/analytics/group-page-visits'

import { InteractionsDrawer } from '../interactions/interactions-drawer'

export function Interactions({
  estateId,
  signersData,
  isLoading,
  pageVisits,
  buttonProps = { size: 'sm' },
}: {
  estateId: string
  signersData?: NextPrivateContactWithProxy[]
  isLoading?: boolean
  buttonProps?: ButtonProps
  pageVisits?: Visit[]
}) {
  const [openDrawer, setOpenDrawer] = useState(false)

  return (
    <div>
      <div className="flex items-center gap-1">
        <Button
          onClick={() => setOpenDrawer(true)}
          variant="outline"
          className={cn(
            !isLoading && !estateId && 'hidden',
            !buttonProps.iconOnly &&
              'bg-[white] disabled:opacity-100 z-0 disabled:hover:bg-[white] hover:bg-[white]',
          )}
          {...buttonProps}
        >
          Se aktivitet
        </Button>
      </div>
      {estateId && (
        <Sheet open={openDrawer} onOpenChange={setOpenDrawer}>
          <InteractionsDrawer
            estateId={estateId}
            open={openDrawer}
            pageVisits={pageVisits}
            signersData={signersData}
          />
        </Sheet>
      )}
    </div>
  )
}
