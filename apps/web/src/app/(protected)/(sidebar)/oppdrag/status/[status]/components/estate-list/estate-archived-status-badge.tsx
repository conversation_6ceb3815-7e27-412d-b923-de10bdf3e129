'use client'

import { isPast } from 'date-fns'
import React from 'react'

import { Badge } from '@nordvik/ui/badge'

export function EstateArchivedStatusBadge({
  soldPrice,
  expireDate,
}: {
  soldPrice?: number | null
  expireDate?: string | null
}) {
  if (soldPrice) {
    return (
      <Badge variant="dark-green" size="lg">
        Solgt
      </Badge>
    )
  }

  if (expireDate && isPast(expireDate)) {
    return (
      <Badge variant="grey" size="lg">
        Utløpt
      </Badge>
    )
  }

  return null
}
