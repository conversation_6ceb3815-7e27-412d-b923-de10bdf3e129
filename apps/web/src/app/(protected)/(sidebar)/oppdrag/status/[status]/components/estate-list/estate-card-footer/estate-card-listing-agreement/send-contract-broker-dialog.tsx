'use client'

import {
  initiateSigning,
  retractInitiatedSigning,
} from '@befaring/actions/listing-agreement'
import { getSignersData } from '@befaring/actions/sellers'
import { handleSign } from '@befaring/actions/signing'
import { useBudget } from '@befaring/context/budget-context'
import { useValidateForm } from '@befaring/hooks/useValidateForm'
import { yupSellerSchema } from '@befaring/lib/check-valid-fields'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { CreatingDocumentLoader } from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/creating-document-loader'
import { useQueryClient } from '@tanstack/react-query'
import uniqBy from 'lodash/uniqBy'
import { PencilLineIcon } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { Button, ButtonProps } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { useToast } from '@nordvik/ui/toaster'

import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { flattenValidationErrors } from '@/app/(protected)/(sidebar)/oppdrag/util'
import { ErrorList } from '@/components/error-list'
import { UserAvatar } from '@/components/user-avatar'
import { useValidateListingAgreement } from '@/hooks/use-validate-listing-agreement'
import { useUserContext } from '@/lib/UserContext'
import { getCookie } from '@/utils/cookies'

import { contractIsBeingCreated } from '../../../../actions/is-being-created'

export function SendContractBrokerDialog({
  estate,
  sellers,
  triggerProps,
  buttonText,
}: {
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  sellers: NextPrivateContactWithProxy[]
  triggerProps?: ButtonProps
  buttonText?: string
}) {
  const trackEvent = useTrackListingAgreement(estate)
  const [loading, setLoading] = useState(false)
  const [showUploadingPopup, setShowUploadingPopup] = useState(false)
  const [uploadingPopupMessage, setUploadingPopupMessage] = useState(
    'Dette tar vanligvis opptil 10 sekunder, men kan ta lengre tid.',
  )
  const [openConfirmationDialog, setOpenConfirmationDialog] = useState(false)
  const { user } = useUserContext()
  const router = useRouter()
  const { toast } = useToast()
  const cid = getCookie('cid')
  const { validateListingAgreement } = useValidateListingAgreement(estate)
  const { validateSync: validateSellers } = useValidateForm<NextPrivateContact>(
    estate,
    yupSellerSchema,
    {
      fieldsTranslation: {
        contactId: 'kontakt-ID',
        socialSecurity: 'fødselsnummer',
        email: 'e-post',
        mobilePhone: 'telefonnummer',
        firstName: 'fornavn',
        lastName: 'etternavn',
        postalAddress: 'adresse',
        postalCode: 'postnummer',
        city: 'poststed',
      },
    },
  )

  const queryClient = useQueryClient()

  const {
    brokers,
    budget,
    listingAgreement,
    income,
    outlay,
    budgetDiscount,
    sellerInsurance,
    commission,
    budgetSum,
    marketingPackage,
    isUpdating,
    setDocumentId,
    valuation,
  } = useBudget()

  async function handleInitiateSigning() {
    setLoading(true)
    setShowUploadingPopup(true)
    if (!estate) {
      throw new Error('Missing estate')
    }

    const sellersData = await getSignersData({
      estateId: estate.estateId,
    })

    const sellersToCheck = [...sellersData]

    if (!sellersData.length) {
      return toast({
        title: 'Mangler informasjon om selgerne',
        variant: 'destructive',
      })
    }

    if (estate.hasCompanySeller) {
      const companySellersData = await getSignersData({
        estateId: estate.estateId,
        signersIfCompany: true,
      })

      sellersToCheck.push(...companySellersData)
    }

    const validationResults = uniqBy(sellersToCheck, 'contactId').map(
      (seller) => validateSellers(seller),
    )

    const errors = flattenValidationErrors(validationResults)
    if (errors.length > 0) {
      setLoading(false)
      setShowUploadingPopup(false)
      setOpenConfirmationDialog(false)
      return toast({
        title: 'Mangler informasjon om selgerne',
        description: <ErrorList errors={errors} />,
        variant: 'destructive',
      })
    }

    const initiated = await initiateSigning(listingAgreement.id).then(
      (response) => {
        if (response?.length) {
          return JSON.parse(response)
        }
      },
    )

    const signingWithRetry = async (triesLeft: number) => {
      try {
        if (initiated?.id) {
          const documentResponse = await handleSign({
            data: {
              sellers: sellersData,
              sellerInsurance,
              marketingPackage,
              brokers,
              estate,
              budget,
              commission,
              budgetSum,
              listingAgreement,
              income,
              outlay,
              budgetDiscount,
            },
            options: {
              skipInitialNotificationFor: Boolean(!user) && cid ? [cid] : [],
            },
          })

          setDocumentId(documentResponse?.documentId)
        } else {
          // if the signing was initiated but the documentId is missing, retry
          if (triesLeft <= 0) {
            throw new Error('Failed to get documentId')
          }
          return setTimeout(() => signingWithRetry(triesLeft - 1), 3000)
        }

        trackEvent('listing_agreement_send_to_sign')

        setUploadingPopupMessage('Ferdigstiller oppdragsavtalen')

        setShowUploadingPopup(false)

        queryClient.invalidateQueries({
          queryKey: ['estatesForBrokerById.infinite'],
        })
        setLoading(false)

        router.refresh()
      } catch (error) {
        setShowUploadingPopup(false)
        if (initiated) {
          void retractInitiatedSigning(listingAgreement.id)
        }
        console.error(error)
        toast({
          title: 'Det skjedde en feil ved generering av kontraktsdokumentet',
          variant: 'destructive',
        })
        setLoading(false)
      }
    }

    signingWithRetry(3)
  }

  const handleOnClick = async (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (!estate) {
        throw new Error('Missing estate')
      }

      try {
        const isBeingCreated = await contractIsBeingCreated(estate.estateId)

        if (isBeingCreated) {
          setLoading(false)
          return toast({
            title: 'Oppdragsavtalen er allerede under oppretting',
            variant: 'destructive',
          })
        }
      } catch (e) {
        console.warn('redis error', e)
      }

      const sellersData = await getSignersData({
        estateId: estate.estateId,
      })

      const sellerValidationResults = sellersData.map((seller) =>
        validateSellers(seller),
      )
      const sellersFlattenedErrors = flattenValidationErrors(
        sellerValidationResults,
      )

      const { errors: listingAgreementErrors } = validateListingAgreement({
        ...listingAgreement,
        commission: listingAgreement.commission ?? undefined,
        valuation: valuation?.post?.amountWithTaxIncluded ?? undefined,
      })

      if (
        listingAgreementErrors?.length ||
        sellersFlattenedErrors?.length ||
        !sellersData.length
      ) {
        const errors = listingAgreementErrors || []
        if (sellersFlattenedErrors?.length || !sellersData.length) {
          errors.unshift({
            path: 'seller-info',
            message: 'Mangler informasjon om selgerne',
            readableFieldName: 'selgerne',
          })
        }
        setLoading(false)
        return toast({
          title: 'Mangler informasjon',
          description: <ErrorList errors={errors} />,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Det skjedde en feil ved generering av kontraktsdokumentet',
        variant: 'destructive',
      })
    }

    setLoading(false)
    setOpenConfirmationDialog(true)
  }

  const uniqueSellers = uniqBy(
    sellers?.map((seller) => seller.proxy ?? seller),
    'contactId',
  )

  return (
    <>
      <CreatingDocumentLoader
        open={showUploadingPopup}
        onOpenChange={setShowUploadingPopup}
      >
        {uploadingPopupMessage}
      </CreatingDocumentLoader>
      <Dialog
        open={openConfirmationDialog && !loading}
        onOpenChange={setOpenConfirmationDialog}
      >
        <DialogTrigger asChild>
          <Button
            {...triggerProps}
            loading={loading || triggerProps?.loading}
            onClick={handleOnClick}
            tooltip="Starter signeringen av oppdragsavtalen med BankID. Selger og
              ansvarlig megler mottar en lenke på e-post og sms for å signere."
            size="lg"
          >
            {buttonText || 'Send til signering'}
          </Button>
        </DialogTrigger>
        <DialogContent
          title="Send til signering"
          subtitle="Oppdragsavtalen vil låses for endringer"
          size="lg"
        >
          <DialogDescription divider>
            {user ? (
              <div className="flex flex-col gap-2">
                <p>
                  {sellers.length > 1 ? 'Selgere' : 'Selger'} mottar lenke til
                  signering med BankID på e-post og sms. Ansvarlig megler mottar
                  lenke på e-post.
                </p>
                <div className="flex flex-col gap-2">
                  <p className="typo-label-md">
                    {sellers.length > 1 ? 'Mottakere' : 'Mottaker'}
                  </p>
                  {uniqueSellers?.map(({ firstName, lastName, contactId }) => (
                    <div className="flex items-center gap-2" key={contactId}>
                      <UserAvatar user={{ name: `${firstName} ${lastName}` }} />
                      <span className="typo-body-md">{`${firstName} ${lastName}`}</span>
                    </div>
                  ))}
                </div>
                <p className="ink-subtle">
                  Oppdragsavtalen sendes på SMS og e-post til selgere.
                </p>
              </div>
            ) : (
              <p>
                Dokumentet blir låst og du blir tatt til BankID for signering.
                Du kan ikke gjøre endringer i avtalen etter dette.
              </p>
            )}
          </DialogDescription>
          <DialogFooter>
            <DialogClose asChild>
              <Button size="md" variant="ghost">
                Avbryt
              </Button>
            </DialogClose>
            <Button
              size="md"
              onClick={handleInitiateSigning}
              loading={isUpdating}
              disabled={isUpdating}
              iconStart={<PencilLineIcon />}
            >
              Send til signering
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
