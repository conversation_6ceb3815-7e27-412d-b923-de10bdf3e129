'use client'

import first from 'lodash/first'
import type { Session } from 'next-auth'
import React from 'react'

import { Button } from '@nordvik/ui/button'

import {
  GQLEstateTabFilter,
  useInfiniteEstatesOverviewForBrokerByIdQuery,
} from '@/api/generated-client'
import { EmptyState } from '@/components/empty-state'
import { useSearchQuery } from '@/hooks/useSearchQuery'

import {
  ESTATE_LIST_DEFAULT_LIMIT,
  estateListStatusMap,
} from '../../../../util'

import { ArchiveTab } from './archive-tab'
import { EstateCard } from './estate-card'
import { EstateListLoading } from './estate-list-loading'
import type { EstateForBroker } from './types'
import { useCurrentTab } from './use-current-tab'

export default function EstateListForBroker({
  user,
  status,
}: {
  user: Session['user']
  status?: string
}) {
  const [search] = useSearchQuery()

  const { tab, isArchiveTab } = useCurrentTab(status)

  const tabs = estateListStatusMap[tab] ?? [GQLEstateTabFilter.Requested]
  const includeEtakstPublished = tabs.includes(GQLEstateTabFilter.Valuation)

  const { data, fetchNextPage, isLoading, isFetchingNextPage, hasNextPage } =
    useInfiniteEstatesOverviewForBrokerByIdQuery(
      {
        brokerId: user.employeeId,
        email: user.employeeId ? undefined : user.email,
        tabs,
        limit: ESTATE_LIST_DEFAULT_LIMIT,
        offset: 0,
        archived: isArchiveTab,
        search,
        // expensive prop, so we only want to include valuations if the tab is valuation
        includeEtakstPublished,
      },
      {
        staleTime: 8000,
        initialPageParam: {
          offset: 0,
        },
        enabled: Boolean(user.employeeId),
        getNextPageParam: (lastPage) => {
          const count = lastPage.estatesForBrokerById.pagination?.count ?? 0
          const offset = lastPage.estatesForBrokerById.pagination?.offset ?? 0
          if (count < ESTATE_LIST_DEFAULT_LIMIT) {
            return null
          }

          return {
            offset: offset + ESTATE_LIST_DEFAULT_LIMIT,
            disableCache: true,
          }
        },
      },
    )

  const estates = React.useMemo(() => {
    return data?.pages.flatMap((page) => page.estatesForBrokerById.items) ?? []
  }, [data])

  const handleFetchNextPage = async () => {
    await fetchNextPage()
  }

  if (estates.length === 0 && !isLoading && !isFetchingNextPage) {
    if (search) {
      return (
        <EmptyState
          className="mt-20"
          illustration="no-search-results"
          description={`Fant ingen oppdrag som matcher søket «${search}»`}
        />
      )
    }

    return (
      <EmptyState
        className="mt-20"
        illustration="no-data"
        description={`Ingen oppdrag i  «${tab}»`}
      />
    )
  }

  const total = first(data?.pages)?.estatesForBrokerById.pagination?.total ?? 0
  const hasMore = total > ESTATE_LIST_DEFAULT_LIMIT && hasNextPage

  return (
    <div className="my-10">
      <EstateListMain
        estates={estates}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        isArchiveTab={isArchiveTab}
      />
      {hasMore && (
        <Button
          variant="tertiary"
          className="mt-4 w-full"
          onClick={handleFetchNextPage}
          disabled={isLoading || isFetchingNextPage}
          size="lg"
        >
          Last flere
        </Button>
      )}
    </div>
  )
}

export function EstateListMain({
  estates,
  isLoading,
  isFetchingNextPage,
  isArchiveTab,
}: {
  estates: EstateForBroker[]
  isLoading: boolean
  isFetchingNextPage: boolean
  isArchiveTab: boolean
}) {
  return (
    <>
      {!isArchiveTab ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
          {estates.map((estate) => (
            <EstateCard key={estate.estateId} estate={estate} />
          ))}
          {(isLoading || isFetchingNextPage) && (
            <EstateListLoading numOfItems={ESTATE_LIST_DEFAULT_LIMIT} />
          )}
        </div>
      ) : (
        <ArchiveTab
          key={isLoading ? 'loading' : 'loaded'}
          estates={estates}
          isLoading={isLoading}
          isFetchingNextPage={isFetchingNextPage}
        />
      )}
    </>
  )
}
