'use client'

import { ChevronDown, ChevronUp, ChevronsUpDown } from 'lucide-react'

import { Button } from '@nordvik/ui/button'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import type { Column, ColumnDef } from '@nordvik/ui/table'

import { getSellersString } from '../../../util'

import { EstateArchivedStatusBadge } from './estate-list/estate-archived-status-badge'
import type { EstateForBroker } from './estate-list/types'

function SortingIcon({ column }: { column: Column<EstateForBroker> }) {
  const sortingDirection = column.getIsSorted()
  if (sortingDirection === 'desc') {
    return <ChevronDown className="ml-2 size-4" />
  } else if (sortingDirection === 'asc') {
    return <ChevronUp className="ml-2 size-4" />
  }
  return <ChevronsUpDown className="ml-2 size-4" />
}

const withSorting = (heading: string) =>
  function sorting({ column }: { column: Column<EstateForBroker> }) {
    return (
      <Button
        onClick={() => {
          column.toggleSorting()
        }}
        variant="unstyled"
        className="!typo-label-md px-0"
        size="lg"
      >
        {heading}

        <SortingIcon column={column} />
      </Button>
    )
  }

export const columns: ColumnDef<EstateForBroker, unknown>[] = [
  {
    accessorKey: 'address',
    id: 'address',
    header: withSorting('Adresser'),
    cell: ({ row }) => {
      return (
        <Link href={`/oppdrag/detaljer/${row.original.estateId}`}>
          <p className="typo-body-md py-4 text-[18px] font-medium">
            {row.original.address?.streetAddress}
          </p>
        </Link>
      )
    },
  },
  {
    accessorKey: 'sellers',
    header: withSorting('Kunde'),
    size: 200,
    maxSize: 200,
    cell: ({ row }) => {
      return (
        <Link href={`/oppdrag/detaljer/${row.original.estateId}`}>
          <p className="typo-body-sm py-4">{getSellersString(row.original)}</p>
        </Link>
      )
    },
  },
  {
    accessorKey: 'status',
    header: withSorting('Status'),
    maxSize: 70,
    size: 70,
    cell: ({ row }) => (
      <Link
        href={`/oppdrag/detaljer/${row.original.estateId}`}
        className="py-4"
      >
        <EstateArchivedStatusBadge
          soldPrice={row.original.estatePrice?.soldPrice}
          expireDate={row.original.expireDate}
        />
      </Link>
    ),
  },
  {
    accessorKey: 'assignmentNumber',
    header: () => (
      <span className="typo-label-md flex items-center">Oppdragsnummer</span>
    ),
    maxSize: 100,
    size: 100,
    cell: ({ row }) => {
      return (
        <Link href={`/oppdrag/detaljer/${row.original.estateId}`}>
          {row.original.assignmentNumber}
        </Link>
      )
    },
  },
]
