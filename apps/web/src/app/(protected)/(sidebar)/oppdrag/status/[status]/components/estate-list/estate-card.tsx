import { Badge, BadgeProps } from '@nordvik/ui/badge'
import { Card } from '@nordvik/ui/card'
import { Link } from '@nordvik/ui/global-navigation-progress/link'

import {
  getBadgeVariantForStatus,
  getStatusNameByNumber,
  isActiveOnFinn,
} from '../../../../util'

import { EstateCardFooter } from './estate-card-footer/estate-card-footer'
import EstateCardHeader from './estate-card-header'
import { EstateCardImage } from './estate-card-image'
import type { EstateForBroker } from './types'

export function EstateCard({ estate }: { estate: EstateForBroker }) {
  const activeOnFinn = isActiveOnFinn(estate.finn)
  const badgeContent = {
    text: getStatusNameByNumber(
      estate.status,
      activeOnFinn,
      estate.isValuation,
    ),
    variant: getBadgeVariantForStatus(
      estate.status,
      activeOnFinn,
      estate.isValuation,
      estate.isEtakstPublished,
    ),
  } as {
    text: string
    variant: BadgeProps['variant']
  }

  if (estate.isWithdrawn) {
    badgeContent.text = 'Tilbaketrukket'
    badgeContent.variant = 'light-green'
  }

  return (
    <Card
      key={estate.id}
      className="relative w-full cursor-pointer overflow-hidden group/estate-card"
    >
      <Link href={`/oppdrag/detaljer/${estate.estateId}`} prefetch>
        <Badge className="absolute left-4 top-4" variant={badgeContent.variant}>
          {badgeContent.text}
        </Badge>

        <EstateCardImage estate={estate} />
        <EstateCardHeader estate={estate} />
      </Link>
      <EstateCardFooter estate={estate} />
    </Card>
  )
}
