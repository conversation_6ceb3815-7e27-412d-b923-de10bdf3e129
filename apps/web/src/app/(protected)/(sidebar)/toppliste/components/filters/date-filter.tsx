'use client'

import { endOfMonth, isSameDay, startOfMonth } from 'date-fns'
import { CalendarIcon } from 'lucide-react'
import { Options, createParser, useQueryStates } from 'nuqs'
import { useEffect, useMemo, useRef, useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button, createButtonClassName } from '@nordvik/ui/button'
import { Calendar } from '@nordvik/ui/calendar'
import {
  type DateRange,
  getCurrentPreset,
} from '@nordvik/ui/date-picker-with-range-and-presets'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@nordvik/ui/drawer'
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from '@nordvik/ui/popover'
import { selectTriggerVariants } from '@nordvik/ui/select'

import { formatDate, toZonedDate } from '@/lib/dates'

import { Period, getFromTo } from '../util'

const options = [
  {
    label: 'I dag',
    value: getFromTo(Period.Today),
  },
  {
    label: 'I går',
    value: getFromTo(Period.Yesterday),
  },
  {
    label: 'Siste 7 dager',
    value: getFromTo(Period.Days7),
  },
  {
    label: 'Siste 30 dager',
    value: getFromTo(Period.Days30),
  },
  {
    label: 'Denne måneden',
    value: getFromTo(Period.Month),
  },
  {
    label: 'Forrige måned',
    value: getFromTo(Period.LastMonth),
  },
  {
    label: 'Hele året',
    value: getFromTo(Period.Year),
  },
]

const parseAsDate = createParser({
  parse: (v) => {
    const date = new Date(v)
    if (Number.isNaN(date.valueOf())) {
      return null
    }
    return toZonedDate(date)
  },
  serialize: (v) => (v ? formatDate(v, 'yyyy-MM-dd') : ''),
})

export function useDateFilter() {
  const now = toZonedDate(new Date())
  return useQueryStates(
    {
      from: parseAsDate.withDefault(startOfMonth(now)),
      to: parseAsDate.withDefault(endOfMonth(now)),
    },
    {
      shallow: true,
    },
  )
}

export default function DateFilter({
  onPeriodChange,
  align = 'start',
  withPresets = true,
  shallow = true,
}: {
  onPeriodChange?: (period: DateRange) => void
  align?: 'start' | 'end' | 'center'
  withPresets?: boolean
  shallow?: boolean
}) {
  const [period, _setPeriod] = useDateFilter()
  const [date, setDate] = useState<DateRange>(period)
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  const presetDefault = useMemo(() => getCurrentPreset(options, date), [date])

  const setPeriod = (value: DateRange, options?: Options) => {
    onPeriodChange?.(value)
    _setPeriod(value, options)
  }

  useEffect(() => {
    if (period !== date) {
      setDate(period)
    }
  }, [period, date])

  const calendarElRef = useRef<HTMLDivElement>(null)
  const scrollToRange = () => {
    calendarElRef.current
      ?.getElementsByClassName('day-range-start')
      .item(0)
      ?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
  const handleOnSelect = (value: DateRange | undefined) => {
    if (value && period !== value) {
      void setPeriod(value, { shallow: true })
    }
  }

  const title = 'Velg periode'

  const mainLabel = useMemo(() => {
    if (date.from) {
      if (date.to && !isSameDay(date.from, date.to)) {
        return `${formatDate(date.from, 'd. MMM')} - ${formatDate(date.to, 'd. MMM')}`
      }

      return formatDate(date.from, 'd. MMMM')
    }

    return title
  }, [date])

  return (
    <>
      <Popover>
        <PopoverTrigger
          className={cn(
            'min-w-max max-md:hidden',
            selectTriggerVariants({ variant: 'outline', size: 'md' }),
          )}
          disabled={!hasMounted}
          suppressHydrationWarning
        >
          <CalendarIcon className="mr-2 size-4" />
          {hasMounted ? mainLabel : ''}
        </PopoverTrigger>
        <PopoverContent align={align} className="w-auto">
          <div className="flex gap-6 space-y-2">
            {withPresets && (
              <div className="flex flex-col gap-2">
                {options.map((preset) => (
                  <PopoverClose asChild key={preset.label}>
                    <Button
                      className={cn(
                        createButtonClassName({
                          variant:
                            presetDefault === preset.label
                              ? 'default'
                              : 'outline',
                          size: 'sm',
                        }),
                        'w-fit',
                      )}
                      onClick={() => setPeriod(preset.value, { shallow })}
                      value={preset.label}
                      size="sm"
                    >
                      {preset.label}
                    </Button>
                  </PopoverClose>
                ))}
              </div>
            )}

            <div className="rounded-md" data-testid="date-picker-content">
              <Calendar
                defaultMonth={date.from}
                initialFocus
                mode="range"
                className="px-4 ink-default"
                disabled={{ after: new Date() }}
                numberOfMonths={2}
                onSelect={handleOnSelect}
                selected={date}
              />
            </div>
          </div>
          <div className="mt-2 flex justify-end border-t border-muted pt-2">
            <PopoverClose asChild>
              <Button size="sm" onClick={() => setPeriod(date, { shallow })}>
                Vis resultat
              </Button>
            </PopoverClose>
          </div>
        </PopoverContent>
      </Popover>

      <Drawer>
        <DrawerTrigger
          className={cn(
            selectTriggerVariants({ variant: 'fill' }),
            'min-w-max md:hidden',
          )}
        >
          <CalendarIcon className="mr-2 size-4" />
          {mainLabel}
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>{title}</DrawerTitle>
          </DrawerHeader>
          <div className="relative mx-2 mb-2  border-b border-b-muted pb-4 pt-2">
            <div className="flex gap-2 overflow-x-scroll pr-3 hide-scrollbar">
              {options.map((preset) => (
                <Button
                  variant={
                    presetDefault === preset.label ? 'default' : 'outline'
                  }
                  size="sm"
                  className="w-fit"
                  onClick={() => {
                    setPeriod(preset.value)
                    setTimeout(scrollToRange)
                  }}
                  key={preset.label}
                  value={preset.label}
                >
                  {preset.label}
                </Button>
              ))}
            </div>
            <div className="absolute inset-y-0 right-0 w-3 bg-gradient-to-r from-transparent" />
          </div>
          <div
            ref={calendarElRef}
            className="mx-auto max-h-[calc(70vh-50px)] w-full max-w-md overflow-y-scroll hide-scrollbar"
          >
            <Calendar
              defaultMonth={period.from}
              initialFocus
              mode="range"
              numberOfMonths={2}
              onSelect={handleOnSelect}
              disabled={{ after: new Date() }}
              selected={date}
              showOutsideDays={false}
              className="px-4 ink-default [&_td]:flex-1 [&_th]:flex-1"
            />
          </div>
          <DrawerFooter>
            <DrawerClose asChild>
              <Button onClick={() => setPeriod(date)} size="lg">
                Vis resultat
              </Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}
