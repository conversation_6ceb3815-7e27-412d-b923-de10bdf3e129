'use client'

import { ChevronDown } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  Drawer<PERSON>ooter,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@nordvik/ui/drawer'
import { Label } from '@nordvik/ui/label'
import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  selectTriggerVariants,
} from '@nordvik/ui/select'

export default function Filter<T extends string>({
  filterValue,
  setFilterValue,
  options,
  title,
  disabled,
  drawerTrigger,
  className,
  hasSelectedFilter,
}: {
  filterValue: T | undefined
  setFilterValue: (value: T) => void
  options: { value: T; label: string; separateBelow?: boolean }[]
  title?: string
  disabled?: boolean
  drawerTrigger?: React.ReactNode
  className?: string
  hasSelectedFilter?: boolean
}) {
  const current = options.find(({ value }) => filterValue === value)

  return (
    <>
      <Select
        disabled={options.length <= 1}
        value={filterValue}
        onValueChange={setFilterValue}
      >
        <SelectTrigger
          variant="fill"
          className="min-w-max max-md:hidden"
          disabled={disabled}
        >
          {current?.label ?? title}
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {options.map((option) => (
              <React.Fragment key={option.value}>
                <SelectItem value={option.value}>{option.label}</SelectItem>
                {option.separateBelow && <SelectSeparator />}
              </React.Fragment>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      <Drawer>
        <DrawerTrigger
          disabled={disabled}
          className={cn(
            selectTriggerVariants({ variant: 'fill' }),
            'min-h-11 min-w-11 md:hidden',
            {
              'bg-root-muted': hasSelectedFilter,
            },
          )}
        >
          {drawerTrigger ? (
            drawerTrigger
          ) : (
            <DrawerTriggerDefault current={current} title={title} />
          )}
        </DrawerTrigger>
        <DrawerContent className={className}>
          {title ? (
            <DrawerHeader>
              <DrawerTitle>{title}</DrawerTitle>
            </DrawerHeader>
          ) : null}
          <div className="max-h-full overflow-y-scroll px-4">
            <RadioGroup
              defaultValue={filterValue}
              onValueChange={setFilterValue}
            >
              {options.map((option) => {
                const id = `r-${option.value}`
                return (
                  <div
                    className={cn('flex items-center space-x-2', {
                      'border-b border-stroke-muted last:border-b-0':
                        option.separateBelow,
                    })}
                    key={option.value}
                  >
                    <RadioGroupItem value={option.value} id={id} />
                    <Label htmlFor={id}>{option.label}</Label>
                  </div>
                )
              })}
            </RadioGroup>
          </div>
          <DrawerFooter>
            <DrawerClose asChild>
              <Button size="lg">Vis resultat</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  )
}

function DrawerTriggerDefault({
  current,
  title,
}: {
  current?: {
    value: unknown
    label: unknown
  }

  title?: string
}) {
  return (
    <>
      {current?.label ?? title}
      <ChevronDown className="ml-auto size-4 shrink-0 opacity-50" />
    </>
  )
}
