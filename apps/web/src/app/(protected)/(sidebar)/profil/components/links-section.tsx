import React from 'react'

import { But<PERSON> } from '@nordvik/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Input } from '@nordvik/ui/input'

import { Links } from './links'
import { AddButton, Section } from './section'

export function LinksSection({
  title,
  description,
  links,
  placeholder,
  addLink,
  removeLink,
}: {
  title: string
  description: string
  links: string[]
  placeholder?: string
  addLink: (url: string) => Promise<void>
  removeLink: (url: string) => Promise<void>
}) {
  const [isDialogOpen, setIsDialogOpen] = React.useState(false)
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <Section
        title={title}
        description={description}
        emptyState={links.length === 0}
        action={
          <DialogTrigger asChild>
            <AddButton size="lg" />
          </DialogTrigger>
        }
      >
        <Links links={links} removeLink={removeLink} />
        <LinkDialogContent
          key={links.length}
          placeholder={placeholder}
          addLink={async (link) => {
            await addLink(link)
            setIsDialogOpen(false)
          }}
        />
      </Section>
    </Dialog>
  )
}

function LinkDialogContent({
  addLink,
  placeholder,
}: {
  addLink: (url: string) => void
  placeholder?: string
}) {
  const [url, setUrl] = React.useState('')
  const [isPending, setIsPending] = React.useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const isValidUrl = url.trim().match(/^https?:\/\//)
    if (isValidUrl) {
      setIsPending(true)
      addLink(url)
      setIsPending(false)
    }
  }

  return (
    <DialogContent title="Legg til lenke" size="sm" data-theme="dark">
      <DialogDescription>
        <form onSubmit={handleSubmit}>
          <label>
            <span className="sr-only">Lenke</span>
            <Input
              variant="fill"
              placeholder={placeholder}
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
            />
          </label>
        </form>
      </DialogDescription>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="ghost" size="md">
            Avbryt
          </Button>
        </DialogClose>
        <Button type="submit" loading={isPending} size="md">
          Legg til
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}
