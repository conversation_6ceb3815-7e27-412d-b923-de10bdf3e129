import { PlusIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button, ButtonButtonProps } from '@nordvik/ui/button'

export function Section({
  title,
  description,
  action,
  children,
  emptyState,
}: {
  title: string
  description?: string
  action?: React.ReactNode
  children?: React.ReactNode
  emptyState?: boolean
}) {
  return (
    <section className="@container/section">
      <div
        className={cn(
          'flex items-start justify-between gap-2',
          emptyState &&
            'border border-muted rounded-sm py-6 [--x:calc(min(24px,var(--container-padding)))] px-[--x] -mx-[--x] -my-6 border-dotted',
        )}
      >
        <div className="flex flex-col gap-1">
          <h2 className="typo-display-xs">{title}</h2>
          {description && (
            <p className="typo-body-md ink-muted">{description}</p>
          )}
        </div>
        {action && <div className="mt-1">{action}</div>}
      </div>
      {children}
    </section>
  )
}

export function Divider({ className }: { className?: string }) {
  return <div className={cn('bg-stroke-muted h-[1px] w-full', className)} />
}

export const AddButton = ({
  ref,
  ...props
}: Omit<ButtonButtonProps, 'iconOnly'>) => {
  return (
    <Button
      ref={ref}
      variant="outline"
      iconStart={<PlusIcon />}
      {...props}
      size="lg"
    >
      Legg til
    </Button>
  )
}
AddButton.displayName = 'AddButton'
