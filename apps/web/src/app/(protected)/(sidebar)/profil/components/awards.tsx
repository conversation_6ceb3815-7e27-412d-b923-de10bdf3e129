import { AnimatePresence, motion } from 'framer-motion'
import {
  EllipsisIcon,
  EyeIcon,
  EyeOffIcon,
  LockIcon,
  PencilIcon,
  Trash2Icon,
} from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import * as DropdownMenu from '@nordvik/ui/dropdown-menu'
import { Input } from '@nordvik/ui/input'

import { GQLAward, GQLNordvikAward } from '@/api/generated-client'
import { Award } from '@/components/broker-profile/components/award'

import { AddButton, Section } from './section'

export function Awards({
  awards,
  nordvikAwards,
  addAward,
  removeAward,
  updateAward,
  toggleVisibility,
}: {
  awards?: GQLAward[]
  nordvikAwards?: GQLNordvikAward[]
  addAward: (award: GQLAward) => Promise<void>
  removeAward: (award: GQLAward) => Promise<void>
  updateAward: (award: GQLAward) => Promise<void>
  toggleVisibility: (award: GQLNordvikAward) => Promise<void>
}) {
  const [showCreateDialog, setShowCreateDialog] = React.useState(false)
  const combinedAwards = [...(awards || []), ...(nordvikAwards || [])]
    .filter((award) => !('private' in award ? award.private : false))
    .sort((a, b) => (b.year ?? 0) - (a.year ?? 0))
  return (
    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
      <Section
        title="Mine utmerkelser"
        description="Priser og andre utmerkelser du har fått som megler"
        emptyState={combinedAwards.length === 0}
        action={
          <DialogTrigger asChild>
            <AddButton size="lg" />
          </DialogTrigger>
        }
      >
        <motion.ul
          animate={
            combinedAwards.length > 0
              ? { height: 'auto', marginTop: 32 }
              : { height: 0, marginTop: 0 }
          }
          className="flex flex-col @xl/section:flex-row flex-wrap gap-[--gap-x] [--gap-x:1rem]"
          transition={{
            type: 'spring',
            damping: 30,
            stiffness: 300,
          }}
        >
          <AnimatePresence initial={false}>
            {combinedAwards.map((award) => (
              <AwardItem
                key={
                  'id' in award
                    ? award.id
                    : 'awardId' in award
                      ? award.awardId
                      : award.name
                }
                award={award}
                removeAward={() =>
                  'id' in award ? removeAward(award) : Promise.reject()
                }
                updateAward={updateAward}
                toggleVisibility={() =>
                  'awardId' in award
                    ? toggleVisibility(award)
                    : Promise.reject()
                }
              />
            ))}
          </AnimatePresence>
        </motion.ul>

        <AwardDialogContent
          onSave={addAward}
          onClose={() => setShowCreateDialog(false)}
        />
      </Section>
    </Dialog>
  )
}

type AwardItemProps = {
  award: GQLAward | GQLNordvikAward
  updateAward: (award: GQLAward | GQLNordvikAward) => Promise<void>
  removeAward: () => Promise<void>
  toggleVisibility: () => Promise<void>
  ref?: React.RefObject<HTMLLIElement>
}

const AwardItem = function AwardItem({
  ref,
  award,
  removeAward,
  updateAward,
  toggleVisibility,
}: AwardItemProps) {
  const privateAward = 'private' in award ? award.private : false
  const hidden = 'hidden' in award ? award.hidden : false
  const [optimisticHidden, setOptimisticHidden] = React.useState(hidden)
  const dimmed = privateAward || optimisticHidden

  return (
    <motion.li
      ref={ref}
      transition={{
        type: 'spring',
        damping: 30,
        stiffness: 300,
      }}
      layout
      initial={{ opacity: 0, height: 0, scale: 0.9 }}
      animate={{ opacity: 1, height: 'auto', scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="relative flex group/item @xl/section:basis-[calc((100%-var(--gap-x))/2)] @3xl/section:basis-[calc((100%-var(--gap-x)*2)/3)] overflow-hidden"
    >
      <div className="px-4 pb-8 pt-10 rounded-sm border border-muted grow">
        <Award award={award} hidden={dimmed} />
        <div className="absolute top-1 right-2 opacity-40 duration-300 group-hover/item:opacity-100 transition-opacity">
          {'id' in award ? (
            <EditAwardButton
              award={award}
              removeAward={removeAward}
              updateAward={updateAward}
            />
          ) : toggleVisibility ? (
            <ToogleVisibilityButton
              toggleVisibility={async () => {
                await toggleVisibility()
                setOptimisticHidden(!optimisticHidden)
              }}
              hidden={hidden ?? false}
            />
          ) : privateAward ? (
            <PrivateButton />
          ) : null}
        </div>
      </div>
    </motion.li>
  )
}

function EditAwardButton({
  award,
  updateAward,
  removeAward,
}: {
  award: GQLAward
  updateAward: (award: GQLAward) => Promise<void>
  removeAward: () => void
}) {
  const [showEditDialog, setShowEditDialog] = React.useState(false)
  return (
    <>
      <DropdownMenu.DropdownMenu>
        <DropdownMenu.DropdownMenuTrigger asChild>
          <Button size="sm" variant="ghost" iconOnly={<EllipsisIcon />}>
            Endre
          </Button>
        </DropdownMenu.DropdownMenuTrigger>
        <DropdownMenu.DropdownMenuContent align="end" data-theme="dark">
          <DropdownMenu.DropdownMenuItem
            onClick={() => setShowEditDialog(true)}
          >
            <PencilIcon className="size-4" />
            Endre
          </DropdownMenu.DropdownMenuItem>
          <DropdownMenu.DropdownMenuItem onClick={removeAward}>
            <Trash2Icon className="size-4" />
            Fjern
          </DropdownMenu.DropdownMenuItem>
        </DropdownMenu.DropdownMenuContent>
      </DropdownMenu.DropdownMenu>
      {updateAward && (
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <AwardDialogContent
            award={award}
            onClose={() => setShowEditDialog(false)}
            onSave={updateAward}
          />
        </Dialog>
      )}
    </>
  )
}

function ToogleVisibilityButton({
  toggleVisibility,
  hidden,
}: {
  toggleVisibility: () => Promise<void>
  hidden: boolean
}) {
  const [pendingAction, setPendingAction] = React.useState(false)

  return (
    <Button
      size="sm"
      loading={pendingAction}
      onClick={async () => {
        setPendingAction(true)
        await toggleVisibility()
        setPendingAction(false)
      }}
      variant="ghost"
      iconOnly={hidden ? <EyeIcon /> : <EyeOffIcon />}
    >
      {hidden ? 'Vis utmerkelse' : 'Skjul utmerkelse'}
    </Button>
  )
}

function PrivateButton() {
  return (
    <Button
      size="sm"
      variant="ghost"
      iconOnly={<LockIcon />}
      tooltipPosition="left"
    >
      Denne utmerkelsen er intern og ikke synlig for kunder
    </Button>
  )
}

function AwardDialogContent({
  award,
  onClose,
  onSave,
}: {
  award?: GQLAward
  onClose: () => void
  onSave: (award: GQLAward) => Promise<void>
}) {
  const [isPending, setIsPending] = React.useState(false)
  const [validationError, setValidationError] = React.useState<
    'NO_TITLE' | 'NO_YEAR' | 'YEAR_NOT_NUMBER' | null
  >(null)
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const form = e.currentTarget
    const formData = new FormData(form)
    const data = Object.fromEntries(formData.entries())
    const title = data.title ? String(data.title) : undefined
    const origin = data.origin ? String(data.origin) : undefined
    const year = Number(data.year)

    if (typeof title !== 'string' || title.length === 0) {
      setValidationError('NO_TITLE')
      return
    }
    if (!year) {
      setValidationError('NO_YEAR')
      return
    }
    if (isNaN(year)) {
      setValidationError('YEAR_NOT_NUMBER')
      return
    }
    setIsPending(true)
    await onSave({
      id: award?.id,
      name: title,
      origin,
      year,
    })
    onClose()
    form.reset()
    setIsPending(false)
  }

  return (
    <DialogContent
      title={award ? 'Endre utmerkelse' : 'Ny utmerkelse'}
      size="lg"
      data-theme="dark"
    >
      <DialogDescription divider>
        <form
          className="flex flex-col gap-2 space-y-4 mb-2"
          id="award-form"
          onSubmit={handleSubmit}
        >
          <Input
            label="Tittel"
            variant="fill"
            defaultValue={award?.name}
            name="title"
            placeholder="F.eks. Årets megler"
            errorMessage={
              validationError === 'NO_TITLE'
                ? 'Tittel må fylles inn'
                : undefined
            }
          />

          <Input
            label="Opphav"
            variant="fill"
            defaultValue={award?.origin}
            name="origin"
            placeholder="F.eks Norges eiendomsmeglerforbund, Krogsveen, Nordvik Bergen…"
          />

          <Input
            label="Årstall"
            variant="fill"
            defaultValue={award?.year}
            name="year"
            placeholder="2023…"
            type="number"
            errorMessage={
              validationError === 'YEAR_NOT_NUMBER'
                ? 'Årstall må være et tall'
                : validationError === 'NO_YEAR'
                  ? 'Årstall må fylles inn'
                  : undefined
            }
          />
        </form>
      </DialogDescription>
      <DialogFooter>
        <Button variant="ghost" size="md" onClick={onClose}>
          Avbryt
        </Button>
        <Button loading={isPending} type="submit" form="award-form" size="md">
          {award ? 'Lagre' : 'Legg til'}
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}
