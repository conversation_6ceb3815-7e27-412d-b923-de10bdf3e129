import {
  AnimatePresence,
  AnimationControls,
  Reorder,
  motion,
  useAnimation,
  useDragControls,
} from 'framer-motion'
import {
  GripHorizontalIcon,
  MoreHorizontalIcon,
  MoveLeftIcon,
  MoveRightIcon,
  Trash2Icon,
} from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@nordvik/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from '@nordvik/ui/tooltip'

import { GQLUsp } from '@/api/generated-client'
import { useTrackEvent } from '@/lib/analytics/track-event'

import { Section } from './section'
import { USPSuggestionsButton } from './ups-suggestions-button'

type USP = Omit<GQLUsp, '__typename'> & { id?: string }

export function randomID() {
  return Math.random().toString(36).substring(7)
}

function ensure3USP(usp?: Partial<USP>[] | null): USP[] {
  const mocked = Array.from({ length: 3 })
  return mocked.map((_, index) => {
    const content = usp?.[index]
    return { id: randomID(), ...content }
  })
}

export function SellingPoints({
  usp: _usp,
  updateUsp,
  allowSuggestions,
}: {
  usp?: USP[] | null
  updateUsp: (usp: GQLUsp[]) => Promise<void | unknown>
  allowSuggestions?: boolean
}) {
  const [loading, setLoading] = React.useState(false)
  const [touched, setTouched] = React.useState(false)
  const initialUSP = React.useMemo(() => ensure3USP(_usp), [_usp])
  const [usp, setUsp] = React.useState<USP[]>(initialUSP)
  const shimmerControls = useAnimation()
  const trackEvent = useTrackEvent()

  React.useEffect(() => {
    setTouched(false)
    setUsp(ensure3USP(_usp))
  }, [_usp])

  function revertToInitialState() {
    setTouched(false)
    setUsp(initialUSP)
  }

  async function onSave() {
    setLoading(true)
    return updateUsp(
      usp
        .filter((s) => s.title || s.description)
        .map((s) => ({
          title: s.title,
          description: s.description,
        })),
    )
      .then(() => setTouched(false))
      .finally(() => setLoading(false))
  }

  function patchContent(content: USP) {
    const newSellingPoints = [...usp]
    const index = usp.findIndex((s) => s?.id === content.id)
    const newContent = { ...usp[index], ...content }
    newSellingPoints[index] = newContent
    setTouched(true)
    setUsp(newSellingPoints)
  }

  function moveContent(content: USP, direction: 'up' | 'down') {
    const newSellingPoints = [...usp]
    const index = usp.findIndex((s) => s?.id === content.id)
    const newContent = newSellingPoints[index]
    const newIndex = direction === 'up' ? index - 1 : index + 1
    const toBeReplaced = newSellingPoints[newIndex]
    newSellingPoints[newIndex] = newContent
    newSellingPoints[index] = toBeReplaced
    setTouched(true)
    setUsp(newSellingPoints.filter((s) => s !== null))
  }

  async function getSuggestions() {
    const result = await fetch(`/profil/usp-suggestion`)
    if (result.ok) {
      trackEvent('profile_get_usp_suggestions')
      const data = await result.json()
      setUsp(usp.map((u, i) => ({ id: u.id, ...data.usps[i] })))
      shimmerControls.start('shimmer')
      setTouched(true)
    } else {
      throw new Error('Failed to fetch suggestions')
    }
  }

  return (
    <Section
      title="Mine fokusområder"
      description="Hva er dine beste egenskaper som megler"
      action={
        allowSuggestions && <USPSuggestionsButton onClick={getSuggestions} />
      }
    >
      <Reorder.Group
        values={usp}
        onReorder={(newOrder) => {
          setTouched(true)
          setUsp(newOrder)
        }}
        axis="x"
        className="mt-6 grid grid-cols-1 @[60rem]/section:grid-cols-3 gap-3 @[70rem]/section:gap-4"
      >
        {usp.map((content, index, list) => (
          <SellingPointsItem
            key={content.id}
            index={index}
            isFirst={index === 0}
            isLast={index === list.length - 1}
            content={content ?? null}
            patchContent={(content) => patchContent(content)}
            moveContent={(direction) => moveContent(content, direction)}
            shimmerControls={shimmerControls}
          />
        ))}
      </Reorder.Group>

      <AnimatePresence>
        {touched && (
          <motion.div
            key="save"
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ type: 'spring', stiffness: 200, damping: 20 }}
          >
            <div className="flex gap-2 justify-end pt-4">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ type: 'spring', stiffness: 200, damping: 20 }}
              >
                <Button
                  size="sm"
                  variant="outline"
                  onClick={revertToInitialState}
                >
                  Forkast
                </Button>
              </motion.div>
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ type: 'spring', stiffness: 200, damping: 20 }}
              >
                <Button size="sm" loading={loading} onClick={onSave}>
                  Lagre
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </Section>
  )
}

const MAX_TITLE_LENGTH = 40
const MAX_BODY_LENGTH = 180

function SellingPointsItem({
  index,
  isFirst,
  isLast,
  content,
  moveContent,
  patchContent,
  shimmerControls,
}: {
  index: number
  isFirst: boolean
  isLast: boolean
  content: USP
  patchContent: (content: USP) => void
  moveContent: (direction: 'up' | 'down') => void
  shimmerControls: AnimationControls
}) {
  const dragControls = useDragControls()
  const isEmpty = !content?.title && !content?.description
  const titleValidationError = (content?.title?.length || 0) > MAX_TITLE_LENGTH
  const descriptionValidationError =
    (content?.description?.length || 0) > MAX_BODY_LENGTH
  const hasValidationError = titleValidationError || descriptionValidationError

  return (
    <Reorder.Item
      layout
      value={content}
      dragListener={false}
      dragControls={dragControls}
    >
      <div
        className={cn(
          'rounded-sm border border-[transparent] transition duration-100 @container/sales-pitch-item relative',
          'select-none', // Prevent selection when dragging
          !isEmpty ? 'bg-root-muted' : 'border-muted',
          hasValidationError && 'border-gold-emphasis',
        )}
      >
        <div className="px-3 pt-5 flex flex-col gap-2 @[22rem]/sales-pitch-item:px-6 @[22rem]/sales-pitch-item:pt-6">
          <Tooltip open>
            <TooltipTrigger asChild>
              <input
                type="text"
                className="typo-display-xs bg-transparent focus:outline-none"
                placeholder="Tittel…"
                value={content?.title ?? ''}
                onChange={(e) =>
                  patchContent({ id: content.id, title: e.target.value })
                }
              />
            </TooltipTrigger>
            {titleValidationError && (
              <TooltipPortal>
                <TooltipContent variant="warning">
                  <p>Tittelen bør være mindre enn {MAX_TITLE_LENGTH} tegn</p>
                </TooltipContent>
              </TooltipPortal>
            )}
          </Tooltip>
          <Tooltip open>
            <TooltipTrigger asChild>
              <textarea
                rows={4}
                className="typo-body-md appearance-none bg-transparent resize-none focus:outline-none"
                placeholder="Ditt salgsargument…"
                value={content?.description ?? ''}
                onChange={(e) =>
                  patchContent({ id: content.id, description: e.target.value })
                }
              />
            </TooltipTrigger>
            {descriptionValidationError && (
              <TooltipPortal>
                <TooltipContent variant="warning">
                  <p>Teksten bør være mindre enn {MAX_BODY_LENGTH} tegn</p>
                </TooltipContent>
              </TooltipPortal>
            )}
          </Tooltip>
        </div>
        <div
          className={cn(
            'p-2 flex justify-between transition group',
            isEmpty && 'opacity-0 pointer-events-none',
          )}
          onPointerDown={(e) => dragControls.start(e)}
        >
          <Button
            tabIndex={-1}
            variant="unstyled"
            iconOnly={<GripHorizontalIcon />}
            className="opacity-0 group-hover:opacity-50 cursor-move transition duration-500"
            size="lg"
          >
            Dra for å flytte
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild tabIndex={isEmpty ? -1 : undefined}>
              <Button
                variant="ghost"
                iconOnly={<MoreHorizontalIcon />}
                className="opacity-50 hover:opacity-100 transition"
                size="lg"
              >
                Endre
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-theme="dark">
              {!isFirst && (
                <DropdownMenuItem onClick={() => moveContent('up')}>
                  <MoveLeftIcon className="size-4" />
                  Flytt frem
                </DropdownMenuItem>
              )}
              {!isLast && (
                <DropdownMenuItem onClick={() => moveContent('down')}>
                  <MoveRightIcon className="size-4" />
                  Flytt bak
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() =>
                  patchContent({
                    id: content.id,
                    title: undefined,
                    description: undefined,
                  })
                }
              >
                <Trash2Icon className="size-4" />
                Fjern
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <motion.div
          className="absolute text-fill-gold-subtle pointer-events-none inset-0 [background-size:200%] [background-position-x:160%]  bg-no-repeat bg-[linear-gradient(-45deg,_transparent_40%,_currentColor_50%,_transparent_65%)] opacity-10"
          transition={{
            type: 'tween',
            ease: 'easeInOut',
            duration: 0.5,
            delay: index * 0.2,
          }}
          animate={shimmerControls}
          variants={{
            shimmer: {
              backgroundPositionX: ['160%', '-60%'],
            },
          }}
        />
      </div>
    </Reorder.Item>
  )
}
