import { AnimatePresence, motion } from 'framer-motion'
import React from 'react'

import { <PERSON>alog, DialogTrigger } from '@nordvik/ui/dialog'

import { GQLBrokerPartner } from '@/api/generated-client'
import { Partner } from '@/components/broker-profile/components/partner/partner'
import {
  PartnerDialogContent,
  PartnerFormValues,
} from '@/components/broker-profile/components/partner/partner-dialog-content'

import { AddButton, Section } from '../section'

export function Team({
  addPartner,
  removePartner,
  updatePartner,
  reorderPartners,
  toggleHidePartner,
  partners,
}: {
  addPartner: (partner: PartnerFormValues) => unknown
  updatePartner: (partner: PartnerFormValues) => unknown
  removePartner: (partnerId: string) => unknown
  reorderPartners: (ids: string[]) => unknown
  toggleHidePartner: (id: string) => unknown
  partners: GQLBrokerPartner[]
}) {
  const [showCreateDialog, setShowCreateDialog] = React.useState(false)

  function movePartner(id: string, direction: 'up' | 'down') {
    const ids = partners.map((p) => p.id)
    const currentIndex = ids.indexOf(id)

    if (currentIndex === -1) return

    const offset = direction === 'up' ? -1 : 1
    const newIndex = currentIndex + offset

    if (newIndex >= 0 && newIndex < ids.length) {
      ;[ids[currentIndex], ids[newIndex]] = [ids[newIndex], ids[currentIndex]]
      reorderPartners(ids)
    }
  }

  return (
    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
      <Section
        title="Mine samarbeidspartnere"
        description="Vis hvilke fotografer, stylister og bygningssakkyndige du samarbeider med"
        emptyState={partners.length === 0}
        action={
          <DialogTrigger asChild>
            <AddButton size="lg" />
          </DialogTrigger>
        }
      >
        <div className="">
          <AnimatePresence>
            {partners.map((partner, index, list) => (
              <motion.div
                key={partner.id}
                layout="position"
                className="overflow-y-clip"
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                transition={{ type: 'spring', stiffness: 200, damping: 30 }}
              >
                <Partner
                  toggleHidePartner={toggleHidePartner}
                  updatePartner={updatePartner}
                  removePartner={removePartner}
                  moveUp={
                    index > 0 ? () => movePartner(partner.id, 'up') : undefined
                  }
                  moveDown={
                    index < list.length - 1
                      ? () => movePartner(partner.id, 'down')
                      : undefined
                  }
                  key={partner.id}
                  partner={partner}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
        <PartnerDialogContent
          key={partners.length}
          onSave={async (data) => {
            await addPartner(data)
            setShowCreateDialog(false)
          }}
        />
      </Section>
    </Dialog>
  )
}
