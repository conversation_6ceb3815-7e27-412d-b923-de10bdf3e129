import { Analytics } from '@vercel/analytics/next'
import type { Metadata } from 'next'
import { NuqsAdapter } from 'nuqs/adapters/next/app'
import { Suspense } from 'react'
import 'reflect-metadata'

import { ProgressBar } from '@nordvik/ui/global-navigation-progress/progress-bar'
import { TooltipProvider } from '@nordvik/ui/tooltip'

import AutoTrackPageview from '@/lib/analytics/auto-track-pageview'
// Our own global styles
import { AnalyticsProvider } from '@/lib/analytics/provider'
import { isProd } from '@/lib/getBaseUrl'
import { QueryClientProvider } from '@/lib/queryClient'

import './globals.css'

export const metadata: Metadata = {
  manifest: '/manifest.json', // we are accessing our manifest file here
}

export default function RootLayout({ children }: React.PropsWithChildren) {
  return (
    <html lang="no">
      <body
        className={`lg:min-h-[100svh] font-grotesk font-normal antialiased`}
      >
        <ProgressBar>
          <TooltipProvider>
            <AnalyticsProvider>
              <Suspense>
                <AutoTrackPageview />
              </Suspense>
              <QueryClientProvider>
                <NuqsAdapter>
                  <div className="isolate">{children}</div>
                </NuqsAdapter>
              </QueryClientProvider>
            </AnalyticsProvider>
          </TooltipProvider>
        </ProgressBar>
        {isProd && <Analytics />}
      </body>
    </html>
  )
}
