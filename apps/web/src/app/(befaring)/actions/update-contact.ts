'use server'

import { contactUpdate } from '@/actions/next/contact-update'

import { NextPrivateContact } from '../../../actions/next/types-next'

type UpdateContactData = Pick<
  NextPrivateContact,
  | 'contactType'
  | 'firstName'
  | 'lastName'
  | 'mobilePhone'
  | 'email'
  | 'contactId'
> & { organisationNumber?: string }

export async function updateContact(
  data: UpdateContactData,
  isCompany?: boolean,
) {
  try {
    const missingRequiredFields = (object?: Partial<NextPrivateContact>) => {
      const requiredFields = [
        // required by api
        'contactType',
        'firstName',
        'lastName',
        'mobilePhone',
        // required by form
        'email',
        'postalAddress',
        'postalCode',
        'city',
      ]

      if (isCompany) {
        requiredFields.push('organisationNumber')
      }

      return requiredFields.some(
        (field) => !object?.[field] && object?.[field] !== 0,
      )
    }

    if (missingRequiredFields(data)) {
      throw new Error('Missing required fields')
    }

    await contactUpdate(data)
  } catch (error) {
    console.error(error)
    throw new Error('Failed to update contact')
  }
}
