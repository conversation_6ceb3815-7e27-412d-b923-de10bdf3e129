'use server'

import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'

import { NextPrivateContactWithProxy } from '../../../actions/next/types-next'

type ExtraData = {
  includeListingAgreement: boolean
  channels?: string[]
  recipientContactIds?: string[]
  templateId: string
  modified: boolean
  email?: string
  sms?: string
  emailSubject?: string
  recipients: Pick<
    NextPrivateContactWithProxy,
    'firstName' | 'lastName' | 'email' | 'mobilePhone' | 'contactId'
  >[]
}

export const markInspectionAsSent = async (
  estateId: string,
  extraData: ExtraData,
) => {
  const currentUser = await getCurrentUser()
  if (!currentUser) return

  const dateNow = new Date()

  const inspectionFolder = await prisma.inspection_folders.findFirst({
    where: {
      estate_id: estateId,
    },
  })

  await prisma.inspection_folders.update({
    where: {
      estate_id: estateId,
    },
    data: {
      sent_at: dateNow,
      sent_by_id: currentUser?.id,
      published_at: dateNow,
      published_by_id: currentUser?.id,
      listing_agreement_active:
        inspectionFolder?.listing_agreement_active ||
        extraData.includeListingAgreement,
      inspection_folders_audit: {
        create: {
          sent_at: dateNow,
          sent_by_id: currentUser?.id,
          listing_agreement_active: extraData.includeListingAgreement,
          channels: extraData.channels,
          recipient_contact_ids: extraData.recipientContactIds,
          extra_data: {
            template: {
              templateId: extraData.templateId,
              modified: extraData.modified,
              email: extraData.email,
              sms: extraData.sms,
              emailSubject: extraData.emailSubject,
            },
            recipients: extraData.recipients.map((recipient) => ({
              email: recipient.email,
              mobilePhone: recipient.mobilePhone,
              lastName: recipient.lastName,
              firstName: recipient.firstName,
              contactId: recipient.contactId,
            })),
          },
        },
      },
    },
  })
}
