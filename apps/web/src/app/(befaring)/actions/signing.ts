import { Broker<PERSON><PERSON> } from '@befaring/lib/broker-constants'
import type { ListingAgreement } from '@befaring/lib/budget-helpers'
import {
  type ListingAgreementBroker,
  getBrokerByRole,
} from '@befaring/lib/format-brokers'
import type { SellerInsurance } from '@befaring/lib/prices'
import { waitUntil } from '@vercel/functions'

import { logListingAgreementInteraction } from '@/actions/log-listing-agreement-interaction'
import { postActivity } from '@/actions/next/post-activity'
import type {
  BudgetDataSection,
  MergedBudgetPost,
  NextAssignmentBudget,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import type { CreateSignicatDocumentOptions } from '@/actions/signing/create-seller-contract'
import { sign } from '@/actions/signing/document-sign'
import { SignicatTag } from '@/actions/signing/signicat-tag'
import type {
  GQLGetBrokerEstateQuery,
  GQLMarketingPackage,
} from '@/api/generated-client'
import { notifyBrokerOnVitec } from '@/app/api/signicat/util/notify-broker-on-vitec'
import { getBaseUrl } from '@/lib/getBaseUrl'
import { pipeSync } from '@/lib/pipe'

import {
  GenerateContractHtmlProps,
  generateContractHtml,
} from '../oppdragsavtale/[estateId]/(agreement)/template/generate-contract'
import { generateOfferHtml } from '../oppdragsavtale/[estateId]/(agreement)/template/generate-offer-html'
import { generateValuationContractHtml } from '../oppdragsavtale/[estateId]/(agreement)/template/generate-valuation-contract'

import { getCompanyProxy } from './get-company-proxy'
import { addDocumentInfoToListingAgreement } from './listing-agreement'

const generateHtml = (data: GenerateContractHtmlProps) => {
  if (data.estate?.isValuation) {
    return pipeSync(generateValuationContractHtml, generateOfferHtml)(data)
  }

  return pipeSync(generateContractHtml, generateOfferHtml)(data)
}

export const handleSign = async ({
  data,
  options,
}: {
  data: {
    sellers: NextPrivateContactWithProxy[]
    brokers: ListingAgreementBroker[]
    estate: GQLGetBrokerEstateQuery['estate']
    budget: NextAssignmentBudget
    commission: number
    budgetSum: number
    listingAgreement: ListingAgreement
    income: BudgetDataSection
    outlay: BudgetDataSection
    budgetDiscount?: MergedBudgetPost
    sellerInsurance: SellerInsurance | null
    marketingPackage: GQLMarketingPackage | undefined
  }
  options?: CreateSignicatDocumentOptions
}) => {
  if (!data.estate) {
    throw new Error('Missing estate')
  }

  const { estate } = data

  const broker = getBrokerByRole(BrokerRole.Responsible, {
    brokers: estate.brokers,
    brokersIdWithRoles: estate.brokersIdWithRoles,
  })

  if (!broker) {
    throw new Error('Missing responsible broker')
  }

  if (estate.hasCompanySeller) {
    await Promise.all(
      data.sellers.map(async (seller) => {
        seller.proxy = await getCompanyProxy(seller, data.listingAgreement.id)
      }),
    )
  }

  const response = await sign({
    estate,
    options,
    htmlString: generateHtml(data),
    broker: {
      employeeId: broker.employeeId,
      email: broker.employee.email ?? '',
      name: broker.employee.name ?? 'Megler',
      mobilePhone: broker.employee.mobilePhone,
    },
    departmentId: estate.department?.departmentId.toString() ?? '',
    listingAgreementId: data.listingAgreement.id,
    tags: [
      estate.isValuation ? SignicatTag.Valuation : SignicatTag.ListingAgreement,
    ],
  })

  if (!response) {
    throw new Error('Failed to initiate document')
  }

  waitUntil(
    Promise.all([
      addDocumentInfoToListingAgreement({
        estateId: estate.estateId,
        document: response,
      }),
      logListingAgreementInteraction({
        listing_agreements_id: data.listingAgreement.id,
        event_type: 'start_signing',
        seller_id: options?.skipInitialNotificationFor?.[0] ?? null,
        employee_id: broker?.employeeId,
      }),
      notifyVitec(estate, broker),
    ]),
  )

  return response
}

export async function notifyVitec(
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>,
  broker: { employeeId: string },
) {
  const estateReference = estate.address?.streetAddress ?? estate.estateId

  const date = new Date()
  console.info(`Notifying Vitec about signed contract for ${estateReference}`)

  try {
    await Promise.all([
      notifyBrokerOnVitec(estate.estateId, {
        subject: `Signer oppdragsavtale for ${estateReference}`,
        body: `Oppdragsavtalen for ${estateReference} er klar for signering.`,
      }),
      postActivity({
        estateId: estate.estateId,
        title: 'Oppdragsavtale er klar for signering',
        start: date.toISOString(),
        end: date.toISOString(),
        doneDate: date.toISOString(),
        typeName: 'Oppdragsavtale',
        employeeId: broker.employeeId,
        url: `${getBaseUrl()}/oppdrag/detaljer/${estate.estateId}`,
      }),
    ])

    console.info(`Notified Vitec about signed contract for ${estateReference}`)
  } catch (e) {
    console.error(`Error notifying Vitec: ${e}`)
  }
}
