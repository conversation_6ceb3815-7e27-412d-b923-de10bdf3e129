import type { budget_posts, listing_agreements } from '@prisma/client'

import type {
  MergedBudgetPost,
  NextBudgetPost,
} from '@/actions/next/types-next'
import type {
  ReplaceBigIntWithNumber,
  ReplaceDecimalWithNumber,
} from '@/utils/typeHelpers'

export function mergeBudgetPosts(
  budgetPostsFromPostgres: budget_posts[],
  nextBudgetPosts: NextBudgetPost[],
): MergedBudgetPost[] {
  return nextBudgetPosts
    .map((post) => {
      const budgetPost = budgetPostsFromPostgres.find(
        ({ next_budget_post_id }) =>
          next_budget_post_id === BigInt(post.budgetpostId),
      )

      if (!budgetPost) {
        return null
      }

      return {
        ...post,
        ...budgetPost,
        initial_price: budgetPost.initial_price?.toNumber() ?? null,
      }
    })
    .filter(Boolean) as MergedBudgetPost[]
}

export enum BuyProcessStatus {
  FINANCING = 'financing',
  LOOKING = 'looking',
  BIDDING = 'bidding',
  BOUGHT = 'bought',
}

export function buyProcessStatusTranslation(status: BuyProcessStatus): string {
  switch (status) {
    case BuyProcessStatus.FINANCING:
      return 'skal refinansiere'
    case BuyProcessStatus.LOOKING:
      return 'er på boligjakt'
    case BuyProcessStatus.BIDDING:
      return 'skal i budrunde'
    case BuyProcessStatus.BOUGHT:
      return 'har allerede kjøpt'
    default:
      return ''
  }
}

export type ListingAgreement = ReplaceBigIntWithNumber<
  ReplaceDecimalWithNumber<listing_agreements>
>

export function formatListingAgreement(
  listingAgreement: listing_agreements,
): ListingAgreement {
  return {
    ...listingAgreement,
    initial_fee_percentage:
      listingAgreement.initial_fee_percentage?.toNumber() ?? null,
    fee_percentage: listingAgreement.fee_percentage?.toNumber() ?? null,
    suggested_price: listingAgreement.suggested_price
      ? Number(listingAgreement.suggested_price)
      : null,
  }
}

export function calculateBudgetTotalReduction(
  mergedIncomePosts: MergedBudgetPost[],
  mergedOutlayPosts: MergedBudgetPost[],
  budgetDiscountSum: number | null | undefined,
  initialCommission: number,
  commission: number,
) {
  // Loop through all posts and calculate the total reduction if the price is lower than the initial price
  let budgetTotalReduction = [
    ...mergedIncomePosts,
    ...mergedOutlayPosts,
  ].reduce((acc, post) => {
    if (
      post.initial_price !== null &&
      post.initial_price > post.amountWithTaxIncluded
    ) {
      return acc - (post.initial_price - post.amountWithTaxIncluded)
    }
    return acc
  }, budgetDiscountSum ?? 0)

  // Add commission reduction if commission is reduced
  if (initialCommission > commission) {
    budgetTotalReduction -= initialCommission - commission
  }

  return budgetTotalReduction
}

export function calculateBudgetSection(posts: MergedBudgetPost[]) {
  return posts.reduce((acc, post) => acc + post.amountWithTaxIncluded, 0)
}
