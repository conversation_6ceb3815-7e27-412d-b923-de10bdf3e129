'use client'

import { SalesHistory } from '@befaring/verdivurdering/[estateId]/din-bolig/components/sales-history-section'
import { isAfter, subYears } from 'date-fns'
import { subMonths } from 'date-fns/subMonths'
import React, { useMemo } from 'react'

import { Separator } from '@nordvik/ui/separator'

import { GQLAreaStatisticsQuery, GQLPriceIndex } from '@/api/generated-client'
import { EiendomsverdiLinkProps } from '@/external-services/eiendomsverdi/get-eiendomsverdi-link'

import { AreaPriceChartSection } from './area-price-chart-section'
import PropertyStats from './property-stats'

export const tabs = [
  {
    label: '1 år',
    value: '1',
  },
  {
    label: '3 år',
    value: '3',
  },
  {
    label: '5 år',
    value: '5',
  },
  {
    label: '10 år',
    value: '10',
  },
] as const

export type TabValue = (typeof tabs)[number]['value']

export function ClientStatisticsWrapper({
  statistics = {
    indexes: [],
    secondaryIndexes: [],
  },
  promise,
  className,
  estate,
  loading,
}: {
  statistics: GQLAreaStatisticsQuery['priceStatistics'] | undefined
  promise?: Promise<SalesHistory>
  className?: string
  estate: EiendomsverdiLinkProps
  loading?: boolean
}) {
  const [activeTab, setActiveTab] = React.useState<TabValue>('10')

  const TabChartValues = useMemo(
    () =>
      tabs.reduce(
        (a, { value }) => (
          (a[value] = filterStatsSinceYearsAgo(statistics, +value)), a
        ),
        {} as Record<TabValue, GQLAreaStatisticsQuery['priceStatistics']>,
      ),
    [statistics],
  )

  const { indexes: primary, secondaryIndexes: secondary } =
    TabChartValues[activeTab]

  const oneYearValues = TabChartValues['1']

  const mostRecentDataPoint = primary?.at(-1)

  return (
    <div className={className}>
      <AreaPriceChartSection
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        statistics={TabChartValues[activeTab]}
        promise={promise}
      />
      <Separator
        orientation="vertical"
        className="h-auto hidden md:inline-flex"
      />
      <Separator orientation="horizontal" className="md:hidden" />
      <PropertyStats
        loading={loading}
        estate={estate}
        primary={{
          change:
            mostRecentDataPoint?.indexChange4Quarter ??
            mostRecentDataPoint?.indexChange12Months ??
            0,
          salesTime:
            priceIndexAvgByProp(oneYearValues.indexes, 'avgSalesTime') ?? 0,
          sqmPrice: oneYearValues.indexes.at(-1)?.avgSqmPrice ?? 0,
        }}
        secondary={
          secondary && secondary.length > 0
            ? {
                name: secondary?.[0]?.region?.replaceAll(' Fylke', '') ?? '',
                change: secondary?.at(-1)?.indexChange12Months ?? 0,
                salesTime:
                  priceIndexAvgByProp(
                    oneYearValues.secondaryIndexes,
                    'avgSalesTime',
                  ) ?? 0,
                sqmPrice:
                  oneYearValues.secondaryIndexes.at(-1)?.avgSqmPrice ?? 0,
              }
            : undefined
        }
      />
    </div>
  )
}

function priceIndexAvgByProp(data: GQLPriceIndex[], prop: keyof GQLPriceIndex) {
  const cleansed = data.filter((e) => e[prop] !== undefined)
  return (
    cleansed.reduce((acc, entry) => acc + (entry[prop]! as number), 0) /
    cleansed.length
  )
}

function filterStatsSinceYearsAgo(
  statistics: GQLAreaStatisticsQuery['priceStatistics'],
  years = 1,
) {
  if (!statistics.indexes?.length || statistics.indexes.length === 0) {
    return {
      indexes: [],
      secondaryIndexes: [],
    }
  }

  // we know the data point exists since we checked the length
  const latestPrimaryDataPoint = statistics.indexes.at(-1)!

  // also subsctract 1 months to catch the last quarter for sure
  const sinceYearsAgo = subMonths(
    subYears(latestPrimaryDataPoint.date, years),
    1,
  )
  return {
    indexes:
      statistics.indexes?.filter((e) => isAfter(e.date, sinceYearsAgo)) ?? [],
    secondaryIndexes:
      statistics.secondaryIndexes?.filter((e) =>
        isAfter(e.date, sinceYearsAgo),
      ) ?? [],
  }
}
