'use server'

import { Suspense } from 'react'

import type {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'

import { SellerCompanyInfoCard } from './seller-company-info-card'
import { SellerCompanyInfoCardContentSkeleton } from './seller-company-info-card-content'
import SellerInfoCard from './seller-info-card'

export async function SellersSection({
  sellers,
  estateId,
  canEdit,
}: {
  sellers: NextPrivateContactWithProxy[]
  estateId: string
  signers: NextPrivateContact[]
  ownership: number
  canEdit: boolean
}) {
  return (
    <div className="flex flex-col items-start gap-8" data-fields="seller-info">
      <div className="grid md:grid-cols-2 lg:grid-cols-3 grid-cols-1 gap-4 w-full gap-y-7">
        {sellers.map((seller) => {
          const name = `${seller.firstName} ${seller.lastName}`
          return seller.contactType === 0 ? (
            <SellerInfoCard key={name} seller={seller} canEdit={canEdit} />
          ) : (
            <Suspense
              key={seller.contactId}
              fallback={<SellerCompanyInfoCardContentSkeleton />}
            >
              <SellerCompanyInfoCard
                key={name}
                company={seller}
                estateId={estateId}
                canEdit={canEdit}
              />
            </Suspense>
          )
        })}
      </div>
    </div>
  )
}
