import { BrokersInfoSection } from '@befaring/components/brokers-info-section'
import { Section } from '@befaring/components/section'
import { SellersSection } from '@befaring/components/seller-section/sellers-section'
import { SubSection } from '@befaring/components/sub-section'

import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import { GQLGetBrokerEstateQuery } from '@/api/generated-client'

export function PartiesSection({
  sellers,
  signers,
  estateId,
  ownership,
  canEdit,
  estate,
}: {
  sellers: NextPrivateContactWithProxy[]
  estateId: string
  signers: NextPrivateContact[]
  ownership: number
  canEdit: boolean
  estate: GQLGetBrokerEstateQuery['estate']
}) {
  return (
    <Section title="Partene">
      <SubSection title="Oppdragsgiver" className="mb-8">
        <SellersSection
          sellers={sellers}
          estateId={estateId}
          signers={signers}
          ownership={ownership}
          canEdit={canEdit}
        />
      </SubSection>
      <SubSection title="Oppdragstaker">
        <BrokersInfoSection estate={estate} />
      </SubSection>
    </Section>
  )
}
