'use client'

import {
  CollapsibleSection,
  SectionContent,
} from '@befaring/components/collapsible-section'
import { EditModeSwitch } from '@befaring/components/edit-mode-switch'
import SectionTotalRow from '@befaring/components/section-total-row'
import { useBudget } from '@befaring/context/budget-context'
import { IncomePostRow } from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/income-post-row'

import { useUserContext } from '@/lib/UserContext'

export function CompensationSection() {
  const { valuation, locked, editMode, setEditMode } = useBudget()
  const { user } = useUserContext()

  return (
    <CollapsibleSection
      title="Oppdrag og vederlag"
      defaultOpen
      action={
        Boolean(user && !locked) && (
          <EditModeSwitch editMode={editMode} setEditMode={setEditMode} />
        )
      }
    >
      <SectionContent className="typo-body-md flex flex-col gap-4 pb-1">
        <p className="px-[--padding-x] text-pretty max-w-[100ch]">
          En e-takst er et kvalifisert skriftlig anslag over eiendommens
          markedsverdi og avgis etter beste skjønn og overbevisning med bakgrunn
          i markedssituasjon, besiktigelse og eiers opplysninger om eiendommen.
        </p>
        {valuation.post && (
          <IncomePostRow
            post={valuation.post}
            editMode={editMode}
            hideDescription
            forceOpen={editMode}
          />
        )}
      </SectionContent>
      <SectionTotalRow
        title="Totalsum"
        total={valuation.post?.amountWithTaxIncluded ?? 0}
      />
    </CollapsibleSection>
  )
}
