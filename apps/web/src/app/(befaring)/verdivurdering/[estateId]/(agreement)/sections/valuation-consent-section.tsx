'use client'

import { Section } from '@befaring/components/section'
import { useBudget } from '@befaring/context/budget-context'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { mapBooleanToYesNo } from '@befaring/oppdragsavtale/[estateId]/(agreement)/parter/form-helpers'
import { startTransition } from 'react'

import { RadioGroup, RadioGroupItem } from '@nordvik/ui/radio-group'

export function ValuationConsentSection() {
  const {
    locked,
    customerInfo,
    setCustomerInfo,
    listingAgreement,
    setIsUpdating,
  } = useBudget()
  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  return (
    <Section>
      <p className="typo-title-xs mb-4">
        Forbrukers avklaring av oppstart av eiendomsmeglingstjenesten:
      </p>

      <div
        className="flex flex-col gap-4"
        data-fields={ValidatedFieldsListingAgreement.waive_withdrawal_right}
      >
        <RadioGroup
          disabled={locked}
          defaultValue={mapBooleanToYesNo(customerInfo.waive_withdrawal_right)}
          className="flex gap-x-6 gap-y-4 flex-col"
          onValueChange={async (value) => {
            setIsUpdating(true)
            startTransition(() => {
              setCustomerInfo({
                ...customerInfo,
                waive_withdrawal_right: value === 'yes',
              })
            })
            await updateInfo(
              { waiveWithdrawalRight: value === 'yes' },
              'Kunne ikke oppdatere angrerett',
            )
          }}
        >
          <div className="flex gap-2">
            <RadioGroupItem
              className="mt-0.5 shrink-0"
              id="waiveWithdrawalRight"
              value="yes"
            />
            <label
              htmlFor="waiveWithdrawalRight"
              className="typo-body-md text-pretty max-w-[100ch]"
            >
              Jeg ønsker at Oppdragstaker skal sette i gang arbeidet i henhold
              til oppdragsavtalen, herunder starte levering av tilknyttede
              tjenester, før angrefristen på 14 dager har utløpt. Jeg erkjenner
              at angreretten har gått tapt når tjenesten er levert.
            </label>
          </div>
          <div className="flex gap-2">
            <RadioGroupItem
              className="mt-0.5 shrink-0"
              id="notWaiveWithdrawalRight"
              value="no"
            />
            <label
              htmlFor="notWaiveWithdrawalRight"
              className="typo-body-md text-pretty max-w-[100ch]"
            >
              Jeg ønsker IKKE at Oppdragstaker skal sette i gang arbeidet i
              henhold til oppdragsavtalen, herunder starte levering av
              tilknyttede tjenester, før angrefristen på 14 dager har utløpt.
            </label>
          </div>
        </RadioGroup>
      </div>
    </Section>
  )
}
