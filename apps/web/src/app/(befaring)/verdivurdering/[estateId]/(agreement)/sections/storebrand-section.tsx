'use client'

import { Section } from '@befaring/components/section'
import { StorebrandAgreementForm } from '@befaring/components/storebrand-agreement-form'
import { SubSection } from '@befaring/components/sub-section'
import { useBudget } from '@befaring/context/budget-context'
import {
  EligibleSigners,
  getDefaultRecipient,
  useEligibleSigners,
} from '@befaring/hooks/use-loan-offer-components'
import { createLoanOfferHandlers } from '@befaring/hooks/use-loan-offer-handlers'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { StorebrandRequestSourceEnum } from '@befaring/verdivurdering/types'
import { AnimatePresence, motion } from 'framer-motion'
import Image from 'next/image'
import { startTransition, useState } from 'react'

import { Checkbox } from '@nordvik/ui/checkbox'

import { useStorebrandDuplicateCheckQuery } from '@/api/generated-client'
import { TrackPageVisit } from '@/components/track-page-visit'

export function StoreBrandSection({
  hasRequestedFinancingOffer,
}: {
  hasRequestedFinancingOffer: boolean
}) {
  const {
    locked: isAgreementLocked,
    setCustomerInfo,
    customerInfo,
    listingAgreement,
  } = useBudget()

  const { data, isLoading: isDuplicateCheckLoading } =
    useStorebrandDuplicateCheckQuery({
      input: { estateId: listingAgreement.estate_id },
    })

  const [expanded, setExpanded] = useState<boolean>(
    Boolean(customerInfo?.receive_loan_offer),
  )

  const { data: signers } = useEligibleSigners(listingAgreement.estate_id, true)

  const getRecipient = async () => {
    return getDefaultRecipient(
      signers,
      listingAgreement.recipient_loan_offer ?? undefined,
    )
  }

  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  const { comment, handleUpdateBuyProcess, handleUpdateHasCoBuyer } =
    createLoanOfferHandlers({
      customerInfo,
      setCustomerInfo,
      source: StorebrandRequestSourceEnum.Oppdragsavtale,
      onServerUpdate: (updates) => {
        // Convert the updates to the format expected by updateInfo
        if (updates.loanOfferComment) {
          updateInfo(
            { loanOfferComment: updates.loanOfferComment },
            'Kunne ikke oppdatere lånetilbud',
          )
        }

        // Handle other properties separately if needed
        if (updates.recipientLoanOffer !== undefined) {
          updateInfo(
            { recipientLoanOffer: updates.recipientLoanOffer },
            'Kunne ikke oppdatere lånetilbud',
          )
        }

        if (updates.receiveLoanOffer !== undefined) {
          updateInfo(
            { receiveLoanOffer: updates.receiveLoanOffer },
            'Kunne ikke oppdatere lånetilbud',
          )
        }
      },
    })

  const handleUpdateSellerWantsOffer = async (value: boolean) => {
    setExpanded(value)

    // If enabled, set recipient
    const recipient = value ? await getRecipient() : undefined

    startTransition(() => {
      setCustomerInfo((prevState) => ({
        ...prevState,
        receive_loan_offer: value,
        recipient_loan_offer: recipient ?? prevState.recipient_loan_offer,
      }))
    })

    // Server-side update
    updateInfo(
      {
        receiveLoanOffer: value,
        recipientLoanOffer: recipient ?? undefined,
        loanOfferComment: {
          ...comment,
          source: StorebrandRequestSourceEnum.Oppdragsavtale,
        },
      },
      'Kunne ikke oppdatere lånetilbud',
    )
  }

  const handleRecipientChange = (value: string) => {
    startTransition(() => {
      setCustomerInfo((prevState) => ({
        ...prevState,
        recipient_loan_offer: value,
      }))
    })

    // Server-side update
    updateInfo({ recipientLoanOffer: value }, 'Kunne ikke oppdatere lånetilbud')
  }

  // Don't show if there are existing Storebrand leads
  if (data?.storebrandDuplicateCheck.hasDuplicates) {
    return null
  }

  // Show loading state while checking for duplicates
  if (isDuplicateCheckLoading) {
    return (
      <Section className="@container/section">
        <SubSection
          title="Få tilbud på finansieringsbevis eller boliglån"
          loading
        >
          <p className="text-pretty max-w-[100ch] masked-placeholder-text">
            Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
            betingelser og fast kontaktperson. Bli kontaktet for et
            uforpliktende tilbud.
          </p>

          <div className="mt-6 w-1/2 h-4 masked-placeholder-text" />
        </SubSection>
      </Section>
    )
  }

  // if (isAgreementLocked || hasRequestedFinancingOffer) {
  //   return null
  // }

  const forceOpen = isAgreementLocked && hasRequestedFinancingOffer
  const isDisabled = isAgreementLocked || hasRequestedFinancingOffer

  return (
    <Section className="@container/section">
      <TrackPageVisit
        pageId="verdivurdering-agreement/storebrand-agreement-section"
        estateId={listingAgreement.estate_id}
      />
      <SubSection title="Få tilbud på finansieringsbevis eller boliglån">
        <p className="text-pretty max-w-[100ch]">
          Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
          betingelser og fast kontaktperson. Bli kontaktet for et uforpliktende
          tilbud.
        </p>
      </SubSection>
      <div className="flex gap-2 mt-6">
        <div className="flex gap-2">
          <Checkbox
            className="mt-0.5"
            id="bankOffer"
            onCheckedChange={handleUpdateSellerWantsOffer}
            defaultChecked={expanded}
            disabled={isDisabled}
          />
          <label className="typo-body-md" htmlFor="bankOffer">
            Ja, jeg vil bli kontaktet av Storebrand
          </label>
        </div>

        <div className="ml-auto self-end hidden @lg/section:block">
          <Image
            src={'/storebrand-logo-text-red.svg'}
            alt="Storebrand"
            width={216}
            height={16}
          />
        </div>
      </div>
      <AnimatePresence>
        {(expanded || forceOpen) && (
          <motion.div
            key="storebrand-offer"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <>
              <div className="mb-4 border-t border-muted pt-2 mt-4">
                <EligibleSigners
                  estateId={listingAgreement.estate_id}
                  currentRecipient={
                    listingAgreement.recipient_loan_offer ?? undefined
                  }
                  onChange={handleRecipientChange}
                  isDisabled={isDisabled}
                />
              </div>
              <StorebrandAgreementForm
                buyProcessStatus={comment.buy_process_status}
                hasCoBuyer={comment.has_co_buyer}
                isDisabled={isDisabled}
                handleUpdateBuyProcess={handleUpdateBuyProcess}
                handleUpdateHasCoBuyer={handleUpdateHasCoBuyer}
                isValuation
              />
            </>
          </motion.div>
        )}
      </AnimatePresence>
      <div className="w-full flex justify-end @lg/section:hidden mt-6">
        <Image
          src="/storebrand-logo-text-red.svg"
          alt="Storebrand"
          width={216}
          height={16}
        />
      </div>
    </Section>
  )
}
