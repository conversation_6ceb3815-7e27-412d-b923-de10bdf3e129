'use client'

import { motion } from 'framer-motion'
import { ArrowRightIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'

import { formatCurrency } from '@/lib/formatCurrency'

import { variants } from '../animations'

import {
  ValuationHeaderTimeline,
  ValuationHeaderTimelineState,
} from './valuation-header-timeline'

export function ValuationHeader({
  address,
  children,
  valuation,
  broker,
  className,
}: {
  address: string
  valuation?: number
  children: React.ReactNode
  broker?: React.ReactNode
  className?: string
}) {
  return (
    <header
      className={cn(
        'container mb-8 md:mb-12 mt-10 md:mt-16 flex flex-col',
        className,
      )}
    >
      <div>
        <motion.h1
          variants={variants.slideInFromBottom}
          className="typo-display-xl text-pretty"
        >
          Verdivurdering
        </motion.h1>
        <motion.div
          variants={variants.slideInFromBottom}
          className="typo-body-md ink-muted"
        >
          {address}
        </motion.div>
        {valuation && (
          <motion.h2
            className="typo-display--2xl ink-gold mt-6"
            variants={variants.slideInFromBottom}
          >
            {formatCurrency(valuation)}
          </motion.h2>
        )}
      </div>
      <div className="flex gap-x-8 lg:gap-x-12 gap-y-6 sm:gap-y-8 md:flex-row flex-col">
        <div className="basis-[45%] shrink grow min-w-0">{children}</div>
        {broker && (
          <>
            <motion.div
              variants={{
                hidden: { scaleY: 0.6, opacity: 0 },
                visible: { scaleY: 1, opacity: 1 },
              }}
              className="bg-stroke-muted basis-[1px] self-stretch max-md:hidden"
            />
            {broker}
          </>
        )}
      </div>
    </header>
  )
}

export const SignedStatusTimeline = ({
  state,
}: {
  state: ValuationHeaderTimelineState
}) => {
  return (
    <div className="flex flex-col gap-8 sm:max-w-[28rem]">
      <motion.div variants={variants.slideInFromBottom}>
        <ValuationHeaderTimeline state={state} />
      </motion.div>
      {state === 'initial' && (
        <motion.div
          variants={variants.slideInFromBottom}
          className="sm:self-start"
        >
          <Button
            variant="gold"
            href="."
            iconEnd={<ArrowRightIcon />}
            size="lg"
          >
            Oppdragsavtale
          </Button>
        </motion.div>
      )}
    </div>
  )
}

export const ValuationBreakdown = ({
  debt,
  totalPrice,
  squareMeterPrice,
}: {
  debt?: number | null
  totalPrice?: number | null
  squareMeterPrice?: number | null
}) => {
  return (
    <div className="flex flex-wrap gap-x-5 gap-y-2 w-full">
      <CurrencyCell label="Fellesgjeld" value={debt} />
      <CurrencyCell label="Totalt" value={totalPrice} />
      <CurrencyCell label="m² pris" value={squareMeterPrice} />
    </div>
  )
}

function CurrencyCell({
  label,
  value,
}: {
  label: string
  value?: number | null
}) {
  return (
    <motion.div
      className="basis-1/4 shrink grow"
      variants={variants.slideInFromBottom}
    >
      <p className="typo-body-md ink-muted">{label}</p>
      <p className="typo-body-md-bold md:typo-body-lg-bold whitespace-nowrap">
        {typeof value === 'number' ? (
          formatCurrency(value)
        ) : (
          <span className="ink-muted">–</span>
        )}
      </p>
    </motion.div>
  )
}
