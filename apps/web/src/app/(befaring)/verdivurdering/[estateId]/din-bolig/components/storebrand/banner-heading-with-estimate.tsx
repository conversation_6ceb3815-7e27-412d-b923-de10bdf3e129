import { HandCoinsIcon, HousePlusIcon, RulerDimensionLine } from 'lucide-react'
import React from 'react'

import { Dialog, DialogContent, DialogDescription } from '@nordvik/ui/dialog'
import { TextButton } from '@nordvik/ui/text-button'

import { MarketEstimate } from '@/external-services/eiendomsverdi/get-market-estimate'
import { Sale } from '@/external-services/eiendomsverdi/get-sales-of-estate'
import { formatCurrency } from '@/lib/formatCurrency'

import { EvValuation } from '../get-ev-valuation'

import { BannerPresentOptions } from './banner-present-options'
import { getForecastedIncrease } from './get-forecasted-increase'

export function BannerHeadingWithEstimate({
  lastSale,
  valuation,
  debt = 0,
  sqmPrice,
  mortgage,
  marketEstimate,
}: {
  lastSale: Sale | undefined
  valuation: EvValuation | undefined
  debt: number
  sqmPrice: number
  mortgage?: number
  marketEstimate?: MarketEstimate | null
}) {
  const adjustedMortgage = React.useMemo(() => {
    if (!mortgage) return undefined
    const price = lastSale?.salesPrice ?? lastSale?.askingPrice ?? 0
    if (!price) return mortgage

    return Math.min(mortgage, price * 0.75)
  }, [mortgage, lastSale])

  const currentValuation = valuation?.marketValue
    ? valuation.marketValue - debt
    : 0

  const additionalLoan = React.useMemo(
    () =>
      getForecastedIncrease({
        mortgage: adjustedMortgage,
        buyPrice: lastSale?.salesPrice ?? lastSale?.askingPrice ?? 0,
        buyDate:
          lastSale?.saleDate ?? lastSale?.judicialTransferDate ?? new Date(),
        currentValuation,
      }),
    [adjustedMortgage, currentValuation, lastSale],
  )

  const { estimateLow, estimateHigh } = React.useMemo(() => {
    if (!marketEstimate)
      return { estimateLow: undefined, estimateHigh: undefined }

    return {
      estimateLow: roundToTenThousands(
        marketEstimate.estimateLow + additionalLoan,
      ),
      estimateHigh: roundToTenThousands(
        marketEstimate.estimateHigh + additionalLoan,
      ),
    }
  }, [marketEstimate, additionalLoan])

  const [open, setOpen] = React.useState(false)

  const items = [
    {
      icon: HousePlusIcon,
      prefix: 'Kjøp en bolig med totalpris fra',
      value: (
        <span className="typo-body-lg-bold">
          {formatNumber(estimateLow)} –{' '}
          <span className="whitespace-nowrap">
            {formatNumber(estimateHigh)} kr
          </span>
        </span>
      ),
    },
    {
      icon: HandCoinsIcon,
      prefix: 'Utvid boliglånet ditt ytterligere med',
      value: (
        <span className="whitespace-nowrap typo-body-lg-bold">
          {formatCurrency(roundToTenThousands(additionalLoan))}
        </span>
      ),
    },
    {
      icon: RulerDimensionLine,
      prefix: 'Kjøp en bolig med opptil',
      value: (
        <span className="whitespace-nowrap typo-body-lg-bold">
          {formatNumber(additionalLoan / sqmPrice)} kvm
        </span>
      ),
      suffix: 'mer plass i ditt nabolag.',
    },
  ]
  return (
    <div className="max-lg:py-8 max-lg:px-5 p-10">
      <p className="typo-display-md">Disse mulighetene har du nå</p>

      <BannerPresentOptions items={items} />
      <p className="mt-6">
        <span className="mr-3">
          Tallene er et anslag basert på standard markedstall og viser ikke hva
          du kan få i lån eller hvor mye du kan refinansiere.{' '}
        </span>
        <TextButton onClick={() => setOpen(true)} className="inline">
          Slik finner vi tallene
        </TextButton>
      </p>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent title="Slik finner vi tallene" size="md">
          <DialogDescription>
            <div className="space-y-4 max-w-prose text-pretty">
              <p>
                Disse anslagene er bare en pekepinn basert på standard
                markedstall og viser ikke hva du kan få i lån eller hvor mye du
                kan refinansiere.
              </p>
              <div className="space-y-1">
                <p className="typo-title-xs">De er basert på:</p>
                <ul className="list-image-dot-gold list-inside">
                  <li>
                    Summen du kjøpte boligen for og forventet nedbetaling siden
                    kjøpet
                  </li>
                  <li>Boligens nye verdivurdering</li>
                  <li>En årsinntekt på 650 000kr / eier</li>
                  <li>5,7 % effektiv rente</li>
                  <li>30 års nedbetalingstid</li>
                  <li>75 % belåningsgrad</li>
                </ul>
              </div>
              <p>
                Anslagene tar ikke høyde for annen gjeld, annen egenkapital
                eller antall barn. For å vite hvor mye du kan kjøpe for eller
                refinansiere, anbefaler vi å prate med vår samarbeidspartner
                Storebrand.
              </p>
              <p>Hos Storebrand får du en fast rådgiver og raske svar.</p>
            </div>
          </DialogDescription>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function formatNumber(number?: number) {
  if (!number) return ''
  return Math.round(number).toLocaleString('nb-NO')
}

function roundToTenThousands(value: number) {
  return Math.round(value / 10_000) * 10_000
}
