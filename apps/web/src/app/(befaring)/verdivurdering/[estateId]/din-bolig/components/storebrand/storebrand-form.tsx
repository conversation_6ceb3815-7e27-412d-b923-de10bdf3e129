import { valuationCheckboxes } from '@befaring/components/storebrand-agreement-form'
import { EligibleSigners } from '@befaring/hooks/use-loan-offer-components'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { BuyProcessStatus } from '@befaring/lib/budget-helpers'
import { CachedEstate } from '@befaring/lib/cached-estate'
import { CachedListingAgreement } from '@befaring/lib/cached-listing-agreement'
import { StorebrandRequestSource } from '@befaring/verdivurdering/types'
import Image from 'next/image'
import { useState } from 'react'
import { useFormStatus } from 'react-dom'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import { ChoiceCards, ChoiceCardsProps } from '@nordvik/ui/choice-card'

import { logListingAgreementInteraction } from '@/actions/log-listing-agreement-interaction'
import { GQLListingAgreementInteractionType } from '@/api/generated-client'
import { useUserContext } from '@/lib/UserContext'
import { getCookie } from '@/utils/cookies'

import { LoanOfferComment } from '../../page'

export function StorebrandForm({
  listingAgreement,
  onSuccess,
  classNameSubmitButton,
  withLogo = true,
  source = 'din-bolig',
  estate,
}: {
  listingAgreement: Pick<
    CachedListingAgreement,
    'id' | 'loan_offer_comment' | 'receive_loan_offer'
  >
  onSuccess?: () => void
  variant?: ChoiceCardsProps['variant']
  classNameSubmitButton?: string
  // Whether to show the Storebrand logo
  withLogo?: boolean
  source?: StorebrandRequestSource
  estate?: CachedEstate
}) {
  const { user } = useUserContext()
  const cidCookie = getCookie('cid')
  const updateInfo = useUpdateListingAgreementCustomerInfo(
    listingAgreement.id,
    false,
  )
  const loanComment =
    listingAgreement?.loan_offer_comment as LoanOfferComment | null

  const [selectedCheckbox, setSelectedCheckbox] =
    useState<BuyProcessStatus | null>(loanComment?.buy_process_status ?? null)

  const [hasCoBuyer, setHasCoBuyer] = useState<boolean>(
    loanComment?.has_co_buyer ?? false,
  )

  const [selectedRecipient, setSelectedRecipient] = useState<
    string | undefined
  >(undefined)

  const mainSellerContactId =
    estate?.mainSeller?.contactId ??
    (estate?.sellers.length === 1 ? estate?.sellers[0].contactId : undefined)

  const handleContact = async () => {
    const sellerId = cidCookie ?? mainSellerContactId ?? selectedRecipient
    try {
      await updateInfo({
        receiveLoanOffer: true,
        recipientLoanOffer: sellerId,
        loanOfferComment: {
          ...loanComment,
          has_co_buyer: hasCoBuyer,
          buy_process_status: selectedCheckbox,
          source,
        },
      })

      if (estate?.estateId) {
        await logListingAgreementInteraction({
          event_type: GQLListingAgreementInteractionType.FinancingRequested,
          seller_id: user ? undefined : sellerId,
          listing_agreements_id: listingAgreement.id,
          extra_data: {
            buyProcessStatus: selectedCheckbox,
            hasCoBuyer,
            source,
          },
        })
      }

      onSuccess?.()
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <form className="space-y-6" action={handleContact}>
      <ChoiceCards
        value={selectedCheckbox ?? ''}
        onChange={(choice: BuyProcessStatus) => setSelectedCheckbox(choice)}
        variant="card"
        withRadio
        items={valuationCheckboxes.map((checkbox) => ({
          label: checkbox.label,
          value: checkbox.value,
          name: checkbox.name,
        }))}
        className="grid @md/section:grid-cols-2 @4xl/section:grid-cols-4 gap-4"
      />

      {/* Show contact selection if no seller ID in cookies and estate is available */}
      {!cidCookie && estate?.estateId && estate?.sellers.length > 1 && (
        <EligibleSigners
          estateId={estate.estateId}
          currentRecipient={selectedRecipient}
          onChange={setSelectedRecipient}
          isDisabled={false}
        />
      )}

      <label className="flex items-center gap-2">
        <Checkbox
          defaultChecked={hasCoBuyer}
          onCheckedChange={(checked: boolean) => setHasCoBuyer(checked)}
        />
        Jeg skal kjøpe sammen med noen
      </label>
      <div className="flex flex-col sm:flex-row items-baseline justify-between gap-2 space-y-4">
        <Submit classNameSubmitButton={classNameSubmitButton} />
        {withLogo && (
          <Image
            src="/storebrand-logo-text-red.svg"
            alt="Storebrand logo"
            width={217}
            height={16}
            className="sm:self-end"
          />
        )}
      </div>
    </form>
  )
}

function Submit({ classNameSubmitButton }: { classNameSubmitButton?: string }) {
  const { pending } = useFormStatus()
  return (
    <Button
      type="submit"
      loading={pending}
      className={cn(classNameSubmitButton, 'w-full sm:w-auto')}
      size="lg"
    >
      Kontakt meg
    </Button>
  )
}
