'use client'

import { useState } from 'react'

import { Button as DefaultButton } from '@nordvik/ui/button'

import { getEstateDocumentById } from '@/actions/next/estate-documents'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import { downloadBlob } from '@/lib/download-blob'

interface NextDocumentDownloadButtonProps {
  estateId: string
  documentId: string
  filename?: string
  docType?: (typeof DocumentTypeEnum)[keyof typeof DocumentTypeEnum]
  children?: React.ReactNode
  button?: React.ElementType
  loadingText?: string
}

export function NextDocumentDownloadButton({
  estateId,
  documentId,
  docType = DocumentTypeEnum.ElectronicValuation,
  ...props
}: NextDocumentDownloadButtonProps) {
  return (
    <DocumentDownloadButton
      {...props}
      downloadFn={async () => {
        const documentData = await getEstateDocumentById(
          estateId,
          documentId,
          docType,
        )

        if (!documentData) {
          console.error('Failed to fetch document')
          return null
        }

        return documentData
      }}
    />
  )
}

interface DocumentDownloadButtonProps {
  children?: React.ReactNode
  filename?: string
  button?: React.ElementType
  loadingText?: string
  downloadFn: () => Promise<Blob | null>
}

export function DocumentDownloadButton({
  children,
  button: Button = DefaultButton,
  filename = 'document',
  loadingText,
  downloadFn,
}: DocumentDownloadButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleDownload = async () => {
    setIsLoading(true)

    try {
      const documentData = await downloadFn()

      if (!documentData) {
        console.error('Failed to fetch document')
        return
      }

      const blob = new Blob([documentData as BlobPart], {
        type: 'application/octet-stream',
      })

      downloadBlob(blob, filename)
    } catch (error) {
      console.error('Error downloading document:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      onClick={handleDownload}
      disabled={isLoading}
      loading={Boolean(isLoading)}
      tooltip="Last ned dokument"
      size="lg"
    >
      {isLoading && loadingText ? loadingText : children}
    </Button>
  )
}
