'use client'

import { CachedEstate } from '@befaring/lib/cached-estate'
import { CachedListingAgreement } from '@befaring/lib/cached-listing-agreement'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { StorebrandRequestSourceEnum } from '@befaring/verdivurdering/types'
import { AnimatePresence, motion } from 'framer-motion'
import { CheckIcon } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTrigger,
} from '@nordvik/ui/dialog'

import { useStorebrandDuplicateCheckQuery } from '@/api/generated-client'
import { TrackPageVisit } from '@/components/track-page-visit'
import { useUserContext } from '@/lib/UserContext'

import { LoanOfferComment } from '../../page'

import { LoadingBanner } from './loading-banner'
import { StorebrandForm } from './storebrand-form'

export function MinimalStorebrandBanner({
  listingAgreement,
  estate,
}: {
  listingAgreement: Pick<
    CachedListingAgreement,
    'id' | 'loan_offer_comment' | 'receive_loan_offer' | 'signicat_document_id'
  >
  estate: CachedEstate
}) {
  const { user } = useUserContext()
  const trackEvent = useTrackListingAgreement(estate)
  const [open, setOpen] = useState(false)

  const { data, isLoading: isDuplicateCheckLoading } =
    useStorebrandDuplicateCheckQuery({ input: { estateId: estate.estateId } })

  const comment = listingAgreement.loan_offer_comment as LoanOfferComment | null

  const [isLocked, setLocalLockState] = useState(
    !!listingAgreement.receive_loan_offer &&
      comment?.source === StorebrandRequestSourceEnum.DinBoligMinimalBanner,
  )

  const signingInitialized = !!listingAgreement.signicat_document_id

  // Don't show if there are existing Storebrand leads
  if (data?.storebrandDuplicateCheck.hasDuplicates) {
    return null
  }

  // Show loading state while checking for duplicates
  if (isDuplicateCheckLoading) {
    return <LoadingBanner />
  }

  if (
    signingInitialized &&
    comment?.source === StorebrandRequestSourceEnum.Oppdragsavtale
  ) {
    return null
  }

  return (
    <>
      <TrackPageVisit
        pageId="verdivurdering-estate/storebrand-minimal-banner"
        estateId={estate.estateId}
      />
      <div className="border border-muted rounded-md max-md:p-6 p-8 flex gap-8 items-center justify-between flex-col md:flex-row">
        <div className="flex flex-col gap-1 grow">
          <p className="typo-title-sm">
            Få et godt tilbud på finansieringsbevis eller boliglån
          </p>
          <p className="typo-body-lg">
            Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
            betingelser og en fast kontaktperson.
          </p>
          <AnimatePresence>
            {isLocked && (
              <motion.div
                key="locked"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="mt-4"
              >
                <div className="flex gap-2 px-4 py-2 rounded-md bg-float max-w-max">
                  <CheckIcon className="size-5 ink-subtle shrink-0 mt-1.5" />
                  <p className="typo-body-lg ink-subtle whitespace-break-spaces">
                    Du vil bli kontaktet av Storebrand når e-taksten er klar.
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          <Image
            src="/storebrand-logo-text-white.svg"
            alt="Storebrand logo"
            width={217}
            height={16}
            className="mt-6 max-md:hidden"
          />
        </div>
        {!isLocked && (
          <div className="w-full md:w-auto">
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full md:w-auto"
                  size="lg"
                >
                  Kontakt meg
                </Button>
              </DialogTrigger>

              <DialogContent
                title="Få finansieringsbevis eller tilbud på boliglån"
                data-theme="dark"
                className="@container/section"
              >
                <DialogDescription>
                  <p className="typo-body-md mb-6">
                    Vi deler nødvendige opplysninger om deg og din e-takst med
                    Storebrand, slik at de kan gi deg et uforpliktende tilbud
                    innen en arbeidsdag.
                  </p>
                  <StorebrandForm
                    listingAgreement={listingAgreement}
                    onSuccess={() => {
                      setOpen(false)
                      setLocalLockState(true)
                      trackEvent('valuation_agreement_send_storebrand_lead', {
                        source:
                          StorebrandRequestSourceEnum.DinBoligMinimalBanner,
                        isBroker: Boolean(user),
                      })
                    }}
                    source={StorebrandRequestSourceEnum.DinBoligMinimalBanner}
                    classNameSubmitButton={cn('ml-auto mt-12')}
                    withLogo={false}
                    estate={estate}
                  />
                </DialogDescription>
              </DialogContent>
            </Dialog>
          </div>
        )}
        <Image
          src="/storebrand-logo-text-white.svg"
          alt="Storebrand logo"
          width={217}
          height={16}
          className="md:hidden self-start"
        />
      </div>
    </>
  )
}
