'use client'

import { updateListingAgreementCustomerInfo } from '@befaring/actions/listing-agreement'
import { pronoun } from '@befaring/lib/text-formatting'
import { InfoIcon } from 'lucide-react'
import { startTransition } from 'react'

import { Alert, AlertDescription } from '@nordvik/ui/alert'
import { Checkbox } from '@nordvik/ui/checkbox'
import { useToast } from '@nordvik/ui/toaster'

import { NextPrivateContact } from '@/actions/next/types-next'
import { formatCurrency } from '@/lib/formatCurrency'

import {
  CollapsibleSection,
  SectionContent,
} from '../../../../components/collapsible-section'
import SectionTotalRow from '../../../../components/section-total-row'
import { useBudget } from '../../../../context/budget-context'

export default function InsuranceSection({
  sellers,
}: {
  sellers: NextPrivateContact[]
}) {
  const { toast } = useToast()
  const {
    locked,
    sellerInsurance,
    hasInsurance,
    setHasInsurance,
    listingAgreement,
  } = useBudget()

  const handleToggleInsurance = async (checked: boolean) => {
    // useOptimistic requires a parent form with an action or wrap in startTransition
    startTransition(() => {
      setHasInsurance(checked)
    })

    try {
      await updateListingAgreementCustomerInfo({
        listingAgreementId: listingAgreement.id,
        sellerInsurance: checked,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: 'Fikk ikke lagret forsikringen',
        description: 'Prøv igjen senere',
        variant: 'destructive',
      })
    }
  }

  return (
    <CollapsibleSection title="Boligselgerforsikring">
      <SectionContent className="flex flex-col gap-6 px-[--padding-x] pb-6">
        <span className="typo-body-md">
          {pronoun(sellers, { perspective: 'second', capitalize: true })} kan
          bli ansvarlig for skjulte feil eller mangler i fem år etter at boligen
          er overtatt av ny eier. Med boligselgerforsikring får{' '}
          {pronoun(sellers, { perspective: 'second' })} juridisk bistand ved
          reklamasjon fra kjøper, og er dekket mot prisavslag, erstatninger og
          heving av kjøp.
        </span>

        <p className="typo-body-md">
          <span className="typo-body-md">
            For boligen gjelder følgende satser:
          </span>
          <br />
          <span className="typo-body-md">
            Promillesats:{' '}
            {(sellerInsurance?.premiumRate ?? 0).toString().replace('.', ',')}‰
            av salgssum.
          </span>
          <br />
          <span className="typo-body-md">
            Minimums- og maksimumspris:{' '}
            {formatCurrency(sellerInsurance?.minimumPremium ?? 0)} /{' '}
            {formatCurrency(sellerInsurance?.maximumPremium ?? 0)}
          </span>
        </p>
        <p className="typo-body-md">
          Estimert premie:{' '}
          {formatCurrency(sellerInsurance?.estimatedPremium ?? 0)}
        </p>

        <label htmlFor="insurance" className="flex items-center gap-2">
          <Checkbox
            id="insurance"
            name="insurance"
            checked={hasInsurance}
            onCheckedChange={handleToggleInsurance}
            // Should be editable by customer and broker until locked
            disabled={locked}
          />
          Ja, jeg ønsker boligselgerforsikring og forstår at dette gjøres ved
          signering av Storebrands egenerklæring
        </label>

        {hasInsurance && (
          <Alert variant="default" Icon={InfoIcon}>
            <AlertDescription>
              Forsikringen bestilles i egenerklæringsskjemaet som sendes ut
              etter at oppdragsavtalen er signert.
            </AlertDescription>
          </Alert>
        )}
      </SectionContent>
      <SectionTotalRow
        disabled={!hasInsurance}
        total={sellerInsurance?.sum ?? 0}
        title={hasInsurance ? 'Boligselgerforsikring' : 'Ingen forsikring'}
      />
    </CollapsibleSection>
  )
}
