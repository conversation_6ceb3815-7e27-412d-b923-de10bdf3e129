'use client'

import { ListingAgreementResponse } from '@befaring/actions/types'
import { UploadIcon } from 'lucide-react'

import { NextPrivateContact } from '@/actions/next/types-next'
import type {
  GQLAgreementAndInspectionQuery,
  GQLGetBrokerEstateQuery,
} from '@/api/generated-client'
import { SendOfferDialog } from '@/components/send-offer-dialog/send-offer-dialog'

import { useBudget } from '../../../../../context/budget-context'

export function SendOfferDialogWrapper({
  estate,
  sellers,
  budget,
}: {
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  sellers: NextPrivateContact[]
  budget: ListingAgreementResponse
}) {
  const { isUpdating } = useBudget()

  return (
    <SendOfferDialog
      estate={estate as GQLAgreementAndInspectionQuery['estate']}
      sellers={sellers}
      listingAgreementBudget={budget}
      isUpdating={isUpdating}
      triggerProps={{
        iconStart: <UploadIcon />,
        size: 'md',
      }}
    />
  )
}
