'use client'

import { updateListingAgreementCustomerInfo } from '@befaring/actions/listing-agreement'
import SectionTotalRow from '@befaring/components/section-total-row'
import Image from 'next/image'
import { startTransition } from 'react'

import { useToast } from '@nordvik/ui/toaster'

import { GQLMarketingPackage } from '@/api/generated-client'

import {
  CollapsibleSection,
  SectionContent,
} from '../../../../components/collapsible-section'
import { useBudget } from '../../../../context/budget-context'

import MarketingPackageButton from './marketing-package-button'
import MarketingReadMore from './marketing-read-more'

interface MarketingSectionClientProps {
  marketingPackages: GQLMarketingPackage[]
}

export default function MarketingSectionClient({
  marketingPackages,
}: MarketingSectionClientProps) {
  const { toast } = useToast()
  const {
    locked,
    selectedMarketingKey,
    setSelectedMarketingKey,
    listingAgreement,
  } = useBudget()

  const selectedPackage = marketingPackages.find(
    (mp) => mp.productTag === selectedMarketingKey,
  )

  const handleSelectPackage = async (marketingPackage: string | null) => {
    startTransition(() => setSelectedMarketingKey(marketingPackage))

    try {
      await updateListingAgreementCustomerInfo({
        listingAgreementId: listingAgreement.id,
        marketingPackage,
      })
    } catch (error) {
      console.error(error)
      toast({
        title: 'Kunne ikke lagret forsikringen',
        description: 'Prøv igjen senere',
        variant: 'destructive',
      })
    }
  }

  return (
    <CollapsibleSection title="Markedsføring" className="relative">
      <SectionContent className="flex flex-col gap-6 px-[--padding-x] pb-6">
        <MarketingReadMore />
        <h3 className="typo-title-sm">Nordvik Ekstra</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="flex flex-col gap-2">
            <div className="flex flex-col gap-2 rounded-lg bg-gold-muted p-6">
              <h4 className="typo-title-sm">I snitt får selger:</h4>
              <ul className="typo-body-md list-disc pl-4">
                <li>1,70 % høyere salgssum</li>
                <li>12 % flere visningspåmeldinger</li>
                <li>28 % flere interessenter</li>
              </ul>
              <span>
                Priseksempel: En bolig til 6 300 000 kr får i snitt 107 100 NOK
                mer med Nordvik Ekstra.
              </span>
            </div>
            <span className="typo-body-xs ink-muted">
              *Datagrunnlaget er gjennomsnitt av solgte boliger med (5 613 stk)
              og uten (6 763 stk) Nordvik Ekstra fra 2022 til 2024. Det ble
              gjort en endring på pakkesammensetningen i 2025 og det kan derfor
              være avvik fra tidligere år.
            </span>
          </div>

          <Image
            src="/nordvik-ekstra-graph.png"
            alt="Nordvik Ekstra"
            width={500}
            height={500}
          />
        </div>
        <h3 className="typo-title-sm">Oppgrader til</h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {marketingPackages.map((pack) => {
            const checked = pack.packageId === selectedPackage?.packageId
            const tag = pack.productTag!

            return (
              <MarketingPackageButton
                clicksInterval={pack.clicks}
                name={pack.name}
                price={pack.price}
                views={pack.views}
                channels={pack.channels}
                key={pack.productTag}
                checked={checked}
                disabled={locked}
                onClick={() => handleSelectPackage(checked ? null : tag)}
              />
            )
          })}
        </div>
      </SectionContent>
      <SectionTotalRow
        disabled={!selectedPackage}
        total={selectedPackage?.price ?? 0}
        title={selectedPackage?.name ?? 'Ingen pakke valgt'}
      />
    </CollapsibleSection>
  )
}
