'use client'

import type { ContractData, PdfBudgetPostData, ValuationContractData } from '.'
import type { ListingAgreement } from '@befaring/lib/budget-helpers'
import type { ListingAgreementBroker } from '@befaring/lib/format-brokers'
import type { SellerInsurance } from '@befaring/lib/prices'
import { pronoun } from '@befaring/lib/text-formatting'
import isEmpty from 'lodash/isEmpty'

import type {
  BudgetDataSection,
  MergedBudgetPost,
  NextAssignmentBudget,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import type {
  GQLGetBrokerEstateQuery,
  GQLMarketingPackage,
} from '@/api/generated-client'
import { formatCurrency } from '@/lib/formatCurrency'

import { renderContract } from './render-contract'

export interface GenerateContractHtmlProps {
  listingAgreement: ListingAgreement
  commission: number
  budget: NextAssignmentBudget
  budgetSum: number
  income: BudgetDataSection
  outlay: BudgetDataSection
  budgetDiscount?: MergedBudgetPost
  estate: GQLGetBrokerEstateQuery['estate']
  sellers: NextPrivateContactWithProxy[]
  brokers: ListingAgreementBroker[]
  sellerInsurance: SellerInsurance | null
  marketingPackage: GQLMarketingPackage | undefined
}

const commissionTypes = {
  1: 'percentage',
  2: 'fixed',
  3: 'hourly',
}

export const contractPronoun = (
  contractData: ContractData | ValuationContractData,
  options: {
    perspective: 'first' | 'second' | 'third'
    capitalize?: boolean
  },
) => {
  let sellers: unknown[] = contractData.sellers
  if ('sellerIsCompany' in contractData && contractData.sellerIsCompany) {
    if (contractData.sellers.length === 1) {
      const company = contractData.sellers[0]
      if (!company.hasProxy) {
        // If the seller is 1 company and has no proxy, we assume multiple sellers
        sellers = [1, 2]
      }
    }
  }

  return pronoun(sellers, options)
}

export function generateContractHtml({
  commission,
  budget,
  budgetSum,
  estate,
  listingAgreement,
  sellers,
  brokers,
  income,
  outlay,
  budgetDiscount,
  sellerInsurance,
  marketingPackage,
}: GenerateContractHtmlProps) {
  const {
    no_previous_brokers: noPreviousBrokers,
    previous_brokers: previousBrokers,
    fee_percentage: feePercentage,
    commission: fixedCommission,
    married_or_in_partnership: marriedOrInPartnership,
    waive_withdrawal_right: waiveWithdrawalRight,
    owner_is_seller: ownerIsSeller,
    is_common_estate: isCommonEstate,
    seller_is_shareholder: sellerIsShareholder,
  } = listingAgreement

  const hasCompanySeller = sellers.some((seller) => seller.contactType === 1)

  if (
    waiveWithdrawalRight === null ||
    noPreviousBrokers === null ||
    (feePercentage === null && fixedCommission === null) ||
    (!hasCompanySeller && marriedOrInPartnership === null) ||
    (ownerIsSeller === null && sellerIsShareholder === null)
  ) {
    console.error('missing required fields in listing agreement', {
      waiveWithdrawalRight,
      noPreviousBrokers,
      feePercentage,
      fixedCommission,
      marriedOrInPartnership,
      ownerIsSeller,
      sellerIsShareholder,
    })
    throw new Error('Missing required fields in listing agreement')
  }

  if (!estate?.estatePrice || !estate.address) {
    throw new Error('Missing estate data')
  }

  const department = estate.department

  const officeExpert = department?.employees?.find((employee) =>
    employee?.title?.toLowerCase().includes('fagansvarlig'),
  )
  const { assignmentNumber, ownershipType } = estate

  if (!department) {
    throw new Error('Missing department data')
  }

  const incomeExtraPosts: {
    name: string
    cost: number | null
    position?: 'start'
  }[] = [
    {
      name:
        budget.type === 1
          ? `Provisjon (basert på estimert salgssum)`
          : 'Fastprisbasert vederlag iht. pkt. 4.2',
      cost: commission,
      position: 'start',
    },
  ]

  if (budgetDiscount) {
    incomeExtraPosts.push({
      name: budgetDiscount.name,
      cost: budgetDiscount.amountWithTaxIncluded ?? 0,
    })
  }

  if (marketingPackage) {
    incomeExtraPosts.push({
      name: marketingPackage?.name ?? '',
      cost: marketingPackage.price ?? 0,
    })
  }

  const separateProvision = isEmpty(listingAgreement.separate_provision)
    ? undefined
    : listingAgreement.separate_provision

  return renderContract({
    ownerIsSeller,
    sellerIsShareholder,
    sellerIsCompany: !!estate.hasCompanySeller,
    assignmentNumber,
    noPreviousBrokers,
    ownershipType,
    previousBrokers,
    waiveWithdrawalRight,
    separateProvision,
    commission: {
      type: commissionTypes[budget.type],
      feePercentage: feePercentage ?? 0,
      fixedCommission: fixedCommission ?? 0,
    },
    brokers,
    officeExpert,
    estate: {
      suggestedPrice: listingAgreement.suggested_price ?? 0,
      address: estate.address.streetAddress!,
      postalOffice: `${estate.address.zipCode} ${estate.address.city}`,
      ownership: estate.ownership,
      partOwnership: estate.partOwnership ?? {},
      businessContact: estate.businessManagerContact?.companyName ?? '',
      matrikkel: {
        gnr: estate.landIdentificationMatrix?.gnr?.toString() ?? '',
        bnr: estate.landIdentificationMatrix?.bnr?.toString() ?? '',
        snr: estate.landIdentificationMatrix?.snr?.toString() ?? '',
        municipality: estate.address.municipality ?? '',
        ownPart: estate.landIdentificationMatrix?.ownPart?.toString() ?? '',
      },
      estateType: estate.estateType ?? '',
    },
    sellers: sellers.map(
      ({
        firstName,
        lastName,
        socialSecurity,
        mobilePhone,
        postalAddress,
        postalCode,
        city,
        email,
        proxy,
        companyName,
        organisationNumber,
        contactType,
      }) => {
        if (!postalAddress || !postalCode || !city) {
          throw new Error('Missing required fields in seller data')
        }
        if (contactType === 0) {
          if (
            !firstName ||
            !lastName ||
            !socialSecurity ||
            !mobilePhone ||
            !email
          ) {
            throw new Error('Missing required fields in seller data')
          }
        } else {
          if (!companyName || !organisationNumber) {
            throw new Error('Missing required fields in company seller data')
          }
        }

        const proxyInName = proxy
          ? ` v/${proxy.firstName} ${proxy.lastName}`
          : ''

        const mainSellerInName =
          estate.hasCompanySeller && estate.mainSeller
            ? ` v/${estate.mainSeller.firstName} ${estate.mainSeller.lastName}`
            : ''

        return {
          name: `${firstName} ${lastName}${proxyInName || mainSellerInName}`, // Add proxy to seller
          socialSecurity,
          mobilePhone,
          email,
          postalAddress,
          postalCode,
          contactType,
          hasProxy: !!proxy,
          city,
          companyName: `${companyName}${proxyInName || mainSellerInName}`, // Add proxy to company seller
          orgNumber: organisationNumber,
          idType: '', // TODO
          idNumber: '', // TODO
        }
      },
    ),
    marriedOrInPartnership: !!marriedOrInPartnership,
    isCommonEstate,
    department: {
      ...department,
      employees:
        estate.department?.employees?.map(
          (employee) => `${employee?.name} (${employee?.title})`,
        ) ?? [],
    },
    budget: {
      sellerInsurance,
      marketingPackage,
      costs: {
        Vederlag: formatBudgetPostsForPdf({
          data: income,
          sumLabel: 'Totalt vederlag inkl. mva. estimert til',
          extraPosts: incomeExtraPosts,
        }),
        Utlegg: formatBudgetPostsForPdf({
          data: outlay,
          sumLabel: 'Utlegg utgjør totalt',
        }),
        'Andre utgifter': [
          {
            description: `Beregnet Boligselgerforsikring basert på prisantydning ${formatCurrency(
              listingAgreement.suggested_price ?? 0,
            )} (se pkt. 4.4)`,
            cost: sellerInsurance?.estimatedPremium ?? 0,
          },
          {
            description: 'Andre utgifter utgjør totalt',
            cost: sellerInsurance?.estimatedPremium ?? 0,
            bold: true,
          },
        ],
      },
      sum: budgetSum,
    },
  })
}

function formatBudgetPostsForPdf({
  data,
  sumLabel,
  extraPosts,
}: {
  data: BudgetDataSection
  sumLabel: string
  extraPosts?: {
    name?: string
    cost?: number | null
    position?: string
  }[]
  lastAdditionalPost?: { name?: string; cost?: number | null }
}) {
  const posts: PdfBudgetPostData[] = data.posts.map((post) => ({
    description: post.name,
    cost: post.amountWithTaxIncluded,
  }))

  if (extraPosts) {
    extraPosts.forEach((post) => {
      if (post.position === 'start') {
        posts.unshift({
          description: post.name ?? '',
          cost: post.cost ?? 0,
        })
      } else {
        posts.push({
          description: post.name ?? '',
          cost: post.cost ?? 0,
        })
      }
    })
  }

  const extraPostsCost =
    extraPosts?.reduce((acc, post) => acc + (post.cost ?? 0), 0) ?? 0

  // Total row
  posts.push({
    description: sumLabel,
    cost: (data.sum ?? 0) + extraPostsCost,
    bold: true,
  })

  return posts
}
