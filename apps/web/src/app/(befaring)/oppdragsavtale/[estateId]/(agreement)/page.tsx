import { cachedSigners } from '@befaring/actions/sellers'
import { NoEstateError } from '@befaring/lib/errors'
import { ArrowRight } from 'lucide-react'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

import { But<PERSON> } from '@nordvik/ui/button'

import { TrackPageVisit } from '@/components/track-page-visit'
import signicat from '@/external-services/signicat/client'

import BudgetSection from './components/budget-section'
import GeneralSection from './components/general-section'
import InsuranceSection from './components/insurance-section'
import MarketingSectionClient from './components/marketing-section.client'
import MarketingSection from './components/marketing-section.server'
import SeparateProvisionSection from './components/separate-provisions-section'
import SignerStatusSection from './components/signer-status-section'
import SigningRedirectStatus from './components/signing-redirect-status'

export const metadata: Metadata = {
  appLinks: {
    ios: {
      url: '/oppdragsavtale',
      app_store_id: '',
    },
    android: {
      package: 'com.nordvik.nordvikapp',
      app_name: 'android_app',
    },
  },
}

export default async function Page(props: {
  params: Promise<{ estateId: string }>
  searchParams: Promise<{ 'idfy-jwt'?: string }>
}) {
  const [searchParams, params] = await Promise.all([
    props.searchParams,
    props.params,
  ])

  const { 'idfy-jwt': jwt } = searchParams

  const { estateId } = params

  if (jwt) {
    signicat
      .validateRedirectJwt(jwt)
      .then((result) => {
        console.info(`Signicat JWT result: ${JSON.stringify(result)}`)
        if (result.error) {
          console.error(`Signicat JWT error: ${result.error}`)
        }
      })
      .catch((error) => {
        console.error(`Signicat JWT validation failed: ${error}`)
      })
  }

  try {
    var signers = await cachedSigners(estateId, true)
  } catch (error) {
    if (error instanceof NoEstateError) {
      notFound()
    }
    throw error
  }

  return (
    <>
      {/* using suspense to avoid forced dynamic render when using useSearchParams */}
      <TrackPageVisit pageId="befaring-agreement/budget" estateId={estateId} />
      <Suspense fallback={null}>
        <SigningRedirectStatus />
      </Suspense>
      <SignerStatusSection estateId={estateId} />
      <div className="sm:container max-sm:mt-6 mb-8 sm:my-8 md:my-8 flex flex-col gap-6 md:gap-8">
        <BudgetSection sellers={signers} />
        <Suspense fallback={<MarketingSectionClient marketingPackages={[]} />}>
          <MarketingSection />
        </Suspense>
        <InsuranceSection sellers={signers} />
        <SeparateProvisionSection estateId={estateId} />
        <Suspense fallback={null} key={estateId}>
          <GeneralSection estateId={estateId} />
        </Suspense>
        <div className="max-sm:px-[--padding-x] md:flex justify-end">
          <Button
            prefetch
            href={`/oppdragsavtale/${estateId}/parter`}
            size="lg"
          >
            Gå videre <ArrowRight className="size-5 max-md:hidden" />
          </Button>
        </div>
      </div>
    </>
  )
}
