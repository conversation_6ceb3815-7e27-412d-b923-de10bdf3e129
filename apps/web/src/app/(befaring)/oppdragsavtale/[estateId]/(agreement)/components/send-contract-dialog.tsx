'use client'

import {
  getCurrentSellerSignLink,
  getListingAgreementDocumentId,
  initiateSigning,
  retractInitiatedSigning,
} from '@befaring/actions/listing-agreement'
import { getSignersData } from '@befaring/actions/sellers'
import { handleSign } from '@befaring/actions/signing'
import { yupSellerSchema } from '@befaring/lib/check-valid-fields'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { StorebrandRequestSourceEnum } from '@befaring/verdivurdering/types'
import uniqBy from 'lodash/uniqBy'
import { PencilLineIcon } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { Button, ButtonProps } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { useToast } from '@nordvik/ui/toaster'

import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'
import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { flattenValidationErrors } from '@/app/(protected)/(sidebar)/oppdrag/util'
import { BankIdButton } from '@/components/bank-id-button'
import { ErrorList } from '@/components/error-list'
import { UserAvatar } from '@/components/user-avatar'
import { useValidateListingAgreement } from '@/hooks/use-validate-listing-agreement'
import { useUserContext } from '@/lib/UserContext'
import { getCookie } from '@/utils/cookies'
import { scrollToTopWithDelay } from '@/utils/scroll-to-top-with-delay'

import { useBudget } from '../../../../context/budget-context'
import { useValidateForm } from '../../../../hooks/useValidateForm'

import { CreatingDocumentLoader } from './creating-document-loader'

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export function SendContractDialog({
  estate,
  sellers,
  triggerProps,
  buttonText,
}: {
  estate: NonNullable<GQLGetBrokerEstateQuery['estate']>
  sellers: NextPrivateContactWithProxy[]
  setErrorFields?: (fields: string[]) => void
  triggerProps?: ButtonProps
  buttonText?: string
}) {
  const trackEvent = useTrackListingAgreement(estate)
  const [loading, setLoading] = useState(false)
  const [showUploadingPopup, setShowUploadingPopup] = useState(false)
  const [uploadingPopupMessage, setUploadingPopupMessage] = useState(
    'Dette tar vanligvis opptil 10 sekunder, men kan ta lengre tid.',
  )
  const [openConfirmationDialog, setOpenConfirmationDialog] = useState(false)
  const { user } = useUserContext()
  const { toast } = useToast()
  const cid = getCookie('cid')
  const router = useRouter()
  const { validateListingAgreement } = useValidateListingAgreement(estate)

  const { validateSync: validateSellers } = useValidateForm<NextPrivateContact>(
    estate,
    yupSellerSchema,
    {
      fieldsTranslation: {
        contactId: 'kontakt-ID',
        socialSecurity: 'fødselsnummer',
        email: 'e-post',
        mobilePhone: 'telefonnummer',
        firstName: 'fornavn',
        lastName: 'etternavn',
        postalAddress: 'adresse',
        postalCode: 'postnummer',
        city: 'poststed',
      },
    },
  )

  const {
    brokers,
    budget,
    listingAgreement,
    income,
    outlay,
    budgetDiscount,
    sellerInsurance,
    commission,
    budgetSum,
    marketingPackage,
    isUpdating,
    setDocumentId,
    setErrorFields,
    locked,
    valuation,
    customerInfo,
  } = useBudget()

  async function handleInitiateSigning(redirectDirectly?: boolean) {
    setLoading(true)

    if (!estate) {
      throw new Error('Missing estate')
    }
    const { errors } = await validateListingAgreement({
      ...listingAgreement,
      commission: listingAgreement.commission ?? undefined,
      valuation: valuation?.post?.amountWithTaxIncluded ?? undefined,
    })

    if (errors && errors.length > 0) {
      setLoading(false)
      scrollToTopWithDelay()
      return
    }

    const sellersData = await getSignersData({
      estateId: estate.estateId,
    })

    const sellersToCheck = [...sellersData]

    if (estate.hasCompanySeller) {
      const companySellersData = await getSignersData({
        estateId: estate.estateId,
        signersIfCompany: true,
      })

      sellersToCheck.push(...companySellersData)
    }

    const sellerValidationResults = sellersToCheck.map((seller) =>
      validateSellers(seller),
    )

    const sellersFlattenedErrors = flattenValidationErrors(
      sellerValidationResults,
    )

    if (sellersFlattenedErrors.length > 0) {
      setErrorFields(sellersFlattenedErrors)
      setLoading(false)
      setShowUploadingPopup(false)
      setOpenConfirmationDialog(false)
      setErrorFields(errors)
      scrollToTopWithDelay()
      return toast({
        title: 'Mangler informasjon om selgerne',
        description: <ErrorList errors={sellersFlattenedErrors} />,
        variant: 'destructive',
      })
    }

    setShowUploadingPopup(true)

    const initiated = await initiateSigning(listingAgreement.id, cid).then(
      (response) => {
        if (response?.length) {
          try {
            return JSON.parse(response)
          } catch (error) {
            console.error(error)
          }
        }
      },
    )

    const signingWithRetry = async (triesLeft: number) => {
      try {
        let documentId: string | undefined

        if (initiated?.id) {
          const documentResponse = await handleSign({
            data: {
              sellers: sellersData,
              sellerInsurance,
              marketingPackage,
              brokers,
              estate,
              budget,
              commission,
              budgetSum,
              listingAgreement,
              income,
              outlay,
              budgetDiscount,
            },
            options: {
              skipInitialNotificationFor: Boolean(!user) && cid ? [cid] : [],
            },
          })

          setDocumentId(documentResponse?.documentId)
          documentId = documentResponse?.documentId
        } else {
          const persistedDocumentId = await getListingAgreementDocumentId(
            listingAgreement.id,
          )
          if (persistedDocumentId) {
            documentId = persistedDocumentId
          } else {
            // if the signing was initiated but the documentId is missing, retry
            if (triesLeft <= 0) {
              throw new Error('Failed to get documentId')
            }

            await delay(3000)

            return await signingWithRetry(triesLeft - 1)
          }
        }

        if (user) {
          trackEvent('listing_agreement_send_to_sign')
        } else {
          trackEvent('listing_agreement_go_to_sign')
        }

        const loanOfferComment = customerInfo?.loan_offer_comment as {
          source: StorebrandRequestSourceEnum
        }
        const hasBeenRequestedInListingAgreement =
          loanOfferComment?.source ===
          StorebrandRequestSourceEnum.Oppdragsavtale

        if (hasBeenRequestedInListingAgreement && estate.isValuation) {
          trackEvent('valuation_agreement_send_storebrand_lead', {
            source: StorebrandRequestSourceEnum.Oppdragsavtale,
            isBroker: Boolean(user),
          })
        }

        if (redirectDirectly) {
          if (!documentId) {
            throw new Error('Missing document response')
          }

          const signerUrl = await getCurrentSellerSignLink({
            documentId,
            contactId: cid,
          })

          // fallback to revalidate path if signerUrl is missing
          if (!signerUrl) {
            return router.refresh()
          }

          window.location.assign(signerUrl)
        } else {
          setUploadingPopupMessage('Ferdigstiller oppdragsavtalen')
          scrollToTopWithDelay()
          router.refresh()
        }
      } catch (error) {
        setShowUploadingPopup(false)
        if (initiated) {
          void retractInitiatedSigning(listingAgreement.id)
        }
        console.error(error)
        toast({
          title: 'Det skjedde en feil ved generering av kontraktsdokumentet',
          variant: 'destructive',
        })
        setLoading(false)
      }
    }

    try {
      await signingWithRetry(4)
    } catch (error) {
      console.error(error)
      // reload the page if the signing fails
      router.refresh()
    }
  }

  const actionLabel =
    buttonText || (user ? 'Send til signering' : 'Start signering')

  const handleOnClick = async (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault()
    setLoading(true)
    if (!estate) {
      throw new Error('Missing estate')
    }
    const sellersData = await getSignersData({
      estateId: estate.estateId,
      onlyUnique: true,
      signersIfCompany: true,
    })

    const sellersToCheck = [...sellersData]

    if (estate.hasCompanySeller) {
      const companySellersData = await getSignersData({
        estateId: estate.estateId,
        onlyUnique: true,
      })

      sellersToCheck.push(...companySellersData)
    }

    const sellerValidationResults = uniqBy(sellersToCheck, 'contactId').map(
      (seller) => validateSellers(seller),
    )

    const sellersFlattenedErrors = flattenValidationErrors(
      sellerValidationResults,
    )

    const { errors: listingAgreementErrors } = validateListingAgreement({
      ...listingAgreement,
      commission: listingAgreement.commission ?? undefined,
      valuation: valuation?.post?.amountWithTaxIncluded ?? undefined,
    })

    if (
      listingAgreementErrors?.length ||
      sellersFlattenedErrors?.length ||
      !sellersData.length
    ) {
      const errors = listingAgreementErrors || []
      if (sellersFlattenedErrors?.length || !sellersData.length) {
        console.error('Missing seller data', sellersFlattenedErrors)
        errors.unshift({
          path: 'seller-info',
          message: 'Mangler informasjon om selgerne',
          readableFieldName: 'selgerne',
        })
      }
      setErrorFields(errors)

      scrollToTopWithDelay()
      setLoading(false)
      return toast({
        title: 'Mangler informasjon',
        description: <ErrorList errors={errors} />,
        variant: 'destructive',
      })
    }
    setLoading(false)
    setErrorFields(null)
    setOpenConfirmationDialog(true)
  }

  const uniqueSellers = uniqBy(
    sellers.map((seller) => seller.proxy ?? seller),
    'contactId',
  )

  if (locked) {
    // keep the loader for the intermediate state
    return (
      <CreatingDocumentLoader
        open={showUploadingPopup}
        onOpenChange={setShowUploadingPopup}
      >
        {uploadingPopupMessage}
      </CreatingDocumentLoader>
    )
  }

  return (
    <>
      <CreatingDocumentLoader
        open={showUploadingPopup}
        onOpenChange={setShowUploadingPopup}
      >
        {uploadingPopupMessage}
      </CreatingDocumentLoader>
      <Dialog
        open={openConfirmationDialog}
        onOpenChange={setOpenConfirmationDialog}
      >
        <DialogTrigger asChild>
          {user ? (
            <Button
              {...triggerProps}
              loading={loading || isUpdating}
              onClick={handleOnClick}
              iconStart={<PencilLineIcon />}
              iconOnly={undefined}
              tooltip="Starter signeringen av oppdragsavtalen med BankID. Selger og
                ansvarlig megler mottar en lenke på e-post og sms for å signere."
              size="lg"
            >
              {actionLabel}
            </Button>
          ) : (
            <Button
              {...triggerProps}
              loading={loading || isUpdating}
              onClick={handleOnClick}
              iconStart={<PencilLineIcon />}
              iconOnly={undefined}
              size="lg"
            >
              {actionLabel}
            </Button>
          )}
        </DialogTrigger>
        <DialogContent
          title={actionLabel}
          size="md"
          subtitle="Oppdragsavtalen vil låses for endringer"
        >
          <DialogDescription divider>
            {user ? (
              <>
                <p>
                  {sellers.length > 1 ? 'Selgere' : 'Selger'} mottar lenke til
                  signering med BankID på e-post og sms. Ansvarlig megler mottar
                  lenke på e-post.
                </p>
                <p className="typo-label-md mt-4 mb-2">
                  {sellers.length > 1 ? 'Mottakere' : 'Mottaker'}
                </p>
                <div className="flex flex-col gap-2">
                  {uniqueSellers.map(({ firstName, lastName, contactId }) => (
                    <div className="flex items-center gap-2" key={contactId}>
                      <UserAvatar user={{ name: `${firstName} ${lastName}` }} />
                      <span className="typo-body-md">{`${firstName} ${lastName}`}</span>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <p>
                Dokumentet blir låst og du blir tatt til BankID for signering.
                Du kan ikke gjøre endringer i avtalen etter dette.
              </p>
            )}
          </DialogDescription>
          <DialogFooter>
            <DialogClose asChild>
              <Button size="md" variant="ghost">
                Avbryt
              </Button>
            </DialogClose>
            {user ? (
              <Button
                size="md"
                onClick={() => handleInitiateSigning(!user)}
                loading={isUpdating}
                disabled={isUpdating}
                iconStart={<PencilLineIcon />}
              >
                {actionLabel}
              </Button>
            ) : (
              <BankIdButton />
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
