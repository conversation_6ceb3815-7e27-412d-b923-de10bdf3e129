'use client'

import {
  createBudgetPost,
  updateBudgetPost,
} from '@befaring/actions/budgetPost'
import { BadgePercent, Pencil, Plus, Trash } from 'lucide-react'
import { useState } from 'react'

import { cn } from '@nordvik/theme/cn'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogTitle,
} from '@nordvik/ui/alert-dialog'
import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { useToast } from '@nordvik/ui/toaster'

import { MergedBudgetPost } from '@/actions/next/types-next'
import { useUserContext } from '@/lib/UserContext'
import { formatCurrency } from '@/lib/formatCurrency'

import { useEstateId } from '../../../../../../hooks/use-estateid'
import { useBudget } from '../../../../context/budget-context'

export default function AddDiscountRow() {
  const { budgetDiscount, locked, setBudgetDiscount } = useBudget()
  const initialPrice = budgetDiscount?.amountWithTaxIncluded.toString() ?? '0'
  const { user } = useUserContext()
  const [open, setOpen] = useState(false)
  const [price, setPrice] = useState(initialPrice)
  const [removeAlertOpen, setRemoveAlertOpen] = useState(false)

  const estateId = useEstateId()
  const { toast } = useToast()

  async function handleSave(passedPrice?: string) {
    setOpen(false)
    // VITEC API expects a positive value for discounts for POST but returns a negative value for GET. Local state should mirror the API.
    const parsedPrice = Math.abs(parseInt(passedPrice ?? price))

    const budgetDiscountBefore = budgetDiscount
      ? { ...budgetDiscount }
      : undefined

    setBudgetDiscount({
      ...(budgetDiscount ?? {
        name: 'Rabatt',
      }),
      amountWithTaxIncluded: parsedPrice * -1,
    } as MergedBudgetPost)

    try {
      if (!estateId) {
        throw new Error('EstateId is not defined')
      }
      if (budgetDiscount) {
        await updateBudgetPost(
          {
            budgetPostId: budgetDiscount.budgetpostId,
            estateId,
            price: parsedPrice === 0 ? 0.001 : parsedPrice,
          },
          budgetDiscount,
        )
      } else {
        await createBudgetPost({
          estateId,
          price: parsedPrice,
          productTag: 'RABATT',
        })
      }
    } catch (error) {
      console.error(error)
      setBudgetDiscount(budgetDiscountBefore)
      toast({
        title: 'Kunne ikke lagre rabatt',
        description: 'Forsøk å laste siden på nytt',
        variant: 'destructive',
      })
    }
  }

  function updatePrice(e: React.ChangeEvent<HTMLInputElement>) {
    const value = e.target.value
    const numberValue = parseInt(value)
    if (isNaN(numberValue)) {
      setPrice(`-${value.replace(/[^0-9]/g, '')}`)
    } else {
      const absoluteValue = Math.abs(numberValue) * -1
      setPrice(absoluteValue.toString())
    }
  }

  async function onRemoveBudgetPost() {
    setRemoveAlertOpen(false)
    const passedPrice = '0'
    if (budgetDiscount) {
      setPrice(passedPrice)
      try {
        await handleSave(passedPrice)
      } catch (error) {
        console.error(error)
        toast({
          title: 'Kunne ikke slette rabatt',
          description: 'Forsøk å laste siden på nytt',
          variant: 'destructive',
        })
      }
    }
  }

  if (removeAlertOpen) {
    return (
      <AlertDialog open={removeAlertOpen} onOpenChange={setRemoveAlertOpen}>
        <AlertDialogContent className="w-96 rounded-md">
          <AlertDialogTitle className="typo-title-sm m-0 mt-1">
            Slette rabatt
          </AlertDialogTitle>
          <div className="typo-body-sm ink-muted">
            Er du sikker på at du vil slette rabatten?
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel asChild>
              <Button size="sm">Avbryt</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button size="sm" onClick={onRemoveBudgetPost}>
                Slett rabatt
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  }

  if (open) {
    return (
      <DiscountRowWrapper className="flex-col">
        <DiscountRowTitle
          onRemove={budgetDiscount ? () => setRemoveAlertOpen(true) : undefined}
        />
        <p className="typo-body-md">Rabatten trekkes fra meglers provisjon</p>

        <div className="flex max-sm:flex-col items-center gap-2 max-sm:gap-4">
          <Input value={price} onChange={updatePrice} addonRight="kr" />
          <div className="flex gap-2 max-sm:self-end sm:hidden">
            <Button variant="outline" size="sm" onClick={() => setOpen(false)}>
              Avbryt
            </Button>
            <Button size="sm" onClick={() => handleSave()}>
              Lagre
            </Button>
          </div>
          <div className="flex gap-2 max-sm:self-end max-sm:hidden">
            <Button variant="outline" onClick={() => setOpen(false)} size="lg">
              Avbryt
            </Button>
            <Button onClick={() => handleSave()} size="lg">
              Lagre
            </Button>
          </div>
        </div>
      </DiscountRowWrapper>
    )
  }

  // When discount exists and when customer is viewing
  if (budgetDiscount && parseInt(price) !== 0) {
    return (
      <DiscountRowWrapper className="justify-between">
        <DiscountRowTitle />
        <div className="flex items-center gap-2">
          <p className="typo-body-lg font-medium ink-success">
            {formatCurrency(parseInt(price))}
          </p>
          {Boolean(user && !locked) && (
            <Button
              className="ink-disabled"
              variant="ghost"
              size="sm"
              iconOnly={<Pencil />}
              onClick={() => setOpen(true)}
            >
              Endre
            </Button>
          )}
        </div>
      </DiscountRowWrapper>
    )
  }

  if (!user || locked) {
    return null
  }

  return (
    <Button
      variant="outline"
      onClick={() => setOpen(true)}
      className="mx-6 my-4 w-max"
      size="lg"
    >
      <Plus size={16} />
      Legg til
    </Button>
  )
}

function DiscountRowWrapper({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className="flex flex-col gap-4 rounded-md bg-gray-muted max-sm:p-4 p-6 m-4">
      <div className={cn('flex gap-4', className)}>{children}</div>
    </div>
  )
}

function DiscountRowTitle({ onRemove }: { onRemove?: () => void }) {
  return (
    <div className="flex justify-between">
      <div className="flex items-center gap-2">
        <BadgePercent size={20} />
        <h3 className="typo-body-md sm:typo-display-xs">Rabatt</h3>
      </div>
      {onRemove && (
        <Button
          onClick={onRemove}
          variant="ghost"
          iconOnly={<Trash size={20} />}
          size="lg"
        >
          Slett
        </Button>
      )}
    </div>
  )
}
