'use client'

import { EditModeSwitch } from '@befaring/components/edit-mode-switch'
import SectionTotalRow from '@befaring/components/section-total-row'
import { pronoun } from '@befaring/lib/text-formatting'

import { useUserContext } from '@/lib/UserContext'

import {
  CollapsibleSection,
  SectionContent,
} from '../../../../components/collapsible-section'
import { useBudget } from '../../../../context/budget-context'

import AddDiscountRow from './add-discount-row'
import { BudgetCostReadMore } from './budget-cost-read-more'
import BudgetReadMore from './budget-read-more'
import BudgetTable from './budget-table'
import CommissionRow from './commission-row'

export default function BudgetSection({ sellers }: { sellers: unknown[] }) {
  const {
    income,
    outlay,
    commission,
    budgetSum,
    budget,
    locked,
    editMode,
    setEditMode,
  } = useBudget()
  const { user } = useUserContext()

  const You = pronoun(sellers, { perspective: 'second', capitalize: true })

  return (
    <CollapsibleSection
      title="Kostnadsoppstilling"
      editMode={editMode}
      action={
        Boolean(user && !locked) && (
          <EditModeSwitch editMode={editMode} setEditMode={setEditMode} />
        )
      }
    >
      <SectionContent className="flex flex-col">
        <BudgetTable
          type="income"
          title="Vederlag"
          description={
            <div className="flex gap-1.5 flex-wrap">
              <p className="ink-subtle">
                {budget.type === 1
                  ? `${You} betaler for provisjon av salgssum i tillegg til faste kostnader.`
                  : `${You} betaler en fast pris på provisjon. I tillegg kommer faste kostnader.`}
              </p>
              <BudgetCostReadMore />
            </div>
          }
          data={income}
          sumAddition={commission}
          totalLabel={
            <>
              <span className="max-sm:hidden">
                Totalt vederlag inkl. mva. estimert til
              </span>
              <span className="sm:hidden">
                Totalt vederlag{' '}
                <span className="font-normal">(inkl. mva.)</span>
              </span>
            </>
          }
          prependedRow={<CommissionRow editMode={editMode} />}
        />
        <AddDiscountRow />

        <BudgetTable
          type="outlay"
          title="Utlegg"
          description="Dette er kostnader og gebyrer som må betales til andre i forbindelse med salget"
          data={outlay}
          totalLabel="Utlegg utgjør totalt"
        />
        <BudgetReadMore sellers={sellers} />
      </SectionContent>
      <SectionTotalRow
        total={budgetSum}
        title="Sum kostnader"
        description="Stipulerte totale kostnader eksklusive Boligselgerforsikring"
      />
    </CollapsibleSection>
  )
}
