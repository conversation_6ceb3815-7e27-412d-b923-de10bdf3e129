'use client'

import { updateListingAgreement } from '@befaring/actions/listing-agreement'
import debounce from 'lodash/debounce'
import React from 'react'

import { useUserContext } from '@/lib/UserContext'

import {
  CollapsibleSection,
  SectionContent,
} from '../../../../components/collapsible-section'
import { useBudget } from '../../../../context/budget-context'

const debouncedUpdate = debounce(async (separateProvision, estateId) => {
  try {
    await updateListingAgreement({ separateProvision, estateId })
  } catch (error) {
    console.error('Failed to update separate provision', error)
  }
}, 500)

export default function SeparateProvisionSection({
  estateId,
}: {
  estateId: string
}) {
  const { locked, listingAgreement } = useBudget()
  const { user } = useUserContext()
  const [separateProvision, setSeparateProvision] = React.useState(
    listingAgreement.separate_provision,
  )

  const handleOnChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSeparateProvision(e.target.value)
  }

  React.useEffect(() => {
    const prevProvision = listingAgreement.separate_provision ?? null
    const currProvisionState = separateProvision ?? null

    if (user && prevProvision !== currProvisionState) {
      debouncedUpdate(separateProvision, estateId)

      // Cleanup on unmount or if dependencies change
      return () => {
        debouncedUpdate.cancel()
      }
    }
  }, [estateId, separateProvision, user, listingAgreement.separate_provision])

  if ((locked || !user) && !separateProvision) {
    return null
  }

  return (
    <CollapsibleSection
      title="Særskilte bestemmelser"
      defaultOpen={
        separateProvision && separateProvision.length > 0 ? true : false
      }
    >
      <SectionContent className="flex flex-col gap-6 px-[--padding-x] pb-6">
        <div className="flex flex-col gap-4">
          <p className="typo-body-md">
            Bestemmelser satt av eiendomsmegler som gjelder spesielt for dette
            oppdraget. Følgende bestemmelser går foran eventuelle generelle
            bestemmelser som ellers ville gjeldt:
          </p>
          {!locked && user ? (
            <textarea
              className="min-h-28 w-full rounded-sm border border-inputs-border p-3"
              value={separateProvision ?? undefined}
              onChange={handleOnChange}
            />
          ) : (
            <div className="whitespace-pre-wrap">{separateProvision}</div>
          )}
        </div>
      </SectionContent>
    </CollapsibleSection>
  )
}
