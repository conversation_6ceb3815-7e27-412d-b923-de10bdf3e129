import { cachedEstate } from '@befaring/lib/cached-estate'
import { cachedUpsertListingAgreement } from '@befaring/lib/cached-upsert-listing-agreement'
import { Metadata } from 'next'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import prisma from '@/db/prisma'
import { getCurrentUser } from '@/lib/session'

import { BudgetProvider } from '../../../context/budget-context'
import { NoEstateError } from '../../../lib/errors'
import { TrackListingAgreementOnce } from '../../../lib/track-listing-agreement'

import Header from './components/header'
import LastUpdateStatus from './components/last-update-status'
import MissingNextData from './components/missing-next-data'
import { NoAccessToAgreement } from './components/no-access-to-agreement'
import {
  AgreementMissingDataValues,
  validateMissingValues,
} from './components/utils'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ estateId: string }>
}): Promise<Metadata> {
  const { estateId } = await params

  const estate = await cachedEstate(estateId)
  return {
    title: `Oppdragsavtale for ${estate?.address?.streetAddress || estateId}`,
  }
}

export default async function Layout({
  params,
  children,
}: {
  params: Promise<{ estateId: string }>
  children: React.ReactNode
}) {
  const [{ estateId }, user, cookieStore] = await Promise.all([
    params,
    getCurrentUser().catch(() => undefined),
    cookies(),
  ])

  const token = cookieStore.get('viewToken')?.value
  const ecidCookie = cookieStore.get('ecid')?.value

  try {
    var [estate, sellerAccessToken] = await Promise.all([
      cachedEstate(estateId),
      token &&
        prisma.offer_access_tokens.findUnique({
          where: {
            estate_id: estateId,
            token,
            deleted_at: null,
            valid: true,
          },
        }),
    ])
    if (!estate) throw new NoEstateError()
  } catch (error) {
    if (error instanceof NoEstateError) {
      notFound()
    }
    throw error
  }

  if (!user && !sellerAccessToken) {
    if (!token) {
      notFound()
    }

    return <NoAccessToAgreement estate={estate} />
  }

  if (!user && sellerAccessToken) {
    const inspectionFolder = await prisma.inspection_folders.findFirst({
      where: {
        estate_id: estateId,
      },
    })

    if (
      inspectionFolder?.published_at &&
      !inspectionFolder?.listing_agreement_active
    ) {
      return <NoAccessToAgreement estate={estate} />
    }
  }

  if (!estate) {
    notFound()
  }

  const listingAgreementData = await cachedUpsertListingAgreement(
    estateId,
    false,
    estate?.estatePrice?.priceSuggestion,
  )

  const missingData: AgreementMissingDataValues[] = validateMissingValues(
    listingAgreementData,
    estate.sellers,
    estate,
  )

  const hasMissingData = missingData.length > 0

  return (
    <BudgetProvider
      listingAgreementData={listingAgreementData}
      estate={estate}
      readOnly={Boolean(ecidCookie) && !user}
    >
      {!user && (
        <TrackListingAgreementOnce
          event="listing_agreement_seen_by_seller"
          estate={estate}
        />
      )}
      <main
        className={cn(
          'flex min-h-screen w-full flex-col sm:pb-36 pb-16 [--padding-x:1rem] md:[--padding-x:1.5rem]',
          hasMissingData ? 'bg-root' : 'bg-root-muted',
        )}
      >
        {/* <AssignmentSync estateId={estateId} /> */}
        {user && listingAgreementData.isNewListingAgreement && (
          <TrackListingAgreementOnce
            event="listing_agreement_created"
            estate={estate}
          />
        )}
        <Header estateId={estateId} disabled={hasMissingData} />

        {hasMissingData ? (
          <div className="grow">
            <MissingNextData
              estate={estate}
              missingData={missingData}
              linkToNext={estate?.linkToNext}
            />
          </div>
        ) : (
          children
        )}
        <LastUpdateStatus estateId={estateId} />
      </main>
    </BudgetProvider>
  )
}
