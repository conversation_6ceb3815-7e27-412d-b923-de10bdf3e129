'use client'

import type { ListingAgreement } from '@befaring/lib/budget-helpers'
import type { ListingAgreementBroker } from '@befaring/lib/format-brokers'

import type { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import type { GQLGetBrokerEstateQuery } from '@/api/generated-client'

import { renderValuationContract } from './render-contract'

export interface GenerateValuationContractHtmlProps {
  listingAgreement: ListingAgreement
  budgetSum: number
  estate: GQLGetBrokerEstateQuery['estate']
  sellers: NextPrivateContactWithProxy[]
  brokers: ListingAgreementBroker[]
}

export function generateValuationContractHtml({
  estate,
  listingAgreement,
  sellers,
  brokers,
  budgetSum,
}: GenerateValuationContractHtmlProps) {
  if (!estate || !estate.address) {
    throw new Error('Missing estate data')
  }

  const { waive_withdrawal_right: waiveWithdrawalRight } = listingAgreement

  if (typeof waiveWithdrawalRight !== 'boolean') {
    console.error('missing required fields in listing agreement', {
      waiveWithdrawalRight,
    })
    throw new Error('Missing required fields in listing agreement')
  }

  const { assignmentNumber } = estate

  const department = estate.department

  if (!department) {
    throw new Error('Missing department data')
  }
  const officeExpert = department.employees?.find((employee) =>
    employee?.title?.toLowerCase().includes('fagansvarlig'),
  )

  return renderValuationContract({
    waiveWithdrawalRight,
    assignmentNumber,
    brokers,
    officeExpert,
    estate: {
      suggestedPrice: listingAgreement.suggested_price ?? 0,
      address: estate.address.streetAddress!,
      postalOffice: `${estate.address.zipCode} ${estate.address.city}`,
      ownership: estate.ownership,
      partOwnership: estate.partOwnership ?? {},
      businessContact: estate.businessManagerContact?.companyName ?? '',
      matrikkel: {
        gnr: estate.landIdentificationMatrix?.gnr?.toString() ?? '',
        bnr: estate.landIdentificationMatrix?.bnr?.toString() ?? '',
        snr: estate.landIdentificationMatrix?.snr?.toString() ?? '',
        municipality: estate.address.municipality ?? '',
        ownPart: estate.landIdentificationMatrix?.ownPart?.toString() ?? '',
      },
      estateType: estate.estateType ?? '',
    },
    sellers: sellers.map(
      ({
        firstName,
        lastName,
        socialSecurity,
        mobilePhone,
        postalAddress,
        postalCode,
        city,
        email,
        proxy,
        companyName,
        organisationNumber,
        contactType,
      }) => {
        if (!postalAddress || !postalCode || !city) {
          throw new Error('Missing required fields in seller data')
        }
        if (contactType === 0) {
          if (
            !firstName ||
            !lastName ||
            !socialSecurity ||
            !mobilePhone ||
            !email
          ) {
            throw new Error('Missing required fields in seller data')
          }
        } else {
          if (!companyName || !organisationNumber) {
            throw new Error('Missing required fields in company seller data')
          }
        }

        const proxyInName = proxy
          ? ` v/${proxy.firstName} ${proxy.lastName}`
          : ''

        return {
          name: `${firstName} ${lastName}${proxyInName}`, // Add proxy to seller
          socialSecurity,
          mobilePhone,
          email,
          postalAddress,
          postalCode,
          contactType,
          hasProxy: !!proxy,
          city,
          companyName: `${companyName}${proxyInName}`, // Add proxy to company seller
          orgNumber: organisationNumber,
          idType: '', // TODO
          idNumber: '', // TODO
        }
      },
    ),
    department: {
      ...department,
      employees:
        estate.department?.employees?.map(
          (employee) => `${employee?.name} (${employee?.title})`,
        ) ?? [],
    },
    budget: {
      costs: {
        'Andre utgifter': [],
      },
      sum: budgetSum,
    },
  })
}
