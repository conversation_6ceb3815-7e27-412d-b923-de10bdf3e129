import { ContractData, ValuationContractData } from '.'

/**
 * Groups brokers by role for PDF template
 */
export function groupBrokersByRole(
  brokers: ContractData['brokers'],
): Record<string, ContractData['brokers']> {
  if (!brokers) {
    return {}
  }
  const grouped: Record<string, ContractData['brokers']> = {}
  brokers.forEach((broker) => {
    if (!broker.roleName) {
      return
    }

    if (!grouped[broker.roleName]) {
      grouped[broker.roleName] = []
    }
    grouped[broker.roleName]!.push(broker)
  })
  return grouped
}

export const getMatrikkelDetails = ({
  estate,
}: ContractData | ValuationContractData) => {
  const partOwnershipName = estate.partOwnership.partName

  switch (estate.ownership) {
    case 0:
      return `Matrikkel: gnr. ${estate.matrikkel.gnr}, brn. ${estate.matrikkel.bnr}${
        estate.matrikkel.snr ? `, snr. ${estate.matrikkel.snr}` : ''
      }`
    case 1: {
      const partOwnership = estate.partOwnership
      return `Matrikkel: andelsnr. ${partOwnership.partNumber} i ${partOwnershipName}${
        partOwnership.partOrgNumber
          ? `, org.nr. ${partOwnership.partOrgNumber}`
          : ''
      }`
    }
    case 2:
    case 3: {
      const partOwnership = estate.partOwnership
      return `Matrikkel: aksjenr. ${partOwnership.estateHousingCooperativeStockNumber}, aksjeboenhentsnr. ${partOwnership.estateHousingCooperativeStockHousingUnitNumber} i ${partOwnershipName}${
        partOwnership.partOrgNumber
          ? `, org.nr. ${partOwnership.partOrgNumber}`
          : ''
      }`
    }
    case 4:
      return `Matrikkel: gnr. ${estate.matrikkel.gnr}, brn. ${estate.matrikkel.bnr}, snr. ${estate.matrikkel.snr} (Ideell andel ${estate.matrikkel.ownPart})`
    default:
      return `Matrikkel: gnr. ${estate.matrikkel.gnr}, brn. ${estate.matrikkel.bnr}${
        estate.matrikkel.snr ? `, snr. ${estate.matrikkel.snr}` : ''
      }`
  }
}

// Function to get commune details
export const getCommuneDetails = (
  estate: ContractData['estate'] | ValuationContractData['estate'],
) => {
  return `i ${estate.matrikkel.municipality} kommune.`
}
