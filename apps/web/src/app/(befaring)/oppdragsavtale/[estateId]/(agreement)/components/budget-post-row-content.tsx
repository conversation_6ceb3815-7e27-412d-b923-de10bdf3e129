'use client'

import {
  CollapsibleContent,
  CollapsibleTrigger,
} from '@radix-ui/react-collapsible'
import { motion } from 'framer-motion'
import { useFormStatus } from 'react-dom'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { Textarea } from '@nordvik/ui/textarea'

import type { MergedBudgetPost } from '@/actions/next/types-next'
import { formatCurrency } from '@/lib/formatCurrency'
import { Browser, getBrowser } from '@/lib/get-browser'

import { indentTableRowClassName } from './budget-table'
import CollapseIcon from './collapse-icon'

export function BudgetPostRowContent({
  post,
  editMode,
  open,
  setOpen,
  hideDescription,
}: {
  post: MergedBudgetPost
  editMode?: boolean
  open: boolean
  setOpen: (open: boolean) => void
  hideDescription?: boolean
}) {
  const { pending } = useFormStatus()
  const enabled = Boolean(post.description && !hideDescription) || editMode

  function handleKeySubmit(e: React.KeyboardEvent<HTMLInputElement>) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.currentTarget.form?.dispatchEvent(
        new Event('submit', { bubbles: true }),
      )
    }
  }

  const hasReducedPrice =
    post.initial_price !== null &&
    post.initial_price > post.amountWithTaxIncluded &&
    post.initial_price - post.amountWithTaxIncluded > 1
  const browser = getBrowser()
  return (
    <>
      {editMode && open ? (
        <div className="flex py-4 items-center gap-1.5 sm:gap-3 px-[--padding-x]">
          <CollapsibleTrigger className="self-start mt-2.5" disabled={!enabled}>
            <CollapseIcon open={open} enabled={enabled} />
          </CollapsibleTrigger>
          <div className="grid grid-cols-1 sm:grid-cols-[1fr,auto] gap-1.5 sm:gap-3 w-full">
            <Input
              name="name"
              onKeyDown={handleKeySubmit}
              defaultValue={post.name}
            />

            {!hideDescription && (
              <Textarea
                name="description"
                wrapperClassName={cn('sm:order-2 sm:col-span-2 h-fit')}
                className={cn(
                  browser === Browser.Safari && 'h-[8em] md:h-[4em]',
                )}
                defaultValue={post.description ?? undefined}
              />
            )}
            <Input
              type="number"
              name="amount"
              defaultValue={post.amountWithTaxIncluded}
              min={0}
              addonRight="kr"
              wrapperClassName="sm:w-56 lg:ml-20"
              onKeyDown={handleKeySubmit}
            />
          </div>
        </div>
      ) : (
        <CollapsibleTrigger asChild disabled={!enabled}>
          <button
            className={cn(
              'flex py-4 items-center gap-1.5 sm:gap-3 px-[--padding-x] w-full',
              !hideDescription && 'hover:bg-interactive-muted',
            )}
          >
            {enabled && <CollapseIcon open={open} enabled={enabled} />}

            <span className="typo-body-sm sm:typo-body-md grow text-left text-pretty">
              {post.name.replaceAll('*', '')}
            </span>

            <div className="flex max-sm:flex-col gap-1 sm:gap-2 whitespace-nowrap text-right typo-body-sm sm:typo-body-md">
              {hasReducedPrice && post.initial_price && (
                <span className="ink-muted line-through">
                  {formatCurrency(post.initial_price)}
                </span>
              )}

              <span className={cn(hasReducedPrice && 'ink-success')}>
                {formatCurrency(post.amountWithTaxIncluded)}
              </span>
            </div>
          </button>
        </CollapsibleTrigger>
      )}
      <CollapsibleContent asChild>
        <motion.div
          className={cn('overflow-hidden')}
          initial={{ height: 0 }}
          animate={{ height: 'auto' }}
          exit={{ height: 0 }}
          transition={{ duration: 0.15 }}
        >
          <div className="flex gap-1.5 sm:gap-3 pb-4 px-4 sm:px-6 w-full">
            {editMode ? (
              <div
                className={cn(
                  'flex flex-col gap-4 pb-4 w-full',
                  indentTableRowClassName,
                )}
              >
                <div className="flex gap-2 self-end">
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => setOpen(false)}
                  >
                    Avbryt
                  </Button>
                  <Button size="sm" type="submit">
                    {pending ? 'Lagrer...' : 'Lagre'}
                  </Button>
                </div>
              </div>
            ) : (
              <span
                className={cn(
                  'typo-body-sm sm:typo-body-md sm:mr-20 md:mr-40 whitespace-pre-wrap',
                  indentTableRowClassName,
                )}
              >
                {post.description}
              </span>
            )}
          </div>
        </motion.div>
      </CollapsibleContent>
    </>
  )
}
