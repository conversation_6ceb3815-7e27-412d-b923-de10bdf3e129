'use client'

import { Eye, MousePointerClick, Target } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Checkbox } from '@nordvik/ui/checkbox'

import { Icons } from '@/components/icons'
import { formatCurrency } from '@/lib/formatCurrency'

const CHANNEL_ICON = {
  default: <Target className="size-4" />,
  snapchat: <Icons.snapchat className="size-4" />,
  googleDisplayAds: <Icons.google className="size-4" />,
} as const

export default function MarketingPackageButton({
  name,
  views,
  clicksInterval,
  price,
  checked,
  disabled,
  onClick,
  channels,
}: {
  name?: string
  views?: string
  clicksInterval?: string
  price?: number
  checked: boolean | null
  disabled?: boolean
  onClick: () => void
  channels: { id: string; title: string }[]
}) {
  function handleKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.stopPropagation()
      onClick()
    }
  }

  return (
    <div
      className={cn(
        'flex flex-col rounded-sm outline outline-subtle focus-visible:outline-2 focus-visible:outline-emphasis',
        checked && 'outline-2 outline-active',
        disabled && 'cursor-default',
      )}
      role="button"
      tabIndex={0}
      onKeyDown={!disabled ? handleKeyDown : undefined}
      onClick={!disabled ? onClick : undefined}
    >
      <div className="flex items-center gap-2 px-4 pb-2 pt-4">
        <Checkbox
          checked={checked ?? false}
          tabIndex={-1}
          disabled={disabled}
        />
        <span className="typo-title-sm">{name}</span>
        <span className="typo-body-md ml-auto px-3">
          {formatCurrency(price)}
        </span>
      </div>

      <div className="typo-body-sm flex flex-col p-4 ink-subtle">
        <div className="flex items-center gap-2">
          <Eye size={16} />
          {views} visninger
        </div>
        <div className="flex items-center gap-2">
          <MousePointerClick size={16} />
          {clicksInterval} Klikk
        </div>
        {channels.map((channel) => {
          return (
            <div key={channel.id} className="flex items-center gap-2">
              {CHANNEL_ICON[channel.id] || CHANNEL_ICON.default}
              Inkluderer {channel.title}
            </div>
          )
        })}
      </div>
    </div>
  )
}
