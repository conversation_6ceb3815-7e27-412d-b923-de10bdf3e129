'use client'

import { Section } from '@befaring/components/section'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { pronoun } from '@befaring/lib/text-formatting'
import { startTransition } from 'react'

import { NextPrivateContact } from '@/actions/next/types-next'

import { useBudget } from '../../../../../context/budget-context'

import ChoiceBlock from './choice-block'
import { hasFieldError } from './utils'
import { WaiveWithdrawalRightsReadMore } from './waive-withdrawal-rights-read-more'

export default function WaiveWithdrawalRightsSection({
  sellers,
}: {
  sellers: NextPrivateContact[]
}) {
  const {
    listingAgreement,
    locked,
    errorFields,
    customerInfo,
    setCustomerInfo,
    setIsUpdating,
  } = useBudget()

  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  const you = pronoun(sellers, { perspective: 'second', capitalize: false })
  const we = pronoun(sellers, { perspective: 'first', capitalize: true })

  const choices = [
    {
      value: 'yes',
      label: `${we} ønsker at Oppdragstaker skal sette i gang arbeidet i henhold til oppdragsavtalen, herunder starte levering av tilknyttede tjenester, før angrefristen på 14 dager har utløpt. ${we} erkjenner at angreretten har gått tapt når tjenesten er levert.`,
    },
    {
      value: 'no',
      label: `${we} ønsker IKKE at Oppdragstaker skal sette i gang arbeidet i henhold til oppdragsavtalen, herunder starte levering av tilknyttede tjenester, før angrefristen på 14 dager har utløpt.`,
    },
  ]

  function getDefaultValue() {
    return typeof customerInfo.waive_withdrawal_right === 'boolean'
      ? customerInfo.waive_withdrawal_right
        ? 'yes'
        : 'no'
      : ''
  }

  return (
    <Section>
      <ChoiceBlock
        id={ValidatedFieldsListingAgreement.waive_withdrawal_right}
        choices={choices}
        title="Angrerett"
        hasError={hasFieldError('waive_withdrawal_right', errorFields)}
        description={
          <div className="flex gap-1.5 flex-wrap">
            <p className="ink-subtle">
              <span className="capitalize">{you}</span> har 14 dagers angrerett.
              Hvis {you} vil starte oppdraget med én gang bortfaller den.
            </p>
            <WaiveWithdrawalRightsReadMore />
          </div>
        }
        name="withdrawalRights"
        onValueChange={async (value) => {
          setIsUpdating(true)
          startTransition(() => {
            setCustomerInfo({
              ...customerInfo,
              waive_withdrawal_right: value === 'yes',
            })
          })
          await updateInfo(
            { waiveWithdrawalRight: value === 'yes' },
            'Kunne ikke oppdatere lånetilbud',
          )
        }}
        defaultValue={getDefaultValue()}
        disabled={locked}
      />
    </Section>
  )
}
