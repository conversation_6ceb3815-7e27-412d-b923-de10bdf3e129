import { Section } from '@befaring/components/section'
import { SellersSection } from '@befaring/components/seller-section/sellers-section'

import {
  NextPrivateContact,
  NextPrivateContactWithProxy,
} from '@/actions/next/types-next'

import { MaritalStatusChoices } from './marital-status-choices'
import OwnerIsSeller from './owner-is-seller'
import SellerIsShareHolder from './seller-is-shareholder'

export function SellerSectionParties({
  sellers,
  signers,
  estateId,
  ownership,
  canEdit,
}: {
  sellers: NextPrivateContactWithProxy[]
  estateId: string
  signers: NextPrivateContact[]
  ownership: number
  canEdit: boolean
}) {
  const hasCompanySeller = sellers.some((seller) => seller.contactType === 1)

  return (
    <Section title="Oppdragsgiver" className="flex flex-col gap-4">
      <SellersSection
        sellers={sellers}
        estateId={estateId}
        signers={signers}
        ownership={ownership}
        canEdit={canEdit}
      />

      {!hasCompanySeller && <MaritalStatusChoices sellers={sellers} />}
      {ownership === 2 ? (
        <SellerIsShareHolder sellers={signers} />
      ) : (
        <OwnerIsSeller sellers={signers} />
      )}
    </Section>
  )
}
