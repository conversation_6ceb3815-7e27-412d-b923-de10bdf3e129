'use client'

import { StorebrandAgreementForm } from '@befaring/components/storebrand-agreement-form'
import { SubSection } from '@befaring/components/sub-section'
import {
  EligibleSigners,
  getDefaultRecipient,
  useEligibleSigners,
} from '@befaring/hooks/use-loan-offer-components'
import { createLoanOfferHandlers } from '@befaring/hooks/use-loan-offer-handlers'
import { useUpdateListingAgreementCustomerInfo } from '@befaring/hooks/use-update-listing-agreement-customer-info'
import { ValidatedFieldsListingAgreement } from '@befaring/lib/check-valid-fields'
import { StorebrandRequestSourceEnum } from '@befaring/verdivurdering/types'
import { AnimatePresence, motion } from 'framer-motion'
import debounce from 'lodash/debounce'
import Image from 'next/image'
import { startTransition, useState } from 'react'

import { Checkbox } from '@nordvik/ui/checkbox'
import { Separator } from '@nordvik/ui/separator'

import { useStorebrandDuplicateCheckQuery } from '@/api/generated-client'
import { useFeatureFlags } from '@/lib/analytics/feature-flag'
import BrokerEstateSeller from '@/server/model/BrokerEstateSeller/model'

import { Section } from '../../../../../components/section'
import { useBudget } from '../../../../../context/budget-context'

import { Subheading } from './subheading'
import { hasFieldError } from './utils'

export default function LoanOfferSection({
  mainContact,
}: {
  mainContact?: Partial<BrokerEstateSeller>
}) {
  const {
    listingAgreement,
    locked,
    errorFields,
    customerInfo,
    setCustomerInfo,
  } = useBudget()
  const featureFlags = useFeatureFlags()
  const { data, isLoading: isDuplicateCheckLoading } =
    useStorebrandDuplicateCheckQuery({
      input: { estateId: listingAgreement.estate_id },
    })

  const updateInfo = useUpdateListingAgreementCustomerInfo(listingAgreement.id)

  const { comment, handleUpdateBuyProcess, handleUpdateHasCoBuyer } =
    createLoanOfferHandlers({
      customerInfo,
      setCustomerInfo,
      source: StorebrandRequestSourceEnum.Oppdragsavtale,
      onServerUpdate: (updates) => {
        if (updates.loanOfferComment) {
          updateInfo(
            { loanOfferComment: updates.loanOfferComment },
            'Kunne ikke oppdatere lånetilbud',
          )
        }

        if (updates.recipientLoanOffer !== undefined) {
          updateInfo(
            { recipientLoanOffer: updates.recipientLoanOffer },
            'Kunne ikke oppdatere lånetilbud',
          )
        }

        if (updates.receiveLoanOffer !== undefined) {
          updateInfo(
            { receiveLoanOffer: updates.receiveLoanOffer },
            'Kunne ikke oppdatere lånetilbud',
          )
        }
      },
    })
  const [receiveLoanOffer, setReceiveLoanOffer] = useState(
    Boolean(listingAgreement.receive_loan_offer),
  )

  const { data: signers } = useEligibleSigners(
    listingAgreement.estate_id,
    !mainContact,
  )

  const getRecipient = async () => {
    if (mainContact) return mainContact.contactId
    return getDefaultRecipient(
      signers,
      listingAgreement.recipient_loan_offer ?? undefined,
    )
  }

  const updateLoanOffer = debounce(async (value: boolean) => {
    if (locked) return

    await updateInfo(
      {
        receiveLoanOffer: value,
        recipientLoanOffer: value ? await getRecipient() : undefined,
      },
      'Kunne ikke oppdatere lånetilbud',
    )
  }, 500)

  const handleLoanOfferChange = (value: boolean) => {
    if (locked) return

    setReceiveLoanOffer(value)
    updateLoanOffer(value)
    startTransition(() => {
      setCustomerInfo((prevState) => ({
        ...prevState,
        receive_loan_offer: value,
      }))
    })
  }

  if (isDuplicateCheckLoading) {
    return (
      <Section className="@container/section">
        <SubSection
          title="Få tilbud på finansieringsbevis eller boliglån"
          loading
        >
          <p className="text-pretty max-w-[100ch] masked-placeholder-text">
            Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
            betingelser og fast kontaktperson. Bli kontaktet for et
            uforpliktende tilbud.
          </p>
          <div className="mt-6 w-1/2 h-4 masked-placeholder-text" />
        </SubSection>
      </Section>
    )
  }

  if (data?.storebrandDuplicateCheck?.hasDuplicates) {
    return null
  }

  if (!featureFlags.loan_offer) return null

  return (
    <Section className="@container/section">
      <div className="flex flex-col gap-4">
        <div
          className="flex flex-col gap-1"
          data-fields={ValidatedFieldsListingAgreement.recipient_loan_offer}
        >
          <Subheading
            hasError={hasFieldError('recipient_loan_offer', errorFields)}
          >
            Få tilbud på finansieringsbevis eller boliglån
          </Subheading>
          <p className="typo-body-md ink-subtle">
            Hos vår samarbeidspartner Storebrand Bank får du konkurransedyktige
            betingelser og fast kontaktperson. Bli kontaktet for et
            uforpliktende tilbud.
          </p>
        </div>
        <div className="flex sm:justify-between sm:items-center flex-col sm:flex-row gap-4">
          <div className="flex gap-2">
            <Checkbox
              className="mt-0.5"
              id="bankOffer"
              onCheckedChange={handleLoanOfferChange}
              defaultChecked={receiveLoanOffer}
              disabled={locked}
            />
            <label className="typo-body-md" htmlFor="bankOffer">
              Ja, jeg vil bli kontaktet av Storebrand
            </label>
          </div>
          <div className="ml-auto self-end hidden @lg/section:block">
            <Image
              src={'/storebrand-logo-text-red.svg'}
              alt="Storebrand"
              width={216}
              height={16}
            />
          </div>
        </div>
      </div>

      <AnimatePresence>
        {receiveLoanOffer && (
          <motion.div
            className="flex flex-col overflow-hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Separator className="my-4" />
            {mainContact ? null : ( // <SingleContact {...mainContact} />
              <EligibleSigners
                onChange={(value) => {
                  startTransition(() => {
                    setCustomerInfo((prevState) => ({
                      ...prevState,
                      recipient_loan_offer: value,
                    }))
                  })
                  updateInfo(
                    { recipientLoanOffer: value },
                    'Kunne ikke oppdatere lånetilbud',
                  )
                }}
                isDisabled={locked}
                estateId={listingAgreement.estate_id}
                currentRecipient={
                  listingAgreement.recipient_loan_offer ?? undefined
                }
              />
            )}
            <StorebrandAgreementForm
              buyProcessStatus={comment.buy_process_status}
              hasCoBuyer={comment.has_co_buyer}
              isDisabled={locked}
              handleUpdateBuyProcess={handleUpdateBuyProcess}
              handleUpdateHasCoBuyer={handleUpdateHasCoBuyer}
              isValuation={false}
            />
          </motion.div>
        )}
      </AnimatePresence>
      <div className="w-full flex justify-end @lg/section:hidden mt-6">
        <Image
          src="/storebrand-logo-text-red.svg"
          alt="Storebrand"
          width={216}
          height={16}
        />
      </div>
    </Section>
  )
}
