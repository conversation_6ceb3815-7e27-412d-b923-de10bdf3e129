import type { SellerInsurance } from '@befaring/lib/prices'

import { GQLDepartment, GQLMarketingPackage } from '@/api/generated-client'

import ConditionalRender from './conditional-render'
import FrontPage from './front-page'
import { Page1 } from './pages/page-1'
import { Page2 } from './pages/page-2'
import { Page3 } from './pages/page-3'
import { Page4 } from './pages/page-4'
import { Page5 } from './pages/page-5'
import { Page6 } from './pages/page-6'
import { Page7 } from './pages/page-7'
import { Page8 } from './pages/page-8'
import { Page9 } from './pages/page-9'
import { Page10 } from './pages/page-10'
import { Page11 } from './pages/page-11'
import { Page12 } from './pages/page-12'
import { ValuationPage1 } from './pages/valuation/valuation-page-1'
import { ValuationPage2 } from './pages/valuation/valuation-page-2'
import { ValuationPage3 } from './pages/valuation/valuation-page-3'
import { ValuationPage4 } from './pages/valuation/valuation-page-4'

export interface PdfBudgetPostData {
  description: string
  cost: number
  bold?: boolean
}

type PdfDepartment = Partial<GQLDepartment> & {
  employees?: string[]
}

export interface ContractData {
  waiveWithdrawalRight: boolean
  sellerIsCompany: boolean
  noPreviousBrokers: boolean
  previousBrokers: string | null
  assignmentNumber?: string | null
  marriedOrInPartnership: boolean
  isCommonEstate: boolean | null
  ownerIsSeller: boolean | null
  sellerIsShareholder: boolean | null
  ownershipType?: string | null
  separateProvision?: string | null
  sellers: {
    name: string
    socialSecurity?: string | null
    companyName?: string | null
    orgNumber?: string | null
    postalAddress: string
    postalCode: string
    city: string
    mobilePhone?: string | null
    email?: string | null
    idType: string
    idNumber: string
    contactType: number
    hasProxy: boolean
  }[]
  brokers?:
    | {
        name: string
        mobilePhone: string
        email: string
        role: number
        roleName: string
        title: string
      }[]
    | null
  officeExpert?: {
    name?: string | null
    mobilePhone?: string | null
    email?: string | null
  } | null
  department: PdfDepartment
  estate: {
    ownership?: number | null
    partOwnership: {
      partName?: string | null
      partNumber?: number | null
      partOrgNumber?: string | null
      estateHousingCooperativeStockNumber?: string
      estateHousingCooperativeStockHousingUnitNumber?: string
    }
    businessContact?: string | null
    address: string
    postalOffice: string
    matrikkel: {
      gnr: string
      bnr: string
      municipality: string
      snr: string
      ownPart?: string
    }
    estateType: string
    suggestedPrice: number
  }
  commission: {
    type: 'hourly' | 'fixed' | 'percentage'
    feePercentage: number
    fixedCommission: number
  }
  budget: {
    costs: Record<string, PdfBudgetPostData[]>
    sum: number
    sellerInsurance: SellerInsurance | null
    marketingPackage: GQLMarketingPackage | undefined
  }
}

interface ContractDocumentProps {
  data: ContractData
}

export function Contract({ data }: ContractDocumentProps) {
  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname -- Custom className for styling
    <div id="contract-document" className="contract-document">
      <FrontPage contractData={data} />
      <Page1 contractData={data} />
      <Page2 contractData={data} />
      <Page3 contractData={data} />
      <Page4 contractData={data} />
      <Page5 contractData={data} />
      <Page6 contractData={data} />
      <Page7 contractData={data} />
      <Page8 />
      <Page9 contractData={data} />
      <Page10 contractData={data} />
      <Page11 contractData={data} />
      <ConditionalRender condition={!data.sellerIsCompany}>
        <Page12 contractData={data} />
      </ConditionalRender>
    </div>
  )
}

export default Contract

export interface ValuationContractData {
  waiveWithdrawalRight: boolean
  assignmentNumber?: string | null
  officeExpert?: {
    name?: string | null
    mobilePhone?: string | null
    email?: string | null
  } | null
  budget: {
    costs: Record<string, PdfBudgetPostData[]>
    sum: number
  }
  department: PdfDepartment
  estate: {
    ownership?: number | null
    partOwnership: {
      partName?: string | null
      partNumber?: number | null
      partOrgNumber?: string | null
      estateHousingCooperativeStockNumber?: string
      estateHousingCooperativeStockHousingUnitNumber?: string
    }
    businessContact?: string | null
    address: string
    postalOffice: string
    matrikkel: {
      gnr: string
      bnr: string
      municipality: string
      snr: string
      ownPart?: string
    }
    estateType: string
    suggestedPrice: number
  }
  sellers: {
    name: string
    socialSecurity?: string | null
    companyName?: string | null
    orgNumber?: string | null
    postalAddress: string
    postalCode: string
    city: string
    mobilePhone?: string | null
    email?: string | null
    idType: string
    idNumber: string
    contactType: number
    hasProxy: boolean
  }[]
  brokers?:
    | {
        name: string
        mobilePhone: string
        email: string
        role: number
        roleName: string
        title: string
      }[]
    | null
}

interface ValuationContractDocumentProps {
  data: ValuationContractData
}

export function ValuationContract({ data }: ValuationContractDocumentProps) {
  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname -- Custom className for styling
    <div id="contract-document" className="contract-document">
      <FrontPage contractData={data} />
      <ValuationPage1 contractData={data} />
      <ValuationPage2 contractData={data} />
      <ValuationPage3 contractData={data} />
      <ValuationPage4 contractData={data} />
    </div>
  )
}
