import { StartMainContent } from '@befaring/components/start/start-main-content'
import { preloadEstate } from '@befaring/lib/cached-estate'
import { Suspense } from 'react'

import { BrandLoopVideoPlayer } from '@/components/looping-brand-videos/video-player'
import { TrackPageVisit } from '@/components/track-page-visit'

export default async function StartPage(props: {
  params: Promise<{ estateId: string }>
}) {
  const params = await props.params

  const { estateId } = params

  if (!estateId) {
    throw new Error('No estate id provided')
  }

  preloadEstate(estateId)

  return (
    <div
      data-theme="dark"
      className="bg-root grow relative isolate flex items-center justify-center"
    >
      <TrackPageVisit estateId={estateId} pageId="befaring-start" />
      <BrandLoopVideoPlayer startIndex={0} className="bg-root" />
      <div className="absolute inset-0 md:hidden bg-[black] opacity-80" />
      <Suspense fallback={null}>
        <StartMainContent estateId={estateId} />
      </Suspense>
    </div>
  )
}
