import { isSellerAndHasPublishedInspection } from '@befaring/authorize'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '<PERSON><PERSON> og team',
}

export default async function Layout(props: {
  children: React.ReactNode
  params: Promise<{ estateId: string }>
}) {
  const params = await props.params

  const { children } = props

  await isSellerAndHasPublishedInspection(params.estateId)

  return <>{children}</>
}
