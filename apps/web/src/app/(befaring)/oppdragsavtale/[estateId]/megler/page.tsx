'use client'

import { use } from 'react'

import { useListingAgreementTeamQuery } from '@/api/generated-client'
import { BrokerPresentation } from '@/components/broker-profile/presentation/broker-presentation'
import { BrokerForPresentation } from '@/components/broker-profile/presentation/type'
import { assistantBrokerAsPartner } from '@/components/edit-inspection/assisant-broker-as-partner'
import { NordvikLoader } from '@/components/nordvik-loader'
import { TrackPageVisit } from '@/components/track-page-visit'

export default function Page(props: { params: Promise<{ estateId: string }> }) {
  const params = use(props.params)
  const { data } = useListingAgreementTeamQuery(
    { estateId: params.estateId },
    {
      select: (data) => {
        const mainBroker = data.estate?.mainBroker as BrokerForPresentation
        const assisantPartner = assistantBrokerAsPartner(
          data.estate?.assistantBroker,
        )
        const modifiedTeam = assisantPartner
          ? [assisantPartner, ...(mainBroker?.team ?? [])]
          : mainBroker?.team

        const filteredTeam = modifiedTeam?.filter((partner) => {
          const excludedParter =
            data.estate?.inspectionFolder?.excludedPartners?.some(
              (excludedPartner) => excludedPartner.id === partner.id,
            )

          if (excludedParter) {
            return false
          }

          const excludedEmployee =
            data.estate?.inspectionFolder?.excludedEmployees?.some(
              (excludedEmployee) => excludedEmployee === partner.id,
            )

          if (excludedEmployee) {
            return false
          }

          return true
        })

        return {
          broker: {
            ...mainBroker,
            team: filteredTeam,
          },
        }
      },
    },
  )

  return (
    <div data-theme="dark" className="bg-root">
      <TrackPageVisit estateId={params.estateId} pageId="befaring-broker" />
      {data?.broker ? (
        <BrokerPresentation broker={data.broker} />
      ) : (
        <NordvikLoader />
      )}
    </div>
  )
}
