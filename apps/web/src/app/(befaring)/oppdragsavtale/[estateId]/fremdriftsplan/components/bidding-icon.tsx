export const BiddingIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.567 21.4466L11.5582 12.2731L13.3341 11.3742L17.3428 20.5477C17.5783 21.0867 18.2079 21.2854 18.7061 20.9781L19.7665 20.3238C20.2647 20.0165 20.4184 19.3345 20.0994 18.8471L14.669 10.5506L16.2854 9.35669L21.7159 17.6532C22.673 19.1154 22.2118 21.1614 20.7173 22.0834L19.6568 22.7376C18.1623 23.6597 16.2735 23.0635 15.567 21.4466Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 19C3 18.4477 3.44772 18 4 18H10C10.5523 18 11 18.4477 11 19C11 19.5523 10.5523 20 10 20H4C3.44772 20 3 19.5523 3 19Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1 22C1 21.4477 1.44772 21 2 21H12C12.5523 21 13 21.4477 13 22C13 22.5523 12.5523 23 12 23H2C1.44772 23 1 22.5523 1 22Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.6409 5.41254L9.25091 8.12085L10.8495 11.0795L15.2394 8.37117L13.6409 5.41254ZM6.59805 7.41137L10.0981 13.8892L17.8923 9.08065L14.3923 2.60285L6.59805 7.41137Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.78874 10.7234C3.15135 9.54373 3.79538 8.01904 5.07196 7.68551L7.83798 6.96282L8.25689 8.95062L5.49088 9.67331L7.71126 13.7828L9.67998 11.5845L11.0816 12.966L9.11284 15.1644C8.20424 16.1789 6.64651 16.0126 6.00912 14.8329L3.78874 10.7234Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.4812 1.65911C17.8438 0.479427 16.2861 0.31307 15.3775 1.32765L13.4088 3.52598L14.8104 4.90754L16.7791 2.70922L18.9995 6.81871L16.2335 7.54139L16.6524 9.52919L19.4184 8.80651C20.695 8.47297 21.339 6.94828 20.7016 5.7686L18.4812 1.65911Z"
        fill="currentColor"
      />
    </svg>
  )
}
