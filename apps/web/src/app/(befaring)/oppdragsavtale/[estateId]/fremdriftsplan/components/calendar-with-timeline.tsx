'use client'

import { useQueryClient } from '@tanstack/react-query'
import { isSameDay, isTuesday, previousFriday, subDays } from 'date-fns'
import React from 'react'

import {
  GQLInspectionEvent,
  GQLInspectionEventInput,
  GQLInspectionEventsQuery,
  useEditInspectionFolderQuery,
  useUpdateInspectionEventsMutation,
} from '@/api/generated-client'
import { useThrottle } from '@/hooks/use-throttle'
import { useTrackEvent } from '@/lib/analytics/track-event'

import { HighlightState, HighlightStateContext } from '../highlight-state'
import {
  EventTypeName,
  TimelineEvent,
  createTimelineEvent,
  eventTypes,
} from '../timeline-event-types'

import { CalendarList } from './calendar-list'
import { TimelineList } from './timeline-list'

export const EventActionsContext = React.createContext<{
  onAdd: (event: TimelineEvent) => void
  onUpdate: (event: TimelineEvent) => void
  onRemove: (event: TimelineEvent) => void
  onClear: () => void
  canEdit: boolean
}>({
  onAdd: () => void 0,
  onUpdate: () => void 0,
  onRemove: () => void 0,
  onClear: () => void 0,
  canEdit: false,
})

function cleanEventDates(event: TimelineEvent) {
  if (event.start && event.end) {
    if (isSameDay(event.start, event.end)) {
      return { ...event, end: undefined }
    }
    if (event.start > event.end) {
      return { ...event, start: event.end, end: event.start }
    }
  }
  return event
}

function autofill(events: TimelineEvent[], previousEvents: TimelineEvent[]) {
  const biddingEvent = events.find((e) => e.meta?.type === 'bidding')
  const hasOnlyBidding = events.length === 1 && biddingEvent
  if (!hasOnlyBidding) return events
  const hadOnlyBidding =
    previousEvents.length === 1 && previousEvents[0].meta?.type === 'bidding'
  if (hadOnlyBidding) return events
  const biddingDate = biddingEvent?.start
  const newEvents = [...events]
  newEvents.unshift(createTimelineEvent('viewing', subDays(biddingDate, 1)))
  if (isTuesday(biddingDate))
    newEvents.unshift(createTimelineEvent('viewing', subDays(biddingDate, 2)))
  newEvents.unshift(
    createTimelineEvent(
      'marketing',
      previousFriday(subDays(biddingDate, 7)),
      biddingDate,
    ),
  )
  return newEvents
}

export type InspectionEvent =
  GQLInspectionEventsQuery['inspectionEvents'][number]

export function CalendarWithTimeline({
  initialEvents,
  estateId,
  canEdit,
  sellers,
}: {
  initialEvents: InspectionEvent[]
  estateId: string
  canEdit: boolean
  sellers: NonNullable<GQLInspectionEventsQuery['estate']>['sellers']
}) {
  const [highlightState, setHighlightState] =
    React.useState<HighlightState | null>(null)
  const memoizedHighlightState = React.useMemo(
    () => [highlightState, setHighlightState] as const,
    [highlightState, setHighlightState],
  )
  const queryClient = useQueryClient()
  const { mutateAsync } = useUpdateInspectionEventsMutation()
  const [events, setEvents] = React.useState(
    initialEvents.map(convertToTimelineEvent),
  )
  const trackEventChanges = useTrackEventChanges(events)
  const saveEvents = useThrottle(
    () => {
      setEvents((events) => {
        mutateAsync(
          {
            estateId,
            events: events.map(convertToInspectionEvent),
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: useEditInspectionFolderQuery.getKey({ estateId }),
              })
            },
          },
        )
        trackEventChanges(events)
        return events
      })
    },
    1000,
    { leading: true, trailing: true },
  )

  const eventActions = React.useMemo(
    () => ({
      canEdit,
      onAdd: (event: TimelineEvent) => {
        if (!canEdit) return
        setEvents((events) => autofill([...events, event], events))
        saveEvents()
      },
      onUpdate: (event: TimelineEvent) => {
        if (!canEdit) return
        setEvents((events) =>
          autofill(
            events.map((e) => (e.id === event.id ? cleanEventDates(event) : e)),
            events,
          ),
        )
        saveEvents()
      },
      onRemove: (event: TimelineEvent) => {
        if (!canEdit) return
        setEvents((events) => events.filter((e) => e.id !== event.id))
        saveEvents()
      },
      onClear: () => {
        if (!canEdit) return
        setEvents([])
        saveEvents()
      },
    }),
    [canEdit, saveEvents],
  )

  return (
    <EventActionsContext.Provider value={eventActions}>
      <HighlightStateContext.Provider value={memoizedHighlightState}>
        <div className="flex mt-[2.5rem] lg:[--top-padding:2.5rem] grow gap-8 xl:gap-12 max-lg:flex-col-reverse px-[--container-padding] max-w-[100rem] mx-auto">
          <CalendarList events={events} />
          <TimelineList events={events} sellers={sellers} />
        </div>
      </HighlightStateContext.Provider>
    </EventActionsContext.Provider>
  )
}

const convertToInspectionEvent = (
  event: TimelineEvent,
): GQLInspectionEventInput => ({
  id: event.id?.toString(),
  type: event.meta?.type || 'custom',
  title: event.meta?.customTitle,
  start: event.start?.toISOString(),
  end: event.end?.toISOString(),
})

export const convertToTimelineEvent = (
  event: GQLInspectionEvent,
): TimelineEvent => ({
  id: event.id,
  title: eventTypes[event.type].title,
  start: new Date(event.start),
  end: event.end ? new Date(event.end) : undefined,
  meta: {
    type: event.type as EventTypeName,
    customTitle: event.title,
  },
})

function useTrackEventChanges(events: TimelineEvent[]) {
  const trackEvent = useTrackEvent()
  const [previousEvents, setPreviousEvents] = React.useState(events)
  React.useEffect(() => {
    setPreviousEvents(events)
  }, [events])
  return React.useCallback(
    (newEvents: TimelineEvent[]) => {
      newEvents.forEach((event) => {
        if (event.meta?.type === 'empty') return
        const prevFiltered = previousEvents.filter(
          (e) => e.meta?.type !== 'empty',
        )
        const prev = prevFiltered.find((e) => e.id === event.id)
        if (event === prev) return
        const action = prev ? 'updated' : 'added'
        trackEvent(`inspection_event_${action}`, {
          event_type: event.meta?.type,
          start: event.start?.toISOString(),
          end: event.end?.toISOString(),
          customTitle: event.meta?.customTitle,
          events_before: prevFiltered.length,
        })
      })
      previousEvents
        .filter((e) => !newEvents.some((ne) => ne.id === e.id))
        .filter((e) => e.meta?.type !== 'empty')
        .forEach((e) => {
          trackEvent('inspection_event_removed', {
            event_type: e.meta?.type,
            start: e.start?.toISOString(),
            end: e.end?.toISOString(),
            customTitle: e.meta?.customTitle,
          })
        })

      setPreviousEvents(newEvents)
    },
    [previousEvents, trackEvent],
  )
}

export function filterCalendarEvents(
  events: GQLInspectionEventsQuery['inspectionEvents'],
) {
  const exludedEventTypes: EventTypeName[] = ['empty']
  return events.filter(
    (event) =>
      !exludedEventTypes.includes(event.type as EventTypeName) &&
      (event.type !== 'custom' || event.title?.trim()),
  )
}
