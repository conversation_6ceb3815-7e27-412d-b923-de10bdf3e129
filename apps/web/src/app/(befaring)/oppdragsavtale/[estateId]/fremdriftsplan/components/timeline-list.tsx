import * as Popover from '@radix-ui/react-popover'
import { differenceInDays, isSameYear } from 'date-fns'
import { AnimatePresence, motion } from 'framer-motion'
import { PlusIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'

import { GQLInspectionEventsQuery } from '@/api/generated-client'

import { useHighlightState } from '../highlight-state'
import {
  EventType,
  TimelineEvent,
  createTimelineEvent,
  eventTypes,
} from '../timeline-event-types'

import { EventActionsContext } from './calendar-with-timeline'
import { EventPopoverContent } from './event-popover-content'

export function TimelineList({
  events,
  sellers,
}: {
  events: TimelineEvent[]
  sellers: NonNullable<GQLInspectionEventsQuery['estate']>['sellers']
}) {
  const eventActions = React.useContext(EventActionsContext)
  const hasEvents = events && events.some((e) => e.meta?.type !== 'empty')
  const now = new Date()

  const eventsGroupedByMonth = Object.fromEntries(
    Object.entries(
      events.reduce(
        (acc, event) => {
          if (event.meta?.type === 'empty') return acc
          const month = `${event.start.getFullYear()}-${String(event.start.getMonth()).padStart(2, '0')}`
          acc[month] = acc[month] || []
          acc[month].push(event)
          return acc
        },
        {} as Record<string, TimelineEvent[]>,
      ),
    ).sort(([a], [b]) => a.localeCompare(b)),
  )

  const isPlural = sellers.length > 1

  return (
    <div className="lg:basis-[260px] pt-[--top-padding] min-w-[260px] h-auto lg:self-start grow lg:sticky top-0 lg:max-h-screen overflow-y-auto lg:pb-[--footer-height]">
      <h2 className="typo-title-sm">Forslag til fremdriftsplan</h2>
      <p className="typo-body-md ink-subtle mt-1.5 text-pretty">
        I møtet legger vi en fremdriftsplan sammen som passer med{' '}
        {isPlural ? 'deres' : 'din'} hverdag og når {isPlural ? 'dere' : 'du'}{' '}
        vil selge.
      </p>
      <AnimatePresence>
        {hasEvents &&
          Object.entries(eventsGroupedByMonth).map(([key, events]) => (
            <motion.div
              key={key}
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ type: 'spring', stiffness: 200, damping: 25 }}
            >
              <h3 className="typo-body-lg capitalize pt-8 font-medium">
                {events[0].start.toLocaleDateString('no-NO', {
                  month: 'long',
                  year: isSameYear(events[0].start, now)
                    ? undefined
                    : 'numeric',
                })}
              </h3>
              <ul>
                <AnimatePresence>
                  {events
                    .sort((a, b) => a.start.getTime() - b.start.getTime())
                    .map((event) => {
                      const eventType = event.meta?.type
                        ? eventTypes[event.meta?.type]
                        : undefined
                      if (!eventType || eventType.system) return null

                      return (
                        <motion.li
                          key={event.id}
                          className="flex flex-col overflow-hidden"
                          initial={{ height: 0 }}
                          animate={{ height: 'auto' }}
                          exit={{ height: 0 }}
                          transition={{
                            type: 'spring',
                            stiffness: 200,
                            damping: 25,
                          }}
                        >
                          <Popover.Root>
                            <EventItem
                              event={event}
                              interactive={eventActions.canEdit}
                            />
                            {eventActions.canEdit && (
                              <Popover.Portal>
                                <EventPopoverContent
                                  event={event}
                                  sideOffset={4}
                                  align="start"
                                  side={
                                    typeof window !== 'undefined' &&
                                    window.innerWidth > 1024
                                      ? 'left'
                                      : 'bottom'
                                  }
                                  onUpdate={(update) =>
                                    eventActions.onUpdate({
                                      ...event,
                                      ...update,
                                    })
                                  }
                                  onRemove={() => eventActions.onRemove(event)}
                                />
                              </Popover.Portal>
                            )}
                          </Popover.Root>
                        </motion.li>
                      )
                    })}
                </AnimatePresence>
              </ul>
            </motion.div>
          ))}
      </AnimatePresence>
      {eventActions.canEdit && (
        <div className="flex gap-2 mt-8">
          <CreateNewButton key={events.length} />
          <AnimatePresence>
            {hasEvents && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9, x: -10 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.9, x: -10 }}
                transition={{
                  type: 'spring',
                  stiffness: 300,
                  damping: 25,
                  bounce: false,
                }}
              >
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => eventActions.onClear()}
                >
                  Fjern alle
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
      <AnimatePresence>
        {!hasEvents && (
          <motion.ul
            aria-hidden
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 200, damping: 25 }}
            className="mt-8"
            style={{
              maskImage:
                'linear-gradient(to bottom, transparent, white 0%, transparent 85%)',
            }}
          >
            {[
              eventTypes.appraisal,
              eventTypes.photography,
              eventTypes.marketing,
              eventTypes.viewing,
              eventTypes.bidding,
            ].map((eventType) => (
              <EmptyEvent key={eventType.title} eventType={eventType} />
            ))}
          </motion.ul>
        )}
      </AnimatePresence>
    </div>
  )
}

function EventItem({
  event,
  interactive,
}: {
  event: TimelineEvent
  interactive: boolean
}) {
  const ref = React.useRef<HTMLButtonElement>(null)
  const [highlightState, setHighlightState] = useHighlightState()
  const eventType = event.meta?.type ? eventTypes[event.meta?.type] : undefined
  const days = event.end ? differenceInDays(event.end, event.start) : 0

  React.useEffect(() => {
    const node = ref.current
    if (!node) return
    const enterListener = () => {
      setHighlightState(typeof event.id === 'string' ? event.id : null)
    }
    const leaveListener = () => setHighlightState(null)
    node.addEventListener('pointerenter', enterListener)
    node.addEventListener('pointerleave', leaveListener)
    return () => {
      node.removeEventListener('pointerenter', enterListener)
      node.removeEventListener('pointerleave', leaveListener)
    }
  }, [event.id, setHighlightState])

  if (!eventType || eventType.system) return null

  return (
    <Popover.Trigger
      ref={ref}
      data-highlight={highlightState === event.id}
      className={cn(
        'flex gap-1 group/event py-3 border-b border-muted transition-colors duration-100',
        interactive ? '' : 'cursor-default',
      )}
    >
      <div className="w-[6ch] flex flex-col typo-body-sm shrink-0">
        {formatDate(event.start)}
        <AnimatePresence>
          {event.end && (
            <motion.span
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{
                type: 'spring',
                stiffness: 200,
                damping: 20,
              }}
            >
              <div
                className="transition-all duration-500 ease-in-out"
                style={{ height: 6 + days * 2 }}
              />
              {formatDate(event.end)}
            </motion.span>
          )}
        </AnimatePresence>
      </div>
      <div
        className="w-1.5 h-auto shrink-0 rounded-full mr-3 self-stretch relative transition-transform duration-100 group-data-[highlight=true]/event:scale-x-125"
        style={eventType.colors}
      >
        <div className="absolute inset-0 bg-[--highlight-color] rounded-[inherit] group-data-[highlight=false]/event:opacity-0" />
      </div>
      <span className="typo-body-md mt-2 group-data-[highlight=true]/event:underline underline-offset-3 decoration-subtle text-left">
        {event.meta?.customTitle || event.title}
      </span>
    </Popover.Trigger>
  )
}

function EmptyEvent({ eventType }: { eventType: EventType }) {
  return (
    <li className="flex gap-1 py-5 first:pt-0 first:border-t-0 border-t border-muted">
      <div
        className="w-1.5 h-auto rounded-full mr-3"
        style={{
          ...eventType.colors,
          height: eventType.name === 'marketing' ? '5rem' : undefined,
        }}
      />
      <span className="typo-body-md">{eventType.title}</span>
    </li>
  )
}

function CreateNewButton() {
  const eventActions = React.useContext(EventActionsContext)
  const [event, setEvent] = React.useState<TimelineEvent>(
    createTimelineEvent('empty', new Date()),
  )
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <Button size="sm" iconStart={<PlusIcon />}>
          Legg til
        </Button>
      </Popover.Trigger>
      <Popover.Portal>
        <EventPopoverContent
          event={event}
          sideOffset={4}
          completeLabel="Legg til"
          onComplete={() => {
            if (event.meta?.type === 'empty') return
            eventActions.onAdd(event)
          }}
          onUpdate={(patch) => {
            setEvent((event) => ({ ...event, ...patch }))
          }}
        />
      </Popover.Portal>
    </Popover.Root>
  )
}

function formatDate(date: Date) {
  return (
    <div className="flex flex-col items-center">
      <span className="typo-body-xs font-medium">
        {date.toLocaleDateString('no-NO', { weekday: 'short' })}
      </span>
      <span className="typo-body-md font-medium">{date.getDate()}</span>
    </div>
  )
}
