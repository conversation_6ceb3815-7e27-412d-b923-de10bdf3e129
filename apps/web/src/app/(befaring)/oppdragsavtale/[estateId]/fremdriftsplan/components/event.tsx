import * as Popover from '@radix-ui/react-popover'
import { MonthViewDay } from 'calendar-utils'
import { isSameDay } from 'date-fns'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { useDragState } from '../drag-state'
import { useHighlightState } from '../highlight-state'
import { TimelineEvent, eventTypes } from '../timeline-event-types'

import { EventActionsContext } from './calendar-with-timeline'
import { EventPopoverContent } from './event-popover-content'

const WEEK_START = 1

export function ClickableEvent({
  event,
  day,
}: {
  event: TimelineEvent
  day: MonthViewDay
}) {
  const ref = React.useRef<HTMLDivElement>(null)
  const [editOpen, setEditOpen] = React.useState(false)
  const [dragState, setDragState] = useDragState()
  const eventActions = React.useContext(EventActionsContext)
  const [highlightState, setHighlightState] = useHighlightState()

  const isStart = isSameDay(day.date, event.start)
  const isEnd = event.end ? isSameDay(day.date, event.end) : false
  const eventType = event.meta?.type ? eventTypes[event.meta.type] : undefined

  // Only allow opening non-system events (not holidays) and delete if closing empty event
  const onEditOpenChange = (open: boolean) => {
    if (open && !eventType?.system) return setEditOpen(true)
    setEditOpen(false)
    if (event.meta?.type === 'empty') {
      eventActions.onRemove(event)
    }
  }

  React.useEffect(() => {
    const node = ref.current
    if (!node) return
    const enterListener = () => {
      setHighlightState(typeof event.id === 'string' ? event.id : null)
    }
    const leaveListener = () => setHighlightState(null)
    node.addEventListener('pointerenter', enterListener)
    node.addEventListener('pointerleave', leaveListener)
    return () => {
      node.removeEventListener('pointerenter', enterListener)
      node.removeEventListener('pointerleave', leaveListener)
    }
  }, [event.id, setHighlightState])

  // After dragging and creating a new empty event, open the last day of the event
  React.useEffect(() => {
    if (!dragState && event.meta?.type === 'empty') {
      setEditOpen(event.end ? isSameDay(day.date, event.end) : true)
    }
  }, [dragState, event.meta?.type, event.end, day.date])

  // Delete event on Backspace but respect input field context
  React.useEffect(() => {
    if (!editOpen) return
    const handleBackspace = ({ key, target }: KeyboardEvent) => {
      if (key !== 'Backspace') return
      const isTextInput =
        target instanceof HTMLInputElement && target.type === 'text'
      if (!isTextInput || !target.value.length) {
        eventActions.onRemove(event)
      }
    }

    document.addEventListener('keydown', handleBackspace)
    return () => document.removeEventListener('keydown', handleBackspace)
  }, [editOpen, event, eventActions])

  // If event is outside of month or system event, we don't need any interaction logic
  if (!day.inMonth || eventType?.system || !eventActions.canEdit)
    return (
      <EventBadge
        icon={eventType?.icon}
        style={eventType?.colors}
        hideContent={!isStart && day.day !== WEEK_START}
        spanStart={!isStart}
        spanEnd={event.end && !isEnd}
        muted={!day.inMonth}
      >
        {event.meta?.customTitle || event.title}
      </EventBadge>
    )

  return (
    <Popover.Root modal open={editOpen} onOpenChange={onEditOpenChange}>
      <Popover.Trigger asChild>
        <EventBadge
          ref={ref}
          icon={eventType?.icon}
          highlight={
            highlightState === event.id &&
            (!dragState || dragState.event.id === event.id)
          }
          style={eventType?.colors}
          hideContent={!isStart && day.day !== WEEK_START}
          spanStart={!isStart}
          spanEnd={event.end && !isEnd}
          muted={!day.inMonth}
          onPointerDown={(ev) => {
            // We listen to clicks on the day to create new event, but when user clicks an
            // existing event, we want to prevent click event bubbling up to the day.
            ev.stopPropagation()
            if (ev.pointerType === 'mouse') {
              setDragState({ event, action: 'move', dragOrigin: day.date })
            }
          }}
          after={
            <>
              {isStart && (
                <DragHandle
                  action="start"
                  onPointerDown={(ev) => {
                    ev.stopPropagation()
                    if (ev.pointerType === 'mouse') {
                      setDragState({
                        event,
                        action: 'start',
                        dragOrigin: day.date,
                      })
                    }
                  }}
                />
              )}
              {(!event.end || isEnd) && (
                <DragHandle
                  action="end"
                  onPointerDown={(ev) => {
                    ev.stopPropagation()
                    if (ev.pointerType === 'mouse') {
                      setDragState({
                        event,
                        action: 'end',
                        dragOrigin: day.date,
                      })
                    }
                  }}
                />
              )}
            </>
          }
        >
          {event.meta?.customTitle || event.title}
        </EventBadge>
      </Popover.Trigger>
      <Popover.Portal>
        <EventPopoverContent
          sideOffset={4}
          event={event}
          onUpdate={(update) => {
            // If it's a new event and the user changes the type,
            // we automatically close the popover to make it feel snappy.
            // If it's an existing event we keep it open.
            if (
              event.meta?.type === 'empty' &&
              update.meta?.type !== 'empty' &&
              update.meta?.type !== 'custom'
            ) {
              setEditOpen(false)
            }
            eventActions.onUpdate({ ...event, ...update })
          }}
          onRemove={() => eventActions.onRemove(event)}
          onComplete={() => {
            onEditOpenChange(false)
          }}
        />
      </Popover.Portal>
    </Popover.Root>
  )
}

export const EventBadge = function EventBadge({
  ref,
  children,
  spanStart,
  spanEnd,
  hideContent,
  muted,
  icon: Icon,
  after,
  highlight,
  className,
  ...rest
}: {
  ref?: React.Ref<HTMLDivElement>
  highlight?: boolean
  hideContent?: boolean
  muted?: boolean
  icon?: React.ComponentType<{ className?: string }>
  children?: React.ReactNode
  after?: React.ReactNode
  spanStart?: boolean
  spanEnd?: boolean
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      ref={ref}
      data-event
      data-highlight={highlight}
      className={cn(
        `peer group/event p-[3px] pl-[3px] sm:pl-1.5 shrink-0 relative cursor-[--drag-cursor,default]`,
        spanStart
          ? 'group-[:not(:nth-child(7n+1))]/day:-ml-px border-l-0'
          : 'ml-0.5 sm:ml-2 rounded-l-sm',
        spanEnd
          ? 'group-[:nth-child(7n)]/day:-mr-px'
          : 'mr-0.5 sm:mr-2 rounded-r-sm',
        {
          'pl-[3px] sm:pl-2': !hideContent && spanStart,
          '!text-transparent': hideContent,
          '!bg-[#0c3539]': muted,
          '!text-[#5c7b7e]': muted && !hideContent,
        },
        className,
      )}
      {...rest}
    >
      {!muted && (
        <div className="absolute inset-0 transition-opacity duration-100 bg-[--highlight-color] rounded-[inherit] group-data-[highlight=false]/event:opacity-0" />
      )}
      <div className="relative flex gap-1 items-center typo-label-sm md:typo-label-md">
        {Icon && <Icon className="size-3 hidden max-md:hidden shrink-0" />}
        <span
          className={cn(
            'whitespace-nowrap pointer-events-none',
            spanEnd && !hideContent
              ? 'relative z-10'
              : 'overflow-hidden grow [mask-image:linear-gradient(to_left,transparent_0ch,black_2ch,black)]',
          )}
        >
          {children}
        </span>
        {after}
      </div>
    </div>
  )
}

function DragHandle({
  action,
  ...rest
}: { action: 'start' | 'end' } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        'absolute inset-y-0 w-4 cursor-col-resize',
        action === 'start' ? '-left-2' : '-right-2',
      )}
      {...rest}
    />
  )
}
