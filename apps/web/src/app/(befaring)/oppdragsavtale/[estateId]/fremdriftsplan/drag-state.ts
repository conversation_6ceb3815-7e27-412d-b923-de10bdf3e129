import React from 'react'

import { TimelineEvent } from './timeline-event-types'

export type DragState = {
  event: TimelineEvent
  action: 'start' | 'end' | 'move'
  dragOrigin: Date
}

export const DragStateContext = React.createContext<
  readonly [DragState | null, (event: DragState | null) => void]
>([null, () => void 0])

export function useDragState() {
  return React.useContext(DragStateContext)
}
