import { MonthViewDay, getMonthView } from 'calendar-utils'
import { adapterFactory } from 'calendar-utils/date-adapters/date-fns'
import { addDays, differenceInDays, getWeek, isDate, max, min } from 'date-fns'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { useDragState } from '../drag-state'
import { TimelineEvent, createTimelineEvent } from '../timeline-event-types'

import { EventActionsContext } from './calendar-with-timeline'
import { ClickableEvent, EventBadge } from './event'

const adapter = adapterFactory()

const WEEK_START = 1
const WEEKDAYS = [
  'Mandag',
  'Tirsdag',
  'Onsdag',
  'Torsdag',
  'Fredag',
  'Lørdag',
  'Søndag',
]

export const Month = React.memo(function Month({
  date,
  events,
}: {
  date: Date
  events: TimelineEvent[]
}) {
  const monthView = getMonthView(adapter, {
    viewDate: date,
    weekStartsOn: WEEK_START,
    events,
  })

  const packingOrder = React.useMemo(
    () => createPackingOrder(monthView.days),
    [monthView.days],
  )

  return (
    <div className="isolate">
      <div className="typo-display-md capitalize mb-4 sticky top-10 z-10 max-sm:px-2">
        {date.toLocaleString('no-NO', {
          month: 'long',
        })}{' '}
        <span className="ink-muted">
          {date.toLocaleString('no-NO', {
            year: 'numeric',
          })}
        </span>
      </div>
      <div className="grid grid-cols-7 rounded-t-[2px]">
        {WEEKDAYS.map((weekday) => {
          return (
            <span
              key={weekday}
              className="typo-body-xs md:typo-body-sm pb-1 ink-muted"
            >
              {weekday.slice(0, 3)}
            </span>
          )
        })}
      </div>
      <div className="grid grid-cols-7 pl-px pt-px overflow-clip">
        {monthView.days.map((day, index) => (
          <Day key={index} day={day} packingOrder={packingOrder[index]} />
        ))}
      </div>
    </div>
  )
})

function Day({
  day,
  packingOrder,
}: {
  day: MonthViewDay
  packingOrder: string[]
}) {
  const eventActions = React.useContext(EventActionsContext)
  const [dragState, setDragState] = useDragState()
  const weekNumber = getWeek(day.date)
  const isHoliday = day.events.some(
    (event) => event.meta?.type === 'national-holiday',
  )

  return (
    <div
      data-in-month={day.inMonth}
      onPointerEnter={() => {
        if (dragState) {
          const event = dragState.event

          if (dragState.action === 'start') {
            eventActions.onUpdate({
              ...event,
              start: min([event.end || event.start, day.date].filter(isDate)),
              end: max([event.start, event.end, day.date].filter(isDate)),
            })
          }

          if (dragState.action === 'end') {
            eventActions.onUpdate({
              ...event,
              start: min([event.start, day.date]),
              end: max([event.start, day.date]),
            })
          }

          if (dragState.action === 'move') {
            const distance = differenceInDays(day.date, dragState.dragOrigin)
            eventActions.onUpdate({
              ...event,
              start: addDays(event.start, distance),
              end: event.end ? addDays(event.end, distance) : undefined,
            })
          }
        }
      }}
      onPointerUp={(event) => {
        if (event.pointerType !== 'mouse') {
          const sameTarget = event.target === event.currentTarget
          const containsTarget = event.currentTarget.contains(
            event.target as Node,
          )
          const isEvent =
            event.target instanceof HTMLElement &&
            event.target.closest('[data-event]')

          if ((sameTarget || containsTarget) && !isEvent) {
            const newEvent = createTimelineEvent('empty', day.date)
            eventActions.onAdd(newEvent)
          }
        }
      }}
      onPointerDown={(event) => {
        if (event.pointerType !== 'mouse') {
          return null
        }
        const sameTarget = event.target === event.currentTarget
        const containsTarget = event.currentTarget.contains(
          event.target as Node,
        )
        if (
          eventActions.canEdit &&
          day.inMonth &&
          (sameTarget || containsTarget)
        ) {
          const newEvent = createTimelineEvent('empty', day.date)
          eventActions.onAdd(newEvent)

          // Allow dragging when creating new event if not touch
          if (event.pointerType === 'mouse') {
            setDragState({
              event: newEvent,
              action: 'end',
              dragOrigin: day.date,
            })
          }
        }
      }}
      className="
        group/day flex flex-col
        typo-body-xs md:typo-body-sm border border-[#1F464B] py-2 -ml-px -mt-px
        hover-supported:data-[in-month=true]:hover:bg-[#003238]
      "
    >
      <span className="flex select-none typo-detail-sm md:typo-body-sm px-2 group-data-[in-month=false]/day:ink-disabled">
        {day.day === WEEK_START && (
          <span className="ink-muted">{weekNumber}</span>
        )}
        <div className="relative ml-auto">
          {day.isToday && (
            <div
              className={cn(
                'absolute rounded-full left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 size-5 bg-light-green-subtle',
                !day.inMonth && 'bg-root-muted',
              )}
            />
          )}
          <span
            className={cn('relative', {
              'ink-danger': isHoliday && day.inMonth,
              'ink-[black]': day.isToday && day.inMonth,
            })}
          >
            {day.date.getDate()}
          </span>
        </div>
      </span>

      <div className="mt-2 flex flex-col gap-0.5 md:gap-1.5 min-h-[3rem] md:min-h-[6rem] h-auto">
        {packingOrder.map((eventId, index) => {
          const event = day.events.find((event) => event.id === eventId)
          if (event)
            return <ClickableEvent day={day} event={event} key={event.id} />
          return (
            <EventBadge key={index} hideContent>
              Empty
            </EventBadge>
          )
        })}
      </div>
    </div>
  )
}

function createPackingOrder(days: MonthViewDay[]) {
  const packingOrder: string[][] = []
  days.forEach((day, index, list) => {
    const week = getWeek(day.date, { weekStartsOn: WEEK_START })
    const prevDay = list[index - 1]
    const prevDayIsSameWeek =
      prevDay && getWeek(prevDay.date, { weekStartsOn: WEEK_START }) === week
    const prevDayPackingOrder = prevDayIsSameWeek ? packingOrder[index - 1] : []
    const thisDayOrder: string[] = Array(
      Math.max(prevDayPackingOrder.length, day.events.length),
    ).fill(undefined)
    if (day.events.length === 0) {
      packingOrder[index] = thisDayOrder
      return
    }

    const sortedEvents = day.events.toSorted((a, b) => {
      // Make sure events that have previous order, are placed first
      const aOrder = prevDayPackingOrder.indexOf(a.id as string)
      const bOrder = prevDayPackingOrder.indexOf(b.id as string)
      return bOrder - aOrder
    })

    sortedEvents.forEach((event) => {
      // If event has a previous order, set the order as the same as the previous event
      const prevOrder = prevDayPackingOrder.indexOf(event.id as string)

      if (prevOrder !== -1) {
        thisDayOrder[prevOrder] = event.id as string
      } else {
        // Find the first empty slot
        const emptySlot = thisDayOrder.findIndex((value) => value === undefined)
        thisDayOrder[emptySlot] = event.id as string
      }
    })

    packingOrder[index] = thisDayOrder
  })
  return packingOrder
}
