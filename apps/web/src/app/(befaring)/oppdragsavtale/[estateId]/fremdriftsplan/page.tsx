'use client'

import { use } from 'react'

import { useInspectionEventsQuery } from '@/api/generated-client'
import { NordvikLoader } from '@/components/nordvik-loader'
import { TrackPageVisit } from '@/components/track-page-visit'
import { useUserContext } from '@/lib/UserContext'

import { CalendarWithTimeline } from './components/calendar-with-timeline'

export default function PlanningPage(props: {
  params: Promise<{ estateId: string }>
}) {
  const params = use(props.params)
  const { user } = useUserContext()
  const { data, isPending } = useInspectionEventsQuery(
    {
      estateId: params.estateId,
    },
    {
      gcTime: 0,
    },
  )

  return (
    <div data-theme="dark" className="bg-root grow flex">
      <TrackPageVisit estateId={params.estateId} pageId="befaring-planning" />
      {isPending ? (
        <NordvikLoader />
      ) : (
        <CalendarWithTimeline
          canEdit={Bo<PERSON>an(user)}
          initialEvents={data?.inspectionEvents ?? []}
          sellers={data?.estate?.sellers ?? []}
          estateId={params.estateId}
        />
      )}
    </div>
  )
}
