'use client'

import { useQuery } from '@tanstack/react-query'
import { addMonths, differenceInMonths, min, startOfMonth } from 'date-fns'
import { AnimatePresence, motion } from 'framer-motion'
import { ArrowUpToLineIcon } from 'lucide-react'
import React from 'react'
import { Virtuoso, Virtuoso<PERSON><PERSON>le } from 'react-virtuoso'

import { Button } from '@nordvik/ui/button'

import { DragState, DragStateContext } from '../drag-state'
import { TimelineEvent } from '../timeline-event-types'

import { Month } from './month'

const Scroller = function Scroller({
  ref,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { ref?: React.Ref<HTMLDivElement> }) {
  return (
    <div ref={ref} className="lg:basis-[800px] mt-[--top-padding]" {...props} />
  )
}

const setCursor = (action: DragState['action']) => {
  document.body.style.setProperty(
    '--drag-cursor',
    {
      start: 'col-resize',
      end: 'col-resize',
      move: 'grabbing',
    }[action],
  )
  document.body.style.setProperty('cursor', 'var(--drag-cursor)')
  document.body.style.setProperty('user-select', 'none')
  return () => {
    document.body.style.removeProperty('--drag-cursor')
    document.body.style.removeProperty('cursor')
    document.body.style.removeProperty('user-select')
  }
}

// We need to count full months between two dates, so we can set inital index for the calendar
function countFullMonths(startDate: Date, endDate: Date): number {
  const start = startOfMonth(startDate)
  const end = startOfMonth(endDate)
  return Math.max(differenceInMonths(end, start), 0)
}

export function CalendarList({ events }: { events: TimelineEvent[] }) {
  const scrollerRef = React.useRef<VirtuosoHandle>(null)
  const [dragState, setDragState] = React.useState<DragState | null>(null)
  const memoizedDragState = React.useMemo(
    () => [dragState, setDragState] as const,
    [dragState, setDragState],
  )
  const [earliestVisibleDate, firstVisibleIndex] = React.useMemo(() => {
    const firstVisibleEvent = events.sort(
      (a, b) => a.start.getTime() - b.start.getTime(),
    )[0]
    const earliestDate = min([
      firstVisibleEvent?.start ?? new Date(),
      new Date(),
    ])
    const isOneRow = typeof window !== 'undefined' && window.innerWidth < 1024
    const initialIndex = isOneRow
      ? 0
      : countFullMonths(earliestDate, firstVisibleEvent?.start ?? new Date())

    return [earliestDate, initialIndex]
  }, [events])
  const [monthCount, setMonthCount] = React.useState(12)
  const [showScrollToTopButton, setShowScrollToTopButton] =
    React.useState(false)
  const { data: holidays } = useQuery({
    queryKey: ['holidays', earliestVisibleDate.getFullYear()],
    queryFn: () => fetchHolidays({ year: earliestVisibleDate.getFullYear() }),
  })

  const allEvents = React.useMemo(() => {
    return [...(holidays ?? []), ...events]
  }, [events, holidays])

  React.useEffect(() => {
    const handlePointerUp = () => setDragState(null)
    window.document.addEventListener('pointerup', handlePointerUp)
    return () =>
      window.document.removeEventListener('pointerup', handlePointerUp)
  }, [])

  React.useEffect(() => {
    if (dragState?.action) return setCursor(dragState.action)
  }, [dragState?.action])

  return (
    <DragStateContext.Provider value={memoizedDragState}>
      <AnimatePresence>
        {showScrollToTopButton && (
          <motion.div
            className="fixed left-1/2 -translate-x-1/2 top-8 z-10"
            initial={{ opacity: 0, y: -10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.9 }}
            transition={{ type: 'spring', stiffness: 150, damping: 12 }}
          >
            <Button
              onClick={() =>
                scrollerRef.current?.scrollToIndex({
                  index: 0,
                  behavior: 'smooth',
                  offset: -200,
                })
              }
              iconOnly={<ArrowUpToLineIcon />}
              size="lg"
            >
              Til topp
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
      <Virtuoso
        ref={scrollerRef}
        style={{ height: 'auto', flexGrow: 2 }}
        totalCount={monthCount}
        useWindowScroll
        components={{ Scroller }}
        initialTopMostItemIndex={
          firstVisibleIndex
            ? {
                index: firstVisibleIndex,
                offset: -80,
                behavior: 'auto',
              }
            : 0
        }
        rangeChanged={({ startIndex }) => {
          setShowScrollToTopButton(startIndex > 0)
        }}
        endReached={() => setMonthCount(monthCount + 12)}
        itemContent={(index) => {
          return (
            <div className="pb-10 lg:pb-20 select-none max-sm:mx-[calc(var(--container-padding)*-1)]">
              <Month
                date={addMonths(earliestVisibleDate, index)}
                events={allEvents}
              />
            </div>
          )
        }}
      />
    </DragStateContext.Provider>
  )
}

async function fetchHolidays({
  year,
}: {
  year: number
}): Promise<TimelineEvent[]> {
  const responses = await Promise.all(
    Array.from({ length: 2 }, (_, i) =>
      fetch(`https://date.nager.at/api/v3/publicholidays/${year + i}/NO`),
    ),
  )
  const data = (await Promise.all(responses.map((r) => r.json()))).flat()
  return data.map((holiday) => ({
    id: holiday.name,
    title: holiday.localName,
    start: new Date(holiday.date),
    meta: { type: 'national-holiday' },
  }))
}
