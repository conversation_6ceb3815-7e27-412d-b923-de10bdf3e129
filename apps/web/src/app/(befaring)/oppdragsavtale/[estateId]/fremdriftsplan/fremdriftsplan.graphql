query inspectionEvents($estateId: String!) {
  inspectionEvents(estateId: $estateId) {
    id
    title
    start
    end
    type
  }
  estate(id: $estateId) {
    sellers {
      contactId
    }
  }
}

mutation addInspectionEvents(
  $estateId: String!
  $events: [InspectionEventInput!]!
) {
  addInspectionEvents(estateId: $estateId, events: $events)
}

mutation deleteInspectionEvent($eventId: String!) {
  deleteInspectionEvent(eventId: $eventId)
}

mutation updateInspectionEvent(
  $eventId: String!
  $event: InspectionEventInput!
) {
  updateInspectionEvent(eventId: $eventId, event: $event)
}

mutation clearInspectionEvents($estateId: String!) {
  clearInspectionEvents(estateId: $estateId)
}

mutation updateInspectionEvents(
  $estateId: String!
  $events: [InspectionEventInput!]!
) {
  updateInspectionEvents(estateId: $estateId, events: $events)
}
