import type { CalendarEvent } from 'calendar-utils'
import {
  CameraIcon,
  ChartNoAxesCombinedIcon,
  DoorOpenIcon,
  LampIcon,
  PenLineIcon,
  PlusIcon,
  SearchCheckIcon,
} from 'lucide-react'

import { themeCSSVariables } from '@nordvik/theme/variables'

import { BiddingIcon } from './components/bidding-icon'

export type EventTypeName =
  | 'empty'
  | 'styling'
  | 'appraisal'
  | 'photography'
  | 'marketing'
  | 'viewing'
  | 'bidding'
  | 'national-holiday'
  | 'custom'

export type TimelineEvent = CalendarEvent<{
  type: EventTypeName
  customTitle?: string
}>

export type EventType = {
  name: EventTypeName
  title: string
  icon?: React.ComponentType<{ className?: string }>
  colors: {
    backgroundColor: string
    color?: string
    '--highlight-color'?: string
  }
  system?: boolean
}

export const eventTypes: Record<EventTypeName, EventType> = {
  empty: {
    name: 'empty',
    title: 'Legg til',
    icon: PlusIcon,
    colors: {
      color: themeCSSVariables.ink.neutral.subtle,
      backgroundColor: '#013f47',
    },
  },
  styling: {
    name: 'styling',
    title: 'Styling',
    icon: LampIcon,
    colors: {
      backgroundColor: '#336669',
      '--highlight-color': '#3A7275',
    },
  },
  appraisal: {
    name: 'appraisal',
    title: 'Tilstandsrapport',
    icon: SearchCheckIcon,
    colors: {
      backgroundColor: '#336669',
      '--highlight-color': '#3A7275',
    },
  },
  photography: {
    name: 'photography',
    title: 'Fotografering',
    icon: CameraIcon,
    colors: {
      backgroundColor: '#336669',
      '--highlight-color': '#3A7275',
    },
  },
  marketing: {
    name: 'marketing',
    title: 'Markedsføring',
    icon: ChartNoAxesCombinedIcon,
    colors: {
      backgroundColor: '#155356',
      '--highlight-color': '#176063',
    },
  },
  viewing: {
    name: 'viewing',
    title: 'Visning',
    icon: DoorOpenIcon,
    colors: {
      backgroundColor: '#336669',
      '--highlight-color': '#3A7275',
    },
  },
  bidding: {
    name: 'bidding',
    title: 'Budrunde',
    icon: BiddingIcon,
    colors: {
      backgroundColor: '#D3F8E0',
      color: '#141E29',
      '--highlight-color': '#E4F8EB',
    },
  },
  custom: {
    name: 'custom',
    title: 'Egendefinert',
    icon: PenLineIcon,
    colors: {
      backgroundColor: '#336669',
      '--highlight-color': '#3A7275',
    },
  },
  'national-holiday': {
    name: 'national-holiday',
    title: 'Helligdag',
    system: true,
    colors: {
      backgroundColor: '#EC3B4220',
      color: themeCSSVariables.ink.danger.default,
    },
  },
}

function randomID() {
  return Math.random().toString(36).substring(7)
}

export const createTimelineEvent = (
  type: EventTypeName,
  start: Date,
  end?: Date,
): TimelineEvent => ({
  id: randomID(),
  title: eventTypes[type].title,
  start,
  end,
  meta: { type },
})
