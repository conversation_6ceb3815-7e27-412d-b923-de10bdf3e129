import type { PopoverContentProps } from '@radix-ui/react-popover'
import * as Popover from '@radix-ui/react-popover'
import { CalendarIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Calendar } from '@nordvik/ui/calendar'
import { TextButton } from '@nordvik/ui/text-button'

import {
  EventTypeName,
  TimelineEvent,
  eventTypes,
} from '../timeline-event-types'

export const EventPopoverContent = function EventPopoverContent({
  ref,
  event,
  onRemove,
  onComplete,
  onUpdate,
  completeLabel = 'Ferdig',
  ...rest
}: {
  ref?: React.Ref<HTMLDivElement>
  event?: Partial<TimelineEvent>
  completeLabel?: string
  onRemove?: () => void
  onComplete?: () => void
  onUpdate: (
    event: Partial<Pick<TimelineEvent, 'start' | 'end' | 'meta' | 'title'>>,
  ) => void
} & PopoverContentProps) {
  function updateEventType(type: EventTypeName, customTitle?: string) {
    const eventType = eventTypes[type]
    onUpdate({
      title: eventType.title,
      meta: { type: eventType.name, customTitle },
    })
  }

  return (
    <Popover.Content
      data-theme="dark"
      className={cn(
        'bg-interactive-top border border-muted py-2.5 px-4 rounded-md flex flex-col gap-2',
        'animate-in fade-in-40 zoom-in-[0.99] duration-300 ease-out-cubic data-[side=bottom]:origin-[--radix-popover-content-transform-origin] data-[side=top]:origin-[--radix-popover-content-transform-origin]',
      )}
      ref={ref}
      {...rest}
    >
      <div className="flex flex-col gap-px">
        {Object.values(eventTypes)
          .filter(
            (eventType) => !eventType.system && eventType.name !== 'empty',
          )
          .map((eventType) => (
            <EventTypeOption
              checked={event?.meta?.type === eventType.name}
              onChange={(value) => updateEventType(value)}
              key={eventType.name}
              value={eventType.name}
              icon={eventType.icon}
            >
              {eventType.name === 'custom' ? (
                <CustomEventInput
                  selected={event?.meta?.type === eventType.name}
                  value={event?.meta?.customTitle || ''}
                  placeholder={eventType.title}
                  onValue={(value) => updateEventType('custom', value)}
                  onEnter={() => onComplete?.()}
                />
              ) : (
                eventType.title
              )}
            </EventTypeOption>
          ))}
      </div>
      <div className="border-t border-muted -mx-4" />
      <div className="flex gap-2 my-1 -mx-2 items-center">
        <DateSelect
          title="Fra dato"
          date={event?.start}
          onSelect={(date) => onUpdate({ start: date })}
        />
        <DateSelect
          title="Til dato"
          date={event?.end}
          onSelect={(date) => onUpdate({ end: date })}
          fromDate={event?.start}
          clearLabel="Fjern sluttdato"
        />
      </div>
      <div className="border-t border-muted -mx-4" />
      <div className="flex gap-2 my-1">
        {onRemove && (
          <TextButton onClick={onRemove} bold subtle size="lg">
            Fjern
          </TextButton>
        )}
        <Popover.Close asChild>
          <TextButton
            onClick={onComplete}
            className="ml-auto"
            bold
            subtle
            size="lg"
          >
            {completeLabel}
          </TextButton>
        </Popover.Close>
      </div>
    </Popover.Content>
  )
}

function EventTypeOption<T extends string>({
  children,
  value,
  icon: Icon,
  checked,
  onChange,
}: {
  children: React.ReactNode
  value: T
  icon?: React.ElementType
  checked: boolean
  onChange: (value: T) => void
}) {
  return (
    <label
      key={value}
      className="has-[input:checked]:bg-interactive-subtle flex items-center hover:bg-interactive-muted gap-1.5 typo-body-sm -mx-2 pl-2 pr-3 py-1 rounded-sm"
    >
      <input
        type="radio"
        name="event"
        value={value}
        className="sr-only"
        checked={checked}
        onChange={(event) => {
          if (event.currentTarget.checked) {
            onChange(value)
          }
        }}
      />
      {Icon && <Icon className="size-3.5" />}
      {children}
    </label>
  )
}

function CustomEventInput({
  onValue,
  value,
  placeholder,
  selected,
  onEnter,
}: {
  onValue: (value: string) => void
  value: string
  placeholder: string
  onEnter: () => void
  selected: boolean
}) {
  const ref = React.useRef<HTMLInputElement>(null)
  React.useEffect(() => {
    const touchDevice = navigator.maxTouchPoints > 0
    if (selected && !touchDevice) {
      ref.current?.focus()
    }
  }, [selected])
  return (
    <input
      ref={ref}
      type="text"
      value={value}
      placeholder={placeholder}
      onChange={(event) => {
        onValue(event.currentTarget.value)
      }}
      onKeyUp={(event) => {
        if (event.key === 'Enter') {
          onEnter()
        }
      }}
      onFocus={() => onValue(value)}
      className="bg-transparent focus:outline-none placeholder:ink-default focus:placeholder:ink-muted"
    />
  )
}

const currentYear = new Date().getFullYear()

function DateSelect({
  title,
  date,
  fromDate,
  onSelect,
  clearLabel,
}: {
  title: string
  date?: Date
  fromDate?: Date
  onSelect: (date?: Date) => void
  clearLabel?: string
}) {
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <div
          className={cn(
            ' py-1 grow px-2.5 flex items-center gap-1.5 typo-body-sm rounded-sm select-none',
            !date
              ? 'ink-subtle bg-interactive-muted hover:bg-interactive-subtle'
              : 'bg-interactive-subtle hover:bg-interactive-emphasis',
          )}
        >
          <CalendarIcon className="size-3.5" />
          {date
            ? date.toLocaleDateString('no-NO', {
                day: 'numeric',
                month: 'short',
                year:
                  currentYear === date.getFullYear() ? undefined : 'numeric',
              })
            : `${title}`}
        </div>
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          data-theme="dark"
          sideOffset={6}
          className="bg-interactive-top px-4 py-3 rounded-md flex flex-col gap-2 border border-muted"
        >
          {fromDate ? (
            <Calendar
              mode="range"
              selected={{ from: fromDate, to: date }}
              onSelect={(range) => onSelect(range?.to)}
              defaultMonth={date || fromDate}
            />
          ) : (
            <Calendar
              defaultMonth={date || fromDate}
              selected={date}
              onSelect={onSelect}
              mode="single"
            />
          )}
          <>
            <div className="border-t border-muted -mx-4" />
            <div className="flex gap-2 my-1">
              {clearLabel && (
                <Popover.Close asChild>
                  <TextButton
                    onClick={() => onSelect(undefined)}
                    bold
                    subtle
                    size="lg"
                  >
                    {clearLabel}
                  </TextButton>
                </Popover.Close>
              )}
              <Popover.Close asChild>
                <TextButton bold subtle size="lg" className="ml-auto">
                  Ferdig
                </TextButton>
              </Popover.Close>
            </div>
          </>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  )
}
