'use client'

import { Overview } from '@befaring/components/sales-process/overview'
import { getSalesProcessContent } from '@befaring/lib/get-sales-process-content'
import { useQuery } from '@tanstack/react-query'
import { use } from 'react'

import { NordvikLoader } from '@/components/nordvik-loader'
import { TrackPageVisit } from '@/components/track-page-visit'

export default function SalesProcessPage(props: {
  params: Promise<{ estateId: string }>
}) {
  const params = use(props.params)
  const { data: salesProcessCategories } = useQuery({
    queryKey: ['salesProcessData'],
    queryFn: () => getSalesProcessContent(),
  })

  return (
    <div data-theme="dark" className="grow bg-root flex flex-col">
      <TrackPageVisit
        estateId={params.estateId}
        pageId="befaring-salesprocess"
      />
      {salesProcessCategories ? (
        <Overview
          categories={salesProcessCategories}
          pageId="befaring-salesprocess"
        />
      ) : (
        <NordvikLoader />
      )}
    </div>
  )
}
