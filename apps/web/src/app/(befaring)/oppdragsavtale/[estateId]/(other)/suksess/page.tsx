import { cachedEstate } from '@befaring/lib/cached-estate'
import { TrackListingAgreementOnce } from '@befaring/lib/track-listing-agreement'
import isEmpty from 'lodash/isEmpty'
import { ArrowUpRight } from 'lucide-react'
import Image from 'next/image'
import { redirect } from 'next/navigation'

import { DocumentStatus } from '@nordvik/signicat-express-sdk/types'
import { Button } from '@nordvik/ui/button'

import { updateSigners } from '@/actions/signicat/update-signers'
import { Confetti } from '@/components/confetti'
import { EmptyState } from '@/components/empty-state'
import prisma from '@/db/prisma'
import signicat from '@/external-services/signicat/client'
import { getCurrentUser } from '@/lib/session'

import { Header, LowerContent } from './components/header'
import NextStep from './components/next-step'
import nordvikApp from './components/screenshot-nordvik-app.png'
import signatureIllustration from './components/signature-illustration.svg'

interface SearchParams {
  'idfy-jwt'?: string
}

export default async function Page(props: {
  params: Promise<{ estateId: string }>
  searchParams: Promise<SearchParams>
}) {
  const params = await props.params

  const { estateId } = params

  const [searchParams, listingAgreement, estate] = await Promise.all([
    props.searchParams,
    prisma.listing_agreements.findUnique({
      where: { estate_id: estateId },
    }),
    cachedEstate(estateId),
  ])

  const { 'idfy-jwt': jwt } = searchParams

  if (!listingAgreement) {
    const user = await getCurrentUser().catch((error) =>
      console.error(`getCurrentUser error: ${error}`),
    )

    if (user) {
      redirect(`/oppdrag/detail/${estateId}`)
    } else {
      return (
        <Header>
          <EmptyState
            className="my-20"
            title="Ingen oppdragsavtale"
            description="Det kan hende at oppdragsavtalen er ferdig signert og ikke er tilgjengelig her lenger. Hør med megleren din."
            illustration="no-data"
          />
        </Header>
      )
    }
  }

  if (!listingAgreement?.signicat_document_id)
    throw new Error('Missing signicat document id')

  try {
    var documentSummary = await signicat.getDocumentSummary(
      listingAgreement?.signicat_document_id,
    )
  } catch (error) {
    console.error(`Signicat JWT error: ${error}`)
    return (
      <Header>
        <EmptyState
          className="my-20"
          title="Kan ikke signeres"
          description="Det kan hende at oppdragsavtalen er ferdig signert og ikke er tilgjengelig her lenger. Hør med megleren din."
          illustration="no-data"
        />
      </Header>
    )
  }
  const fullySigned =
    documentSummary.status.documentStatus === DocumentStatus.Signed

  void updateSigners({
    estate_id: estateId,
    signicat_document_id: listingAgreement.signicat_document_id,
    id: listingAgreement.id,
  }).catch()

  if (jwt) {
    try {
      const result = await signicat.validateRedirectJwt(jwt)

      if (!result.valid) {
        console.error(`Signicat JWT error: ${result.error}`)
      }

      const { SignSuccess } = result.payload

      if (isEmpty(SignSuccess)) {
        return (
          <Header>
            <EmptyState
              className="my-20"
              title="Noe gikk galt med signeringen"
              description="Hør med megleren din."
              illustration="no-data"
            />
          </Header>
        )
      }
    } catch (error) {
      console.error(`Signicat JWT error: ${error}`)
    }
  }

  return (
    <main className="w-full">
      {jwt &&
        (fullySigned ? (
          <TrackListingAgreementOnce
            event="listing_agreement_signed"
            estate={estate}
          />
        ) : (
          <TrackListingAgreementOnce
            event="listing_agreement_partially_signed"
            estate={estate}
          />
        ))}
      <Confetti delay={1000} />
      <Header>
        <div className="flex flex-col items-center gap-6 px-6 py-12">
          <Image src={signatureIllustration} alt="Signatur" />
          <div className="flex flex-col items-center text-center gap-1">
            <h1 className="typo-display-sm">Du har signert oppdragsavtalen!</h1>
            <div className="typo-body-md ink-muted">
              {fullySigned
                ? 'Snart vil du motta signert kontrakt på e-post.'
                : 'Når alle har signert vil du motta signert kontrakt på e-post.'}
            </div>
          </div>
          <Button
            variant="outline"
            href={`/oppdragsavtale/${estateId}`}
            size="lg"
          >
            Tilbake til avtalen
          </Button>
        </div>
      </Header>
      <LowerContent>
        <div className="flex flex-col">
          <h2 className="typo-display-sm">Hva skjer nå</h2>
          {!fullySigned && (
            <p className="mt-1">
              Når alle parter har signert avtalen starter jobben med å klargjøre
              boligen for salg.
            </p>
          )}
          <div className="grid grid-cols-1 mt-4 gap-4 md:grid-cols-3">
            <NextStep step={1}>
              Vi innhenter dokumenter og nødvendig informasjon om boligen din.
            </NextStep>
            <NextStep step={2}>
              Vi finner datoer for besøk av bygningssakkyndig, fotograf og
              eventuelt stylist.
            </NextStep>
            <NextStep step={3}>
              Vi finner relevante interessenter blant våre boligsøkere.
            </NextStep>
          </div>
        </div>

        <div className="flex md:items-center overflow-hidden max-md:flex-col justify-between gap-2 rounded-lg border border-muted">
          <div className="p-6 md:p-8 flex flex-col ">
            <h2 className="typo-display-sm">Følg boligsalget i vår app</h2>
            <ul className="pl-[1em] list-image-dot mt-2 [&_li]:text-pretty">
              <li>
                Oversikt over hva som gjøres underveis i hele salgsperioden.
              </li>
              <li>
                Tips og råd til alt fra styling og klargjøring av boligen din.
              </li>
              <li>
                Statistikk om boligannonsen, salgsrapporten og interessenter.
              </li>
              <li>Skreddersydde tilbud på flytting, oppussing og mer. </li>
              <li>Alt samlet på ett sted, både når du selger og kjøper.</li>
            </ul>
            <Button
              iconEnd={<ArrowUpRight />}
              target="_blank"
              className="mt-8 mr-auto"
              variant="tertiary"
              size="sm"
              href="https://nordvik.app/customer"
            >
              Nordvik App
            </Button>
          </div>
          <div className="flex md:justify-center md:px-6 md:mt-6 grow">
            <Image
              className="md:rounded-t-lg max-md:w-full"
              src={nordvikApp}
              alt="Nordvik App"
              width={240}
            />
          </div>
        </div>
      </LowerContent>
    </main>
  )
}
