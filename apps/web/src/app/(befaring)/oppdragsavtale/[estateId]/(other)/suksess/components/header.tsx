'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'

import { cn } from '@nordvik/theme/cn'

import nordvikLogo from '../../../../../../../../public/logo_small.svg'

import NordvikFullLogo from './nordvik-logo-full.svg'

const MotionImage = motion.create(Image)

export function Header({
  children,
  skeleton,
}: {
  children?: React.ReactNode
  skeleton?: boolean
}) {
  return (
    <header>
      <div className="absolute inset-0 h-[400px] flex-col items-center lg:h-[530px]">
        <motion.div
          data-theme="dark"
          transition={{
            type: 'spring',
            damping: 30,
            stiffness: 200,
            delay: 1,
          }}
          variants={{
            expanded: { height: '100vh' },
            default: { height: '100%' },
          }}
          initial="expanded"
          animate={skeleton ? 'expanded' : 'default'}
          className="bg-root w-full"
        />
      </div>
      <div className="container max-w-[58rem] mb-6 relative z-50 mt-16 flex flex-col gap-12 lg:mt-32">
        <div
          className={cn(
            'self-center relative flex animate-in fade-in-0 duration-500',
            skeleton && '[animation-delay:2s] fill-mode-backwards',
          )}
        >
          {skeleton && (
            <div className="absolute w-[38%] top-[41%] m-auto left-1/2 -translate-x-1/2">
              <Image
                src={nordvikLogo}
                alt="Nordvik"
                className="w-full animate-pulse"
              />
            </div>
          )}
          <MotionImage
            src={NordvikFullLogo}
            alt="Nordvik Logo"
            variants={{
              initial: { scale: 1.2, y: 50 },
              hidden: { scale: 1.2, opacity: 0 },
              default: { scale: 1, y: 0 },
            }}
            initial={skeleton ? 'hidden' : 'initial'}
            animate={skeleton ? 'hidden' : 'default'}
            transition={{
              type: 'spring',
              damping: 40,
              stiffness: 200,
              delay: 1,
            }}
          />
        </div>
        {children && (
          <motion.div
            initial={{ opacity: 0, y: 300 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              opacity: {
                duration: 0.2,
                delay: 1,
              },
              type: 'spring',
              damping: 40,
              stiffness: 200,
              delay: 1,
            }}
            className="rounded-lg bg-root shadow-lg"
          >
            {children}
          </motion.div>
        )}
      </div>
    </header>
  )
}

export function LowerContent({ children }: { children: React.ReactNode }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 500 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        opacity: {
          duration: 0.1,
          delay: 1,
        },
        type: 'spring',
        damping: 40,
        stiffness: 200,
        delay: 1,
      }}
      className="container max-w-[58rem] relative z-50 mb-8 flex flex-col gap-12 mt-10"
    >
      {children}
    </motion.div>
  )
}
