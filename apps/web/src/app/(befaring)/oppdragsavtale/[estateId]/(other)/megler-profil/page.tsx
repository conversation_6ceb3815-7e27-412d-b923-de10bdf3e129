'use client'

import { ArrowLeftIcon } from 'lucide-react'
import { useParams } from 'next/navigation'

import { TextButton } from '@nordvik/ui/text-button'

import { useListingAgreementTeamQuery } from '@/api/generated-client'
import { BrokerPresentation } from '@/components/broker-profile/presentation/broker-presentation'
import { NordvikLoader } from '@/components/nordvik-loader'

export default function Page() {
  const { estateId } = useParams<{ estateId: string }>()
  const { data: broker } = useListingAgreementTeamQuery(
    { estateId },
    { select: (data) => data.estate?.mainBroker },
  )

  return (
    <div data-theme="dark" className="bg-root min-h-screen flex flex-col">
      <div className="flex items-center h-16 border-b border-b-muted">
        <div className="container">
          <div className="flex items-center gap-1.5">
            <ArrowLeftIcon className="w-4 h-4 ink-subtle mb-px" />
            <TextButton href="./" subtle>
              Tilbake
            </TextButton>
          </div>
        </div>
      </div>
      {broker ? <BrokerPresentation broker={broker} /> : <NordvikLoader />}
    </div>
  )
}
