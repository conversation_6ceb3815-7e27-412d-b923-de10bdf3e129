import Footer from '@befaring/components/footer'
import { Header } from '@befaring/components/layout-header'
import {
  PrefetchInspectionPages,
  PrefetchPages,
} from '@befaring/components/prefetch-inspection'

import {
  AgreementAndInspectionDocument,
  GQLAgreementAndInspectionQuery,
} from '@/api/generated-client'
import { gqlServerFetch } from '@/api/gqlServerFetch'
import { findOrCreateInspectionFolder } from '@/app/(protected)/(sidebar)/oppdrag/detaljer/[slug]/components/assignment-header/actions/create-inspection'
import { fetchAgreementAndSigners } from '@/app/(protected)/(sidebar)/oppdrag/detaljer/[slug]/components/inspection/utils'
import Providers from '@/components/providers'
import { getCurrentUser } from '@/lib/session'

import { authorize } from '../../authorize'
import { BudgetPriceProxyProvider } from '../../context/budget-proxy-context'

export default async function BefaringLayout(props: {
  children: React.ReactNode
  params: Promise<{ estateId: string }>
}) {
  const params = await props.params

  const { children } = props

  const [user] = await Promise.all([
    getCurrentUser(),
    authorize({ estateId: params.estateId }),
  ])

  if (user) {
    await findOrCreateInspectionFolder(params.estateId)
  }

  const [{ data: agreementAndInspectionData }, listingAgreementAndSigners] =
    await Promise.all([
      gqlServerFetch<GQLAgreementAndInspectionQuery>(
        AgreementAndInspectionDocument,
        { estateId: params.estateId },
      ),
      fetchAgreementAndSigners(params.estateId).catch((error) => {
        console.error(
          `Failed to fetch listing agreement and signers for estate ${params.estateId}`,
          error,
        )
      }),
    ])

  return (
    <Providers user={user}>
      <BudgetPriceProxyProvider>
        <div className="min-h-screen flex flex-col [--footer-height:6rem] [@media(min-height:50rem)]:[--footer-height:8rem]">
          {user && (
            <Header
              estateId={params.estateId}
              isValuation={Boolean(
                agreementAndInspectionData?.estate?.isValuation,
              )}
              sendOfferData={{
                estate: agreementAndInspectionData?.estate,
                signersData: listingAgreementAndSigners?.signerData,
                listingAgreement:
                  listingAgreementAndSigners?.listingAgreementData,
              }}
            />
          )}
          {children}

          <Footer
            rootPrefix="/oppdragsavtale"
            startLink="start"
            links={[
              { href: 'salgsprosess', label: 'Salgsprosess' },
              { href: 'fremdriftsplan', label: 'Fremdriftsplan' },
              { href: 'megler', label: 'Megler og team' },
              { href: 'omrade', label: 'Ditt område' },
            ]}
            hasPublishedAgreement={Boolean(
              agreementAndInspectionData?.estate?.inspectionFolder
                ?.listingAgreementActive,
            )}
          />
        </div>
      </BudgetPriceProxyProvider>
      <PrefetchInspectionPages
        rootPrefix="/oppdragsavtale"
        pages={[
          PrefetchPages.Start,
          PrefetchPages.Fremdriftsplan,
          PrefetchPages.Salgsprosess,
          PrefetchPages.Megler,
          PrefetchPages.Omrade,
        ]}
      />
    </Providers>
  )
}
