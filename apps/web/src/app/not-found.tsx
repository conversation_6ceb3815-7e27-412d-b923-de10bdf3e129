import Image from 'next/image'
import React from 'react'

import { Button } from '@nordvik/ui/button'

import { EmptyState } from '@/components/empty-state'
import { FeedbackButton } from '@/components/feedback-button'
import { IntercomProvider } from '@/lib/analytics/intercom'
import { TrackEventOnce } from '@/lib/analytics/track-event'
import { getCurrentUser } from '@/lib/session'

const NotFound = async () => {
  const user = await getCurrentUser()

  return (
    <IntercomProvider user={user}>
      <TrackEventOnce event="not_found" />
      <div className="flex flex-col">
        <div
          data-theme="dark"
          className="bg-brand-muted p-4 flex justify-center mb-32"
        >
          <Image src="/logo.svg" alt="" width={173} height={56} />
        </div>
        <EmptyState
          illustration="not-found"
          title="Siden du leter etter finnes ikke"
          description={
            user
              ? 'Hvis du tror noe er galt kan du sende oss en melding.'
              : '<PERSON><PERSON> skjer vanligvis fordi lenken er feil eller utdatert.'
          }
          actions={
            user ? (
              <>
                <FeedbackButton size="lg" />
                <Button href="/" replace size="lg">
                  Gå til forsiden
                </Button>
              </>
            ) : null
          }
        />
      </div>
    </IntercomProvider>
  )
}

export default NotFound
