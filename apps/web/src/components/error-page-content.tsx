'use client'

import { HomeIcon } from 'lucide-react'

import { But<PERSON> } from '@nordvik/ui/button'

import { redirectFromErrorPage } from '@/actions/redirect-from-error-page'
import { EmptyState, animateInClassName } from '@/components/empty-state'

export function ErrorPageContent({
  redirectTo,
  buttonText,
  description = 'Vi finner ikke det du leter etter',
}: {
  redirectTo?: string
  buttonText?: string
  description?: string
}) {
  return (
    <div
      data-theme="dark"
      className="flex flex-col items-center justify-center h-screen bg-root"
    >
      <div>
        <EmptyState
          className="my-auto py-6"
          illustration="error"
          title="Noe gikk galt"
          description={description}
        />
      </div>
      <Button
        onClick={() => redirectFromErrorPage(redirectTo)}
        iconStart={<HomeIcon />}
        className={animateInClassName}
        size="lg"
      >
        {buttonText || 'Til forsiden'}
      </Button>
    </div>
  )
}
