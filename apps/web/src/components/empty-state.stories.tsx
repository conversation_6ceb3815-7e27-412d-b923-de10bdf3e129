import { ArrowUpRight } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import { StoryPartHeading } from '@nordvik/ui/story-blocks'

import { EmptyState } from './empty-state'

export default {
  title: 'Components / Empty State',
  parameters: {
    theme: 'both',
  },
}

export const EmptyStateStory = () => (
  <>
    <StoryPartHeading>Not found</StoryPartHeading>
    <EmptyState
      className="my-20"
      title="Siden du leter etter finnes ikke"
      illustration="not-found"
      description={`Du vil kanskje tilbake til nyheter. Hvis du tror noe er galt, kan du sende oss en melding.`}
      actions={
        <>
          <Button variant="outline" size="lg">
            Send tilbakemelding
          </Button>
          <Button size="lg">Gå til nyheter</Button>
        </>
      }
    />

    <StoryPartHeading>No data</StoryPartHeading>
    <EmptyState
      className="my-20"
      illustration="no-data"
      description={`Ingen oppdrag i «solgte»`}
    />

    <StoryPartHeading>No broker</StoryPartHeading>
    <EmptyState
      className="my-20"
      illustration="no-broker"
      title="Mangler ansvarlig megler"
      description="For å bruke oppdragsavtalen på Nordvik Megler må du sette opp budsjettet i Next for denne boligen først."
      actions={
        <Button variant="tertiary" size="sm" iconEnd={<ArrowUpRight />}>
          Gå til Next
        </Button>
      }
    />

    <StoryPartHeading>No incidents</StoryPartHeading>
    <EmptyState
      className="my-20"
      illustration="no-incidents"
      description="Det ser ut som alt fungerer som det skal!"
    />

    <StoryPartHeading>No search results</StoryPartHeading>
    <EmptyState
      className="my-20"
      illustration="no-search-results"
      description="Det ser ut som det ikke er noen nyheter for øyeblikket i denne kategorien"
    />

    <StoryPartHeading>No top list</StoryPartHeading>
    <EmptyState
      className="my-20"
      illustration="no-toplist"
      description="Ingen flere resultater å vise i listen"
    />
  </>
)
EmptyStateStory.storyName = 'Empty State'
