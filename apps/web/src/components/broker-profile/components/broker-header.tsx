import { kebabCase } from 'lodash'
import { AtSignIcon, MailIcon, PlusIcon, SmartphoneIcon } from 'lucide-react'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@nordvik/ui/dialog'
import { Input } from '@nordvik/ui/input'
import { TextButton, TextButtonProps } from '@nordvik/ui/text-button'

import { KTI } from '@/components/broker-profile/components/KTI'
import { InstagramIcon } from '@/components/broker-profile/components/instagram-icon'
import { Logo } from '@/components/logo'
import { formatPhoneNumber } from '@/lib/format-phone-number'

import { BrokerForPresentation } from '../presentation/type'

export function BrokerHeader({
  broker,
  updateInstagram,
}: {
  broker: Pick<
    BrokerForPresentation,
    | 'name'
    | 'image'
    | 'department'
    | 'instagram'
    | 'email'
    | 'mobilePhone'
    | 'rating'
    | 'title'
  >
  updateInstagram?: (instagram: string | null) => Promise<void>
}) {
  return (
    <section className="@container/section">
      <div className="flex gap-6 lg:gap-8 @2xl/section:mt-[--gap-y] flex-col @2xl/section:flex-row @2xl/section:items-center justify-center">
        <div className="@2xl/section:basis-[15rem] shrink-[2] -mx-[--container-padding] @2xl/section:mx-0 relative">
          <Avatar imageUrl={broker?.image?.medium} />
          <div className="absolute inset-x-0 top-1/2 bottom-0 bg-gradient-to-t from-background-root opacity-70 @2xl/section:hidden" />
        </div>
        <div className="flex flex-col gap-4 shrink-[1] basis-[60%] -mt-[4.5rem] @2xl/section:mt-0 relative">
          <div className="flex flex-col gap-2">
            {broker?.department?.name && (
              <div className="typo-detail-md ink-subtle">
                Nordvik {broker?.department?.name}
              </div>
            )}
            <h1 className="typo-display-xl text-[3rem] md:text-[3.5rem] leading-[95%] ink-gold text-balance">
              {broker?.name}
            </h1>
            {broker?.rating && broker?.department?.displayKtiOnEmployee && (
              <KTI
                average={broker?.rating.average}
                count={broker?.rating.count}
              />
            )}
          </div>
          <div className="typo-body-md flex flex-col gap-2 items-start">
            {broker?.title && (
              <div className="typo-body-md text-pretty">{broker.title}</div>
            )}
            {broker?.email && (
              <TextButtonWithIcon
                icon={MailIcon}
                href={`mailto:${broker.email}`}
              >
                {broker.email}
              </TextButtonWithIcon>
            )}
            {broker?.mobilePhone && (
              <TextButtonWithIcon
                icon={SmartphoneIcon}
                href={`tel:${broker.mobilePhone}`}
              >
                {formatPhoneNumber(broker?.mobilePhone)}
              </TextButtonWithIcon>
            )}
            <Instagram
              name={broker?.name}
              instagram={broker?.instagram}
              updateInstagram={updateInstagram}
            />
          </div>
        </div>
      </div>
    </section>
  )
}

function Avatar({ imageUrl }: { imageUrl?: string | null }) {
  return (
    <div className="w-full aspect-square">
      {imageUrl ? (
        <img
          src={imageUrl}
          alt=""
          className="size-full @2xl/section:rounded-full object-cover"
        />
      ) : (
        <PlaceholderImage />
      )}
    </div>
  )
}

function PlaceholderImage() {
  return (
    <div className="size-full aspect-square flex bg-brand-muted rounded-full fill-gold-emphasis">
      <div className="m-[30%]">
        <Logo className="size-full" />
      </div>
    </div>
  )
}

function Instagram({
  name,
  instagram,
  updateInstagram,
}: {
  name?: string | null
  instagram?: string | null
  updateInstagram?: (instagram: string | null) => void
}) {
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [open, setOpen] = React.useState(false)
  const title = instagram ? 'Endre Instagram konto' : 'Legg til Instagram konto'

  const trigger = instagram ? (
    <TextButtonWithIcon
      icon={InstagramIcon}
      aria-label={`${title}. Nåværende: ${instagram}`}
    >
      {instagram}
    </TextButtonWithIcon>
  ) : (
    <TextButtonWithIcon icon={PlusIcon}>{title}</TextButtonWithIcon>
  )

  if (!updateInstagram) {
    if (!instagram) return null
    return (
      <TextButtonWithIcon
        icon={InstagramIcon}
        href={`https://instagram.com/${instagram}`}
        target="_blank"
      >
        {instagram}
      </TextButtonWithIcon>
    )
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent title={title} size="sm" data-theme="dark">
        <form
          onSubmit={async (event) => {
            event.preventDefault()
            setLoading(true)
            let input = event.currentTarget.username.value
            if (!input?.trim()) {
              await updateInstagram(null)
              setOpen(false)
            } else {
              if (input.startsWith('@')) {
                input = input.slice(1)
              } else if (input.startsWith('https://')) {
                input = getInstagramUsername(input)
              }

              if (!isValidInstagramUsername(input)) {
                setError('Ugyldig Instagram brukernavn')
              } else {
                setError(null)
                await updateInstagram(input)
                setOpen(false)
              }
            }
            setLoading(false)
          }}
        >
          <DialogDescription divider>
            <label>
              <span className="sr-only">Brukernavn</span>
              <Input
                variant="fill"
                name="username"
                type="text"
                errorMessage={error}
                addonLeft={<AtSignIcon />}
                onChange={(event) => {
                  if (error && isValidInstagramUsername(event.target.value)) {
                    setError(null)
                  }
                }}
                placeholder={`Feks. ${kebabCase(name ?? 'nordvikbolig')}`}
                defaultValue={instagram ?? undefined}
              />
            </label>
          </DialogDescription>
          <DialogFooter>
            <DialogClose asChild>
              <Button size="md" variant="ghost">
                Avbryt
              </Button>
            </DialogClose>
            <Button size="md" loading={loading} type="submit">
              Lagre
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function getInstagramUsername(url?: string | null) {
  return url?.match(/instagram\.com\/(.*?)\//)?.[1]
}

function isValidInstagramUsername(username?: string | null) {
  return username?.match(/^[a-zA-Z0-9._]+$/)
}

const TextButtonWithIcon = function TextButtonWithIcon({
  ref,
  icon: Icon,
  ...rest
}: {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>
  ref?: React.Ref<HTMLDivElement>
} & TextButtonProps) {
  return (
    <div className="flex items-center gap-2" ref={ref}>
      <Icon className="size-4 mt-px ink-muted" />
      <TextButton {...rest} />
    </div>
  )
}
