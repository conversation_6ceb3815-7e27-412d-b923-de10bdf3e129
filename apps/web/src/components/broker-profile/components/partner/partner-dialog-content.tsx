import { AnimatePresence, LayoutGroup, motion } from 'framer-motion'
import { AtSignIcon, ImagePlusIcon, Trash2Icon } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import * as yup from 'yup'
import { ValidationError } from 'yup'

import { <PERSON><PERSON> } from '@nordvik/ui/button'
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
} from '@nordvik/ui/dialog'
import { Input } from '@nordvik/ui/input'
import * as Select from '@nordvik/ui/select'
import { Textarea } from '@nordvik/ui/textarea'
import { Tooltip, TooltipContent, TooltipTrigger } from '@nordvik/ui/tooltip'

import { GQLBrokerPartner } from '@/api/generated-client'

import {
  PartnerCategoryValue,
  getCategory,
  partnerCategories,
} from './partner-categories'
import { PartnerProfilePicture } from './partner-category-icon'

const partnerSchema = yup.object({
  id: yup.string().nullable().optional(),
  name: yup.string().required('Navn på partner er påkrevd'),
  instagram: yup
    .string()
    .matches(/^[a-zA-Z0-9._]+$/, 'Ugyldig Instagram-brukernavn')
    .notRequired(),
  website: yup
    .string()
    .notRequired()
    .matches(/^https?:\/\//, 'Ugyldig lenke. Husk http:// eller https://'),
  description: yup.string().required('Beskrivelse er påkrevd'),
  category: yup
    .string()
    .oneOf(partnerCategories.map((c) => c.value))
    .required('Kategori er påkrevd'),

  profilePicture: yup.mixed<string | File>().nullable().optional(),

  images: yup
    .array()
    .of(yup.mixed<string | File>())
    .test(
      'min-length-or-empty',
      'Minst 2 bilder er påkrevd, eller ingen.',
      (value) => {
        // Allow an empty array or an array with at least 2 items
        return Array.isArray(value) && (value.length === 0 || value.length >= 2)
      },
    )
    .optional(),
})

export type PartnerFormValues = yup.InferType<typeof partnerSchema>

function emptyStringsToNull(obj: Record<string, string | File>) {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      value === '' ? null : value,
    ]),
  )
}

export function PartnerDialogContent({
  partner,
  onSave,
}: {
  partner?: GQLBrokerPartner
  onSave: (partner: PartnerFormValues) => Promise<void>
}) {
  const id = React.useId()
  const [error, setError] = React.useState<{
    path: string
    message: string
  } | null>(null)
  const [isPending, setIsPending] = React.useState(false)
  const [categoryValue, setCategoryValue] =
    React.useState<PartnerCategoryValue>(
      (partner?.category as PartnerCategoryValue) || 'photographer',
    )
  const category = getCategory(categoryValue)

  const [profilePicture, setProfilePicture] = React.useState<
    string | File | null
  >(partner?.profilePicture || null)

  const [images, setImages] = React.useState<(File | string)[]>(
    partner?.images || [],
  )
  const [description, setDescription] = React.useState(
    partner?.description ?? category.templates?.description,
  )

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    setError(null)
    setIsPending(true)
    const fields = emptyStringsToNull(
      Object.fromEntries(new FormData(event.currentTarget)),
    )

    const dataToValidate = { ...fields, profilePicture, images }
    if (category.hideImages) {
      dataToValidate.images = []
    }
    if (category.hideInstagram && 'instagram' in dataToValidate) {
      dataToValidate.instagram = null
    }

    try {
      const validatedData = partnerSchema.validateSync(dataToValidate)
      await onSave({
        ...validatedData,
        id: partner?.id,
        images: validatedData.images?.filter(Boolean),
      })
    } catch (error) {
      if (error instanceof ValidationError && error.path) {
        console.error(error)
        setError({ path: error.path, message: error.message })
      } else {
        throw error
      }
    } finally {
      setIsPending(false)
    }
  }

  function onCategoryChange(value: PartnerCategoryValue) {
    // When changing category, reset description to default template description if empty or the same as the current template
    if (!description || category.templates?.description === description) {
      const nextCategory = getCategory(value)
      setDescription(nextCategory.templates?.description ?? '')
    }
    setCategoryValue(value)
  }

  return (
    <DialogContent
      title={
        partner ? 'Oppdater samarbeidspartner' : 'Legg til samarbeidspartner'
      }
      size="lg"
      data-theme="dark"
      onOpenAutoFocus={(event) => event.preventDefault()}
    >
      <DialogDescription divider>
        <form className="flex flex-col mt-4 gap-6" onSubmit={onSubmit} id={id}>
          <div className="flex gap-6 items-center">
            <div className="relative">
              <PartnerProfilePicture
                onAdd={setProfilePicture}
                onRemove={() => setProfilePicture(null)}
                category={categoryValue}
                profilePicture={profilePicture}
              />
            </div>
            <div className="flex flex-col grow">
              <div className="mb-1.5 typo-label-md">Kategori</div>
              <Select.Select
                name="category"
                value={category.value}
                onValueChange={onCategoryChange}
              >
                <Select.SelectTrigger className="grow" variant="fill">
                  <Select.SelectValue placeholder="Kategori" />
                </Select.SelectTrigger>
                <Select.SelectContent>
                  {partnerCategories
                    .filter((c) => !c.system)
                    .map((category) => (
                      <Select.SelectItem
                        key={category.value}
                        value={category.value}
                      >
                        {category.label}
                      </Select.SelectItem>
                    ))}
                </Select.SelectContent>
              </Select.Select>
            </div>
          </div>
          <Input
            name="name"
            label="Navn"
            variant="fill"
            autoFocus
            errorMessage={error?.path === 'name' ? error.message : undefined}
            placeholder={category.placeholders.name}
            defaultValue={partner?.name}
          />
          {!category.hideInstagram && (
            <Input
              name="instagram"
              label="Instagram"
              variant="fill"
              addonLeft={<AtSignIcon />}
              onBlur={(event) => {
                const username = getCleanedInstagramUsername(event.target.value)
                if (username) {
                  event.target.value = username
                }
              }}
              errorMessage={
                error?.path === 'instagram' ? error.message : undefined
              }
              placeholder={category.placeholders.instagram}
              defaultValue={partner?.instagram}
            />
          )}

          <Input
            name="website"
            label="Nettside"
            variant="fill"
            errorMessage={error?.path === 'website' ? error.message : undefined}
            placeholder={category.placeholders.website}
            defaultValue={partner?.website}
          />
          <Textarea
            name="description"
            label="Beskrivelse"
            rows={4}
            variant="fill"
            key={categoryValue}
            errorMessage={
              error?.path === 'description' ? error.message : undefined
            }
            placeholder={category.placeholders.description}
            value={description}
            onChange={(event) => setDescription(event.target.value)}
          />

          {category.disclaimer && (
            <Textarea
              name="disclaimer"
              label="Ansvarsfraskrivelse"
              rows={4}
              variant="fill"
              key="disclaimer"
              value={category.disclaimer}
              disabled
            />
          )}

          {!category.hideImages && (
            <div className="flex flex-col gap-1.5">
              <div className="typo-label-md">Bilder</div>
              <div className="flex gap-4 flex-wrap">
                <LayoutGroup>
                  <AnimatePresence mode="sync" initial={false}>
                    {images.map((image) => (
                      <ImageItem
                        key={typeof image === 'string' ? image : image.name}
                        image={image}
                        onRemove={() => {
                          setImages((images) =>
                            images.filter((i) => i !== image),
                          )
                        }}
                      />
                    ))}
                    <AddImageButton
                      images={images}
                      onAdd={(files) =>
                        setImages((images) =>
                          [...images, ...files].filter(Boolean),
                        )
                      }
                    />
                  </AnimatePresence>
                </LayoutGroup>
              </div>
              {error?.path === 'images' ? (
                <div className="mt-3 typo-body-sm ink-danger">
                  {error.message}
                </div>
              ) : (
                <div className="mt-3 typo-body-sm ink-muted">
                  Vi anbefaler 3 til 5 bilder.
                </div>
              )}
            </div>
          )}
        </form>
      </DialogDescription>
      <DialogFooter>
        <DialogClose asChild>
          <Button size="md" variant="ghost">
            Avbryt
          </Button>
        </DialogClose>
        <Button size="md" type="submit" loading={isPending} form={id}>
          {partner ? 'Oppdater' : 'Legg til'}
        </Button>
      </DialogFooter>
    </DialogContent>
  )
}

function getCleanedInstagramUsername(string?: string | null) {
  if (!string) return null
  if (string.startsWith('@')) return string.slice(1)
  if (!string.match(/^https?:\/\//)) return null
  const match = string.match(/instagram\.com\/(.*?)\//)
  return match?.[1]
}

const ImageItem = function ImageItem({
  ref,
  image,
  onRemove,
}: {
  image: File | string
  onRemove?: () => void
  ref?: React.RefObject<HTMLDivElement>
}) {
  const [preview, setPreview] = React.useState<string | null>(null)

  React.useEffect(() => {
    if (image instanceof File) {
      const objectUrl = URL.createObjectURL(image)
      setPreview(objectUrl)
      return () => {
        URL.revokeObjectURL(objectUrl)
      }
    }
    setPreview(image ?? null)
  }, [image])

  return (
    <motion.div
      key={preview}
      layout
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ type: 'spring', stiffness: 260, damping: 30 }}
      className="size-24 rounded-md relative"
      ref={ref}
    >
      {preview && (
        <Image
          src={preview}
          alt=""
          width={100}
          height={100}
          onClick={() => {
            window.open(preview, '_blank')
          }}
          className="size-full object-cover rounded-md"
        />
      )}
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            type="button"
            onClick={onRemove}
            data-theme="light"
            className="absolute hover:bg-gold-subtle -right-2 -top-2 size-6 cursor-pointer border border-subtle rounded-full bg-root ink-success p-1"
          >
            <Trash2Icon className="size-full" />
          </button>
        </TooltipTrigger>
        <TooltipContent>Fjern bilde</TooltipContent>
      </Tooltip>
    </motion.div>
  )
}

function AddImageButton({
  onAdd,
  images,
  ref,
}: {
  onAdd: (files: File[]) => void
  images: (File | string)[]
  ref?: React.RefObject<HTMLLabelElement>
}) {
  return (
    <motion.label
      layout
      ref={ref}
      transition={{ type: 'spring', stiffness: 260, damping: 30 }}
      className="border bg-float relative size-24 transition-colors duration-150 ease-in-out hover:bg-brand-muted border-subtle rounded-md flex items-center justify-center"
    >
      <span className="sr-only">Legg til bilde</span>
      <ImagePlusIcon className="size-[40%]" />
      <input
        type="file"
        multiple
        key={images.length}
        accept="image/png,image/jpeg,image/webp"
        className="absolute inset-0 opacity-0 cursor-pointer appearance-none"
        onChange={(event) => {
          if (event.target.files) {
            const files = Array.from(event.target.files)
            const oversizedFiles = files.filter(
              (file) => file.size > 5 * 1024 * 1024,
            )
            if (files.some((file) => file.name.endsWith('avif'))) {
              alert('AVIF-bilder støttes ikke. Bruk .png, .jpg eller .webp')
              return
            }
            if (oversizedFiles.length > 0) {
              alert(
                'Følgende bilder er for store (maks 5MB): ' +
                  oversizedFiles.map((f) => f.name).join(', '),
              )
              return
            }
            onAdd(files)
          }
        }}
      />
    </motion.label>
  )
}
