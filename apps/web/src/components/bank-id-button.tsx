import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button, ButtonProps } from '@nordvik/ui/button'

type BankIdButtonProps = {
  href?: string
  onClick?: () => void
  loading?: boolean
  disabled?: boolean
  size?: ButtonProps['size']
  className?: string
}

const BankIdButton = ({ className, ...props }: BankIdButtonProps) => {
  return (
    <Button
      data-theme="dark"
      variant="unstyled"
      className={cn('bg-[#341749] hover:bg-[#341749]/90', className)}
      iconStart={bankIdIcon}
      {...props}
      size="lg"
    >
      Signer med BankID
    </Button>
  )
}

export { BankIdButton }

const bankIdIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.16406 3.99479C1.16406 3.6266 1.46254 3.32812 1.83073 3.32812H4.4974C4.86559 3.32812 5.16406 3.6266 5.16406 3.99479C5.16406 4.36298 4.86559 4.66146 4.4974 4.66146H1.83073C1.46254 4.66146 1.16406 4.36298 1.16406 3.99479Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.16406 9.33073C1.16406 8.96254 1.46254 8.66406 1.83073 8.66406H4.4974C4.86559 8.66406 5.16406 8.96254 5.16406 9.33073C5.16406 9.69892 4.86559 9.9974 4.4974 9.9974H1.83073C1.46254 9.9974 1.16406 9.69892 1.16406 9.33073Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.16406 11.9948C1.16406 11.6266 1.46254 11.3281 1.83073 11.3281H4.4974C4.86559 11.3281 5.16406 11.6266 5.16406 11.9948C5.16406 12.363 4.86559 12.6615 4.4974 12.6615H1.83073C1.46254 12.6615 1.16406 12.363 1.16406 11.9948Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.5 6.66667C6.5 6.29848 6.79848 6 7.16667 6H9.83333C10.2015 6 10.5 6.29848 10.5 6.66667C10.5 7.03486 10.2015 7.33333 9.83333 7.33333H7.16667C6.79848 7.33333 6.5 7.03486 6.5 6.66667Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.5 9.33073C6.5 8.96254 6.79848 8.66406 7.16667 8.66406H9.83333C10.2015 8.66406 10.5 8.96254 10.5 9.33073C10.5 9.69892 10.2015 9.9974 9.83333 9.9974H7.16667C6.79848 9.9974 6.5 9.69892 6.5 9.33073Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.832 3.99479C11.832 3.6266 12.1305 3.32812 12.4987 3.32812H15.1654C15.5336 3.32812 15.832 3.6266 15.832 3.99479C15.832 4.36298 15.5336 4.66146 15.1654 4.66146H12.4987C12.1305 4.66146 11.832 4.36298 11.832 3.99479Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.832 6.66667C11.832 6.29848 12.1305 6 12.4987 6H15.1654C15.5336 6 15.832 6.29848 15.832 6.66667C15.832 7.03486 15.5336 7.33333 15.1654 7.33333H12.4987C12.1305 7.33333 11.832 7.03486 11.832 6.66667Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.832 11.9948C11.832 11.6266 12.1305 11.3281 12.4987 11.3281H15.1654C15.5336 11.3281 15.832 11.6266 15.832 11.9948C15.832 12.363 15.5336 12.6615 15.1654 12.6615H12.4987C12.1305 12.6615 11.832 12.363 11.832 11.9948Z"
      fill="currentColor"
    />
  </svg>
)
