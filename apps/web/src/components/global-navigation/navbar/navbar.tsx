'use client'

import { <PERSON><PERSON>ef<PERSON> } from 'lucide-react'
import type { Session } from 'next-auth'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { Link } from '@nordvik/ui/global-navigation-progress/link'
import { TextButton } from '@nordvik/ui/text-button'

import {
  GQLCraftCmsIncidentLevel,
  type GQLIncidentsQuery,
  useIncidentsQuery,
} from '@/api/generated-client'

import ProfileMenu from '../../profile-menu/profile-menu'
import { NavigationSheet } from '../navigation-sheet'

import { NavbarIncidents } from './navbar-incidents'
import { NavbarIncidentsIndicator } from './navbar-incidents-indicator'
import { useAlerts } from './useAlerts'

export interface NavbarProps {
  className?: string
  color?: 'white' | 'muted' | 'dark'
  user?: Session['user']
  incidentsResponse?: GQLIncidentsQuery | null
  backLink?: {
    href: string
    label: string
  }
}

export function Navbar({
  className,
  color = 'white',
  user,
  backLink,
}: NavbarProps) {
  const { data } = useIncidentsQuery(
    { active: true },
    {
      refetchInterval: 30000,
    },
  )

  const pathname = usePathname()
  const { handleDismiss, visibleAlerts, dismissedAlerts } = useAlerts(
    data?.cmsIncidents.items.filter(
      (incident) => incident.level === GQLCraftCmsIncidentLevel.Critical,
    ),
  )

  const showIncidentsList = ['/nyheter', '/dashboard'].includes(pathname)
  const segments = pathname.split('/')
  let isNestedRoute = segments.length > 2

  if (segments.includes('oppdrag') && segments.includes('status')) {
    isNestedRoute = false
  }

  return (
    <>
      <div
        data-theme={color === 'dark' && 'dark'}
        className="[@supports(container-type:inline-size)]:hidden"
      >
        <div className="text-center typo-label-md bg-root-muted py-1 px-2 text-pretty max-lg:border-b border-muted">
          Din nettleser er utdatert og tjenesten kan oppleves feil. Oppdater
          eller bytt til en nyere nettleser.
        </div>
      </div>
      <div
        data-theme={color === 'dark' && 'dark'}
        className={cn(
          // The background should be bg-root-muted, but since the theme is either dark or light, it does not work as of now.
          // If it's in light mode, it won't get the correct color when on small screens. If in dark mode, everything is fine.
          // The fix could be to support themes with media queries. lg:mode-dark
          'flex h-[60px] w-full items-center max-lg:bg-[#193d41ff] lg:border-b lg:border-b-muted lg:bg-root',
          color === 'muted' && 'lg:!bg-root-muted',
          className,
        )}
      >
        <div className="flex w-full grow items-center justify-between px-4 lg:px-12">
          <Link href="/">
            <Image
              src="/logo.svg"
              className="hidden max-w-[160px] sm:block lg:hidden"
              priority
              width={160}
              height={50}
              alt=""
            />
            <Image
              src="/logo_small.svg"
              className="svg:fill-gold-emphasis max-w-[160px] sm:hidden"
              width={32}
              height={32}
              alt=""
            />
          </Link>
          {isNestedRoute || backLink ? (
            <DesktopGoBackButton backLink={backLink} />
          ) : null}

          <nav className="ml-auto flex items-center gap-5">
            <NavbarIncidentsIndicator
              dismissedAlerts={dismissedAlerts}
              alerts={data?.cmsIncidents.items}
              colorTheme={color}
            />
            <ProfileMenu user={user} />
            <NavigationSheet user={user} />
          </nav>
        </div>
      </div>
      {showIncidentsList ? (
        <NavbarIncidents
          handleDismiss={handleDismiss}
          visibleAlerts={visibleAlerts}
        />
      ) : null}
      {(isNestedRoute || backLink) && (
        <MobileGoBackButton backLink={backLink} colorTheme={color} />
      )}
    </>
  )
}

function DesktopGoBackButton({
  backLink,
}: {
  backLink: NavbarProps['backLink']
}) {
  const router = useRouter()

  return (
    <Button
      className="hidden lg:flex"
      variant="ghost"
      onClick={() => {
        backLink?.href ? null : router.back()
      }}
      href={backLink?.href}
      iconStart={<ArrowLeft />}
      size="lg"
    >
      <span className="first-letter:uppercase">
        {backLink ? backLink.label : 'Tilbake'}
      </span>
    </Button>
  )
}

function MobileGoBackButton({
  backLink,
  colorTheme,
}: {
  backLink: NavbarProps['backLink']
  colorTheme?: string
}) {
  const router = useRouter()

  return (
    <div
      data-theme={colorTheme === 'dark' && 'dark'}
      className={cn(
        'bg-root lg:hidden',
        colorTheme === 'muted' && 'bg-root-muted',
      )}
    >
      <TextButton
        bold
        subtle
        size="lg"
        className="p-4"
        iconStart="arrow"
        onClick={() => {
          backLink?.href ? router.push(backLink.href) : router.back()
        }}
        data-theme={colorTheme}
      >
        Tilbake
      </TextButton>
    </div>
  )
}
