import { DialogTitle } from '@radix-ui/react-dialog'
import React from 'react'

import { DialogDescription } from '@nordvik/ui/dialog'
import { SimpleLabel } from '@nordvik/ui/simple-label'

import {
  GQLCmsChangelogsQuery,
  useCmsChangelogsQuery,
} from '@/api/generated-client'
import { ChangelogItemContent } from '@/app/(protected)/(sidebar)/oppdateringer/components/changelog-timeline'
import { renderModule } from '@/components/articles/article-content'
import { DATE_FORMAT } from '@/lib/constants'
import { formatDate } from '@/lib/dates'

import { OtherChangelogs } from './other-changelogs'

export function ChangelogDialogContent({
  changelog,
}: {
  changelog: GQLCmsChangelogsQuery['cmsChangelogs']['items'][number]
}) {
  const {
    data: otherChangelogs = [],
    isLoading,
    isPlaceholderData,
  } = useCmsChangelogsQuery(
    {
      limit: 4,
    },
    {
      select: (data) => {
        return data.cmsChangelogs.items.filter(
          (extraChangelog) => extraChangelog.id !== changelog.id,
        )
      },
      placeholderData: placeholderChangelogs,
    },
  )

  const category = changelog.categories.at(0)
  return (
    <div>
      {changelog.image?.medium && (
        <div className="grid grid-cols-1 grid-rows-1 w-full h-full">
          <img
            src={changelog.image.medium}
            alt={changelog.title || ''}
            className="w-full h-auto object-cover row-start-1 row-end-2 col-start-1 col-end-2"
          />
          <img
            src={changelog.image.large}
            alt={''}
            aria-hidden
            className="w-full h-auto object-cover row-start-1 row-end-2 col-start-1 col-end-2"
          />
        </div>
      )}
      <DialogDescription className="pt-4">
        <div className="flex items-center justify-start gap-2 mb-3">
          {changelog.postDate && (
            <p className="ink-muted typo-body-sm">
              {formatDate(changelog.postDate, DATE_FORMAT)}
            </p>
          )}
          {category && (
            <SimpleLabel size="sm" color="outline">
              {category?.title}
            </SimpleLabel>
          )}
        </div>

        <DialogTitle className="typo-title-sm mb-1 text-pretty">
          {changelog.title}
        </DialogTitle>
        <ChangelogItemContent>
          {changelog.modules?.map((entry, index) => (
            <React.Fragment key={entry?.body || index}>
              {renderModule(entry)}
            </React.Fragment>
          ))}
        </ChangelogItemContent>

        {otherChangelogs.length > 0 ? (
          <OtherChangelogs
            changelogs={otherChangelogs}
            loading={isLoading || isPlaceholderData}
          />
        ) : null}
      </DialogDescription>
    </div>
  )
}

const placeholderChangelogs: GQLCmsChangelogsQuery = {
  cmsChangelogs: {
    meta: {
      total: 4,
      currentPage: 1,
      totalPages: 1,
    },
    items: [
      {
        id: '245274',
        slug: 'test-test',
        title: 'Ny toppliste for Storebrand-leads',
        categories: [],
        targetRoles: [],
      },
      {
        id: '245215',
        slug: 'add-custom-contacts-now',
        title: 'Ferdigstill den nye meglerprofilen din under profil',
        postDate: '2025-04-22T14:36:00+02:00',

        categories: [],
        targetRoles: [],
      },
      {
        id: '245034',
        slug: 'next-versjon-20-1',
        title: 'Next versjon 21.1',
        postDate: '2025-04-21T20:10:00+02:00',

        categories: [],
        targetRoles: [],
      },
      {
        id: '245233',
        slug: 'next-versjon-20-1-2',
        title: 'Next versjon 20.1',
        postDate: '2025-04-19T20:10:00+02:00',

        categories: [
          {
            id: '245039',
            slug: 'next',
            title: 'Next',
          },
        ],
        targetRoles: [],
      },
    ],
  },
  readArticles: [],
}
