import { DialogClose } from '@radix-ui/react-dialog'
import { ArrowRightIcon, ChevronRightIcon } from 'lucide-react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'
import { TextButton } from '@nordvik/ui/text-button'

import { GQLCmsChangelogsQuery } from '@/api/generated-client'

export function OtherChangelogs({
  changelogs,
  loading,
}: {
  changelogs: GQLCmsChangelogsQuery['cmsChangelogs']['items']
  loading: boolean
}) {
  return (
    <div className={cn('mt-12 pb-2')}>
      <p className="ink-subtle typo-detail-md mb-1"><PERSON>ste oppdateringer</p>
      <ul className="flex flex-col">
        {changelogs.slice(0, 3).map((changelog) => (
          <li
            key={changelog.id}
            aria-busy={loading}
            className="border-b border-stroke-muted"
          >
            <DialogClose asChild>
              <TextButton
                subtle
                bold={false}
                href={loading ? '#' : `/oppdateringer#${changelog.slug}`}
                className="group/changelog-link flex items-center py-2.5"
              >
                <div
                  className={cn(
                    'size-1 bg-ink rounded-full mr-3',
                    loading && 'opacity-0',
                  )}
                />
                <span
                  className={cn(
                    loading &&
                      'masked-placeholder-text !bg-fill-brand-emphasis',
                  )}
                >
                  {changelog.title}
                </span>
                <ChevronRightIcon className="size-4 ink-muted ml-auto hover-supported:opacity-0 group-hover/changelog-link:opacity-100 transition-opacity" />
              </TextButton>
            </DialogClose>
          </li>
        ))}
      </ul>
      <DialogClose asChild>
        <Button
          variant="outline"
          size="sm"
          className="inline-flex w-auto mt-6"
          iconEnd={<ArrowRightIcon />}
          href="/oppdateringer"
        >
          Se alle oppdateringer
        </Button>
      </DialogClose>
    </div>
  )
}
