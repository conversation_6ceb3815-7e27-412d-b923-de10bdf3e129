'use client'

import { isAfter, startOfToday, subDays } from 'date-fns'
import { AnimatePresence, motion } from 'framer-motion'
import { sortBy } from 'lodash'
import { XIcon } from 'lucide-react'
import React, { useCallback, useMemo, useState, useTransition } from 'react'

import { Button } from '@nordvik/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@nordvik/ui/dialog'

import {
  GQLCmsChangelogsQuery,
  useCmsChangelogsQuery,
  useMarkNewsAsReadMutation,
} from '@/api/generated-client'
import { useLocalStorage } from '@/hooks/use-local-storage'
import { useTrackEvent } from '@/lib/analytics/track-event'

import { ChangelogDialogContent } from './changelog-dialog-content'

export function ChangelogCard({ fallback }: { fallback?: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const [, startTransition] = useTransition()
  const trackEvent = useTrackEvent()
  const { getValues, addValue } = useLocalStorage<string[]>()
  const dateFrom = useMemo(() => subDays(startOfToday(), 14).toISOString(), [])
  const { data } = useCmsChangelogsQuery({ dateFrom }, { enabled: !open })
  const { mutateAsync: markNewsAsRead } = useMarkNewsAsReadMutation()

  const changelog = useMemo(() => {
    return getRecentChangelog(
      data?.cmsChangelogs?.items,
      data?.readArticles,
      getValues('dismissed-changelogs'),
    )
  }, [data, getValues])

  React.useEffect(() => {
    if (changelog) {
      markNewsAsRead({ newsId: changelog.id })
      if (open) {
        addValue('dismissed-changelogs', changelog.id)
      }
    }
  }, [changelog, markNewsAsRead, addValue, open])

  const handleDismiss = useCallback(() => {
    if (!changelog) return
    startTransition(async () => {
      addValue('dismissed-changelogs', changelog.id)
      setIsDismissed(true)
    })
  }, [changelog, addValue])

  const visible = !isDismissed && changelog

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          handleDismiss()
        } else {
          trackEvent('changelog_card_click', {
            slug: changelog?.slug,
            title: changelog?.title,
          })
        }
        setIsDismissed(false)
        setOpen(open)
      }}
    >
      <AnimatePresence mode="wait" initial={false}>
        {!visible && (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={{
              hidden: { opacity: 0, scaleX: 0 },
              visible: { opacity: 1, scaleX: 1 },
            }}
            transition={{ type: 'spring', damping: 30, stiffness: 150 }}
          >
            {fallback}
          </motion.div>
        )}
        {visible && (
          <motion.div
            key={changelog.slug + changelog.id}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={{
              hidden: { opacity: 0, y: 8 },
              visible: { opacity: 1, y: 0 },
              exit: {
                opacity: 0,
                scale: 0.98,
                transition: { type: 'spring', stiffness: 400, damping: 40 },
              },
            }}
            transition={{
              type: 'spring',
              damping: 30,
              stiffness: 200,
            }}
            className="cursor-pointer mt-6 mb-2 mx-3"
          >
            <DialogTrigger asChild>
              <div
                role="button"
                className="group/changelog bg-root rounded-md w-full overflow-hidden relative text-left"
              >
                <CardImage image={changelog.image} title={changelog.title} />
                <Button
                  variant="unstyled"
                  size="sm"
                  className="absolute top-0 right-0 ink-subtle opacity-0 group-hover/changelog:opacity-100 transition-opacity hover:ink-default"
                  iconOnly={<XIcon />}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    trackEvent('changelog_card_dismiss', {
                      slug: changelog.slug,
                      title: changelog.title,
                    })
                    handleDismiss()
                  }}
                />

                <div className="p-3 space-y-1">
                  <p className="typo-detail-sm ink-muted">Oppdatering</p>
                  <p className="typo-body-sm leading-[1.34]">
                    {changelog.title}
                  </p>
                </div>
              </div>
            </DialogTrigger>
          </motion.div>
        )}
      </AnimatePresence>
      {changelog && (
        <DialogContent data-theme="dark" title={false}>
          <ChangelogDialogContent changelog={changelog} />
        </DialogContent>
      )}
    </Dialog>
  )
}

function CardImage({
  image,
  title,
}: {
  image: GQLCmsChangelogsQuery['cmsChangelogs']['items'][0]['image']
  title?: string
}) {
  if (!image?.medium) {
    return null
  }

  return (
    <div className="w-full">
      <img
        src={image?.medium ?? ''}
        alt={title ?? ''}
        className="object-cover size-full object-center"
      />
    </div>
  )
}

function getRecentChangelog(
  changelogs?: GQLCmsChangelogsQuery['cmsChangelogs']['items'],
  viewedArticles?: GQLCmsChangelogsQuery['readArticles'],
  dismissedChangelogs?: string[],
) {
  if (!changelogs) return undefined

  const filteredChangelogs = changelogs.filter((changelog) => {
    if (dismissedChangelogs?.includes(changelog.id)) {
      return false
    }

    const viewedArticle = viewedArticles?.find(
      (article) => article.id === changelog.id,
    )

    // If article has not been shown in sidebar within last 24 hours, show it
    if (!viewedArticle) {
      return true
    }

    // If article has been shown in sidebar within last 24 hours
    const readAt = new Date(viewedArticle.readAt)

    return isAfter(readAt, subDays(new Date(), 1))
  })

  return sortBy(filteredChangelogs, 'postDate').at(0)
}
