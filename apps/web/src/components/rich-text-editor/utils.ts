import { marked } from 'marked'
import type { Delta } from 'quill'

import { useQuill } from './use-quill.hook'

export function isMarkdown(text: string): boolean {
  const markdownPatterns = [
    /^#{1,6}\s/, // Headings (#, ##, ###, etc.)
    /^(\*|-|\+)\s+/, // Unordered list (- item, * item, + item)
    /^\d+\.\s+/, // Ordered list (1. item, 2. item)
    /\*\*(.*?)\*\*/, // Bold (**bold**)
    /\*(.*?)\*/, // Italic (*italic*)
    /`{1,3}.*?`{1,3}/, // Inline or block code (`code`, ```code```)
    /\[.+?\]\(.+?\)/, // Links ([text](url))
    /^>\s+/, // Blockquotes (> quote)
  ]

  // Count how many lines match Markdown patterns
  const lines = text.split('\n')
  let matchCount = 0

  for (const line of lines) {
    if (markdownPatterns.some((pattern) => pattern.test(line.trim()))) {
      matchCount++
    }
  }

  // If at least 30% of lines contain Markdown syntax, assume it's Markdown
  return matchCount / lines.length > 0.3
}
/**
 * Converts HTML to a Delta object
 * @param html - The HTML to convert
 * @param quillInstance - The Quill instance to use - should be passed from useQuill hook
 * @returns The Delta object or null if the window is undefined
 */
export const convertHtmlToDelta = (
  html: string,
  Quill: ReturnType<typeof useQuill>['Quill'],
): Delta | null => {
  if (typeof window === 'undefined' || !Quill) return null

  const quill = new Quill(document.createElement('div'))
  // Pre-process HTML to handle various line break scenarios
  const processedHtml = html // Add <p> tags around <br> tags that aren't already within <p> tags
    .replace(/(?<!<p[^>]*>)(?:<br\s*\/?>)(?![^<]*<\/p>)/gi, '<p><br></p>')

  quill.clipboard.dangerouslyPasteHTML(processedHtml)
  return quill.getContents()
}

/**
 * Converts a Delta object to HTML
 * @param delta - The Delta object to convert
 * @param quillInstance - The Quill instance to use - should be passed from useQuill hook
 * @returns The HTML string or an empty string if the window is undefined
 */
export const convertDeltaToHtml = (
  delta: Delta['ops'],
  Quill: ReturnType<typeof useQuill>['Quill'],
): string => {
  if (typeof window === 'undefined' || !Quill) return ''

  const quill = new Quill(document.createElement('div'))

  quill.setContents(delta)
  const html = quill.getSemanticHTML()

  if (html === '<p><br></p>' || html === '<p></p>') return ''

  return html
}

/**
 * Converts Markdown to a Delta object
 * @param markdown - The Markdown to convert
 * @param quillInstance - The Quill instance to use - should be passed from useQuill hook
 * @returns The Delta object or an empty array if the window is undefined
 */
export const convertMarkdownToDelta = async (
  markdown: string,
  Quill: ReturnType<typeof useQuill>['Quill'],
) => {
  if (typeof window === 'undefined' || !Quill) return null

  const quill = new Quill(document.createElement('div'))
  let html = await marked(markdown)

  // Ensure extra spacing between block elements, but not between list items
  html = html.replace(
    /<\/(h\d|p|blockquote|pre|ul|ol|table)>(?![^<]*<\/?(?:li))/g,
    '$&<br>',
  )

  // // replace all heading tags with h2 :(
  html = html.replace(/<\/?h[1,3-6]>/g, (match) => {
    return match.startsWith('</') ? '</h2>' : '<h2>'
  })

  return quill.clipboard.convert({ html })
}
