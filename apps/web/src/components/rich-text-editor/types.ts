import { QuillOptions } from 'quill'

import { TemplateReplacements } from '../send-offer-dialog/types'
import { Template } from '../template-renderer/types'

export interface RichTextEditorProps {
  value: Template<TemplateReplacements>['emailContent'][number]['value'] | null
  onChange: (
    value: Template<TemplateReplacements>['emailContent'][number]['value'],
  ) => void
  placeholder?: string
  className?: string
  isDisabled?: boolean
  formats?: QuillOptions['formats']
  autoFocus?: boolean
}
