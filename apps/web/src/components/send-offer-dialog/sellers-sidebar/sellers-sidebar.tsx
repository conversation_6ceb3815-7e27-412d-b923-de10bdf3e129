import EditSellerDialog from '@befaring/components/seller-section/edit-seller-dialog'
import { useUpdateSeller } from '@befaring/hooks/use-update-seller'
import { motion } from 'framer-motion'
import { AlertTriangle, ChevronRight, PenIcon } from 'lucide-react'
import React, { useEffect, useMemo, useState } from 'react'
import * as yup from 'yup'

import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import { Tooltip, TooltipContent, TooltipTrigger } from '@nordvik/ui/tooltip'

import { NextPrivateContactWithProxy } from '@/actions/next/types-next'
import { joinNames } from '@/lib/join-names'
import { isNotNull } from '@/server/utils'

import { useCompanySigners } from '../company-signers.hook'
import { useOfferContext } from '../offer-context'
import { OfferContact } from '../types'

import { AddContact } from './add-contact'
import { EditContact } from './edit-contact'
import {
  getOtherContactsTitle,
  getProxyFor,
  getRelationName,
  groupContacts,
} from './utils'

export function SellersSidebar() {
  const { sellers, selectedContacts, estate, setOpen } = useOfferContext()
  const isCompany = estate.hasCompanySeller
  const company = isCompany ? estate.sellers[0] : null
  const { sellersOnly, proxiesOnly, spousesOnly } = groupContacts(
    isCompany ? estate.companyContacts : sellers,
    { uniqueOnly: true, isCompany },
  )
  const { rightsHolders } = useCompanySigners(company ?? null, estate.estateId)
  const extraContacts = estate.extraContacts.filter(
    (contact) => !contact.deletedAt,
  )

  const otherContacts = [...proxiesOnly, ...spousesOnly]

  const getDisabled = (contactId?: string) => {
    if (!contactId) return true
    return selectedContacts.length === 1 && selectedContacts.includes(contactId)
  }

  const companyRedirect = useMemo(() => {
    if (!company) return '#'

    if (estate.isValuation) {
      return `/verdivurdering/${estate.estateId}#oppdragsgiver`
    }
    return `/oppdragsavtale/${estate.estateId}/parter#oppdragsgiver`
  }, [company, estate.estateId, estate.isValuation])

  return (
    <div className="not-last:border-b border-muted space-y-4 not-last:pb-4">
      {isCompany && (
        <div className="space-y-3">
          <p className="typo-detail-md ink-subtle">Selger</p>

          {company?.contactId ? <CompanyItem company={company} /> : null}
          <p className="typo-detail-md ink-subtle">
            {sellers.length > 1 ? 'Mottakere' : 'Mottaker'}
          </p>
          <div className="space-y-2">
            {sellersOnly?.map((seller) => (
              <SellerItem
                disabled={getDisabled(seller.contactId)}
                key={seller.contactId}
                seller={seller}
                relationName={seller.roleName}
                isCompanySigner={
                  isCompany
                    ? rightsHolders?.some((right) => right === seller.contactId)
                    : undefined
                }
              />
            ))}
          </div>

          <Button
            variant="outline"
            iconEnd={<ChevronRight className="size-3" />}
            className="rounded-md typo-body-xs font-normal whitespace-pre-wrap text-left p-3 h-auto ink-subtle"
            href={companyRedirect}
            onClick={() => setOpen(false)}
            size="lg"
          >
            Rediger hvem som skal signere i oppdragsavtalen
          </Button>
        </div>
      )}
      {!isCompany && (
        <div className="space-y-3">
          <p className="typo-detail-md ink-subtle">
            {sellers.length > 1 ? 'Selgere' : 'Selger'}
          </p>
          <div className="space-y-2">
            {sellersOnly?.map((seller) => (
              <SellerItem
                disabled={getDisabled(seller.contactId)}
                key={seller.contactId}
                seller={seller}
                relationName={seller.roleName}
                isCompanySigner={
                  isCompany
                    ? rightsHolders?.some((right) => right === seller.contactId)
                    : undefined
                }
              />
            ))}
          </div>
        </div>
      )}
      {(otherContacts.length > 0 || extraContacts.length > 0) && (
        <div className="space-y-3 pt-3">
          <p className="typo-detail-md ink-subtle">
            {isCompany
              ? 'Andre mottakere'
              : getOtherContactsTitle([...otherContacts, ...extraContacts])}
          </p>
          <div className="space-y-2">
            {otherContacts.map((contact) =>
              contact.proxy ? (
                <SellerItem
                  key={contact.proxy.contactId}
                  seller={contact.proxy}
                  proxyFor={getProxyFor(contact, sellersOnly)}
                  disabled={getDisabled(contact.proxy.contactId)}
                />
              ) : (
                <SellerItem
                  key={contact.contactId}
                  seller={contact}
                  relationName={getRelationName(contact.relationType)}
                  disabled={getDisabled(contact.contactId)}
                />
              ),
            )}
            {extraContacts.map((contact) => (
              <SellerItem
                key={contact.contactId}
                seller={contact}
                disabled={getDisabled(contact.contactId)}
                relationName={contact.relationName}
                isEditable
              />
            ))}
          </div>
        </div>
      )}

      <div className="not-last:border-b border-muted pb-4">
        <AddContact />
      </div>
    </div>
  )
}

function CompanyItem({
  company,
}: {
  company: Pick<NextPrivateContactWithProxy, 'firstName' | 'lastName'>
}) {
  return (
    <div className="flex items-center gap-2">
      <div className="grow">
        <p className="typo-body-md">
          {company.firstName} {company.lastName}
        </p>
      </div>
    </div>
  )
}

const sellerSchema = yup.object({
  email: yup.string().email('e-post').required('e-post'),
  mobilePhone: yup
    .string()
    .required('mobilnummer')
    .matches(/^[0-9]+$/, 'mobilnummer'),
})

function SellerItem({
  seller,
  proxyFor,
  disabled,
  relationName,
  isEditable,
  isCompanySigner,
}: {
  seller: OfferContact
  proxyFor?: string
  disabled?: boolean
  relationName?: string | null
  onRemove?: () => Promise<void>
  isEditable?: boolean
  isCompanySigner?: boolean
}) {
  const [isOpen, setIsOpen] = useState(false)
  const { selectedContacts, setSelectedContacts } = useOfferContext()
  const [validationErrors, setValidationErrors] = useState<
    yup.ValidationError[]
  >([])

  useEffect(() => {
    try {
      sellerSchema.validateSync(
        {
          email: seller.email,
          mobilePhone: seller.mobilePhone?.replace(/\s+/g, ''),
        },
        {
          abortEarly: false,
          context: { returnErrors: true },
        },
      )

      setValidationErrors([])
    } catch (errors) {
      setValidationErrors(errors.inner)
    }
  }, [seller])

  useEffect(() => {
    if (validationErrors.length === 2) {
      setSelectedContacts((prev) =>
        prev.filter((id) => id !== seller.contactId),
      )
    }
  }, [validationErrors, seller.contactId, setSelectedContacts])

  const hasPhoneNumber = !validationErrors.find(
    (error) => error.path === 'mobilePhone',
  )
  const hasEmail = !validationErrors.find((error) => error.path === 'email')

  if (!seller.contactId) return null

  return (
    <>
      <div
        className="flex gap-2"
        key={seller.contactId}
        aria-disabled={disabled}
      >
        <label className="mt-[1px]">
          <Checkbox
            disabled={disabled || (!hasPhoneNumber && !hasEmail)}
            id={seller.contactId}
            checked={selectedContacts.includes(seller.contactId)}
            onCheckedChange={(checked) => {
              setSelectedContacts(
                checked
                  ? [...selectedContacts, seller.contactId].filter(isNotNull)
                  : selectedContacts.filter((id) => id !== seller.contactId),
              )
            }}
          />
        </label>
        <div className="grow">
          <label
            htmlFor={seller.contactId}
            className="typo-body-md w-auto data-[disabled=false]:cursor-pointer flex items-center justify-between"
            data-disabled={disabled}
          >
            <p>
              {seller.firstName} {seller.lastName}
            </p>
            {typeof isCompanySigner === 'boolean' ? (
              isCompanySigner ? null : (
                <Tooltip delayDuration={150}>
                  <TooltipTrigger asChild>
                    <AlertTriangle className="mr-2 size-3 ink-muted" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Ikke valgt for signering</p>
                  </TooltipContent>
                </Tooltip>
              )
            ) : null}
          </label>
          {proxyFor && (
            <p className="typo-body-xs ink-muted">Fullmektig for {proxyFor}</p>
          )}
          {relationName && (
            <p className="typo-body-xs ink-muted">{relationName}</p>
          )}
          {validationErrors?.length > 0 && (
            <p className="typo-body-xs ink-muted">
              Mangler{' '}
              {joinNames(validationErrors.map((error) => error.message))}
            </p>
          )}
        </div>
        {validationErrors?.length > 0 && <SellerEdit seller={seller} />}
        {isEditable && (
          <motion.div
            key="add-new-custom-contact-button"
            initial={{ opacity: 1, height: 'auto' }}
            animate={{ opacity: isOpen ? 0 : 1, height: isOpen ? 0 : 'auto' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            <Button
              onClick={() => setIsOpen(true)}
              iconOnly={<PenIcon />}
              size="sm"
              className="opacity-50 hover:opacity-100"
              variant="ghost"
            >
              Rediger
            </Button>
          </motion.div>
        )}
      </div>
      {isEditable && (
        <EditContact
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          contact={{
            email: seller.email,
            firstName: seller.firstName,
            lastName: seller.lastName,
            mobilePhone: seller.mobilePhone,
            contactId: seller.contactId,
          }}
        />
      )}
    </>
  )
}

function SellerEdit({ seller }: { seller: OfferContact }) {
  const { handleSubmit } = useUpdateSeller(seller)

  if (seller.contactType === 0) {
    return (
      <EditSellerDialog
        seller={seller}
        handleSubmit={handleSubmit}
        buttonComponent={
          <Button
            variant="ghost"
            size="sm"
            className="opacity-50 hover:opacity-100"
            iconOnly={<PenIcon />}
          >
            Rediger
          </Button>
        }
      />
    )
  }

  return null
}
