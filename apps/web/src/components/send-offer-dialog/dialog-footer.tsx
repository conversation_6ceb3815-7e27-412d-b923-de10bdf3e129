import { UploadIcon } from 'lucide-react'

import { Button } from '@nordvik/ui/button'
import { useToast } from '@nordvik/ui/toaster'

import { useOfferContext } from './offer-context'
import {
  pagesOrderWithOptions,
  pagesOrderWithoutOptions,
  validateDraft,
} from './utils'

export function DialogFooter() {
  const { setTab, tab, setOpen, estate } = useOfferContext()
  const pagesOrder = estate.isValuation
    ? pagesOrderWithoutOptions
    : pagesOrderWithOptions

  const currentIndex = pagesOrder.indexOf(tab)

  return (
    <div className="grid-in-[footer] bg-root">
      <div className="col-start-2 row-start-2 border-t border-muted px-6 py-4 space-y-2  flex max-sm:flex-col sm:items-center gap-2">
        <div className="flex gap-2 items-center max-md:w-full ml-auto">
          {currentIndex === 0 && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setOpen(false)}
              size="lg"
            >
              Avbryt
            </Button>
          )}
          {currentIndex > 0 && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setTab(pagesOrder[currentIndex - 1])}
              size="lg"
            >
              Tilbake
            </Button>
          )}
          {currentIndex < pagesOrder.length - 1 ? (
            <Button
              className="w-full"
              onClick={() => setTab(pagesOrder[currentIndex + 1])}
              size="lg"
            >
              Neste
            </Button>
          ) : (
            <Send />
          )}
        </div>
      </div>
    </div>
  )
}

function Send() {
  const {
    isSendingOffer,
    handleSendSignLink,
    draft,
    dirty,
    canSendOffer,
    selectedChannels,
    setIsSendingOffer,
  } = useOfferContext()
  const { toast } = useToast()
  return (
    <Button
      loading={isSendingOffer}
      disabled={isSendingOffer || !canSendOffer}
      className="w-full"
      tooltip={!canSendOffer ? 'Velg minst en selger' : undefined}
      iconStart={<UploadIcon />}
      onClick={(e) => {
        if (!validateDraft(draft, toast, selectedChannels)) {
          e.preventDefault()
          setIsSendingOffer(false)
          return
        }
        handleSendSignLink({ draft, dirty })
      }}
      size="lg"
    >
      Del med kunde
    </Button>
  )
}
