import { XIcon } from 'lucide-react'

import { Button } from '@nordvik/ui/button'

import {
  TemplateTabs,
  pagesOrderWithOptions,
  pagesOrderWithoutOptions,
} from './utils'

export function PageTitle({
  tab,
  setOpen,
  isValuation,
}: {
  tab: TemplateTabs
  setOpen?: (open: boolean) => void
  isValuation?: boolean
}) {
  const pagesOrder = !isValuation
    ? pagesOrderWithOptions
    : pagesOrderWithoutOptions
  const currentIndex = pagesOrder.indexOf(tab)

  const tabTitle = {
    [TemplateTabs.EMAIL]: 'Tilpass innholdet i e-posten',
    [TemplateTabs.OPTIONS]: 'Hva vil du dele?',
    [TemplateTabs.SMS]: 'Tilpass tekst i sms',
  } satisfies Record<TemplateTabs, string>

  return (
    <div className="flex justify-between items-baseline">
      <h3 className="typo-title-sm">{tabTitle[tab]}</h3>

      <div className="flex items-center gap-4">
        <span className="typo-body-xs ink-muted">
          {currentIndex + 1} / {pagesOrder.length}
        </span>
        <Button
          variant="unstyled"
          className="max-md:hidden"
          iconOnly={<XIcon />}
          onClick={() => setOpen?.(false)}
          size="lg"
        />
      </div>
    </div>
  )
}
