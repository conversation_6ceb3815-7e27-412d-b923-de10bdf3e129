'use client'

import React from 'react'

import { cn } from '@nordvik/theme/cn'
import * as Dialog from '@nordvik/ui/dialog_DEPRECATED'

import { EditorsContent } from './editors-content'
import { useOfferContext } from './offer-context'

export function DialogContent() {
  const { draft } = useOfferContext()

  return (
    <Dialog.DialogContent
      closeOnClickOutside={false}
      className={cn('max-w-[1200px]')}
      withCloseButton={false}
      classNameScrollArea="sm:max-h-dvh"
      classNameWrapper="p-0"
    >
      <Dialog.DialogTitle className="sr-only">
        Send tilbud til selgere
      </Dialog.DialogTitle>
      <EditorsContent key={draft?.id} />
    </Dialog.DialogContent>
  )
}
