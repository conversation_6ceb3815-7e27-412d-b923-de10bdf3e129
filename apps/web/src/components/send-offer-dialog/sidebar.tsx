import { XIcon } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'
import { Button } from '@nordvik/ui/button'

import { useOfferContext } from './offer-context'
import { TemplateTabs } from './utils'

export function Sidebar({ children }: { children: React.ReactNode }) {
  const { setOpen, tab } = useOfferContext()

  const tabDescription = {
    [TemplateTabs.OPTIONS]: 'Velg mottakere og tilpass innhold som skal deles.',
    [TemplateTabs.EMAIL]:
      'Velg en mal og tilpass innholdet i e-posten til selger.',
    [TemplateTabs.SMS]: 'Tilpass teksten som sendes på sms til selger.',
  } satisfies Record<TemplateTabs, string>

  return (
    <div className={cn('grid-in-[selector]')} data-theme="dark">
      <div
        data-theme="dark"
        className="h-full bg-brand-muted flex flex-col overflow-y-auto"
      >
        <div className="p-4 pb-6 md:py-6 md:px-8 space-y-6 grow flex flex-col">
          <div className="space-y-4 md:space-y-2">
            <div className="flex items-baseline justify-between ">
              <h1 className="typo-title-sm">Del med kunde</h1>
              <div className="md:hidden flex items-center gap-4">
                <Button
                  iconOnly={<XIcon className="size-5" />}
                  variant="unstyled"
                  onClick={() => setOpen(false)}
                  size="lg"
                >
                  <span className="sr-only">Close</span>
                </Button>
              </div>
            </div>

            <p className="typo-body-md">{tabDescription[tab]}</p>
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
