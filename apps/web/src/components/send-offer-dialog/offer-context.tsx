import { useValidateForm } from '@befaring/hooks/useValidateForm'
import { BrokerRole } from '@befaring/lib/broker-constants'
import { optionalSellerContactSchema } from '@befaring/lib/check-valid-fields'
import { getBrokerByRole } from '@befaring/lib/format-brokers'
import { useTrackListingAgreement } from '@befaring/lib/track-listing-agreement'
import { validateMissingValues } from '@befaring/oppdragsavtale/[estateId]/(agreement)/components/utils'
import { useQueryClient } from '@tanstack/react-query'
import { uniq } from 'lodash'
import { useRouter } from 'next/navigation'
import React, { useContext } from 'react'

import { useToast } from '@nordvik/ui/toaster'

import { GQLGetBrokerEstateQuery } from '@/api/generated-client'
import { getLinkToOfferText } from '@/lib/get-link-to-offer-text'
import { isNotNull } from '@/server/utils'

import { useQuill } from '../rich-text-editor/use-quill.hook'
import { Template } from '../template-renderer/types'

import {
  Channels,
  OfferContextProps,
  OfferContextProviderProps,
  TemplateReplacements,
  TemplateTabs,
} from './types'
import { useTemplates } from './use-templates.hook'
import {
  getProxies,
  validateDraft,
  validateInputs,
  validateSellers,
} from './utils'

const OfferContext = React.createContext<OfferContextProps | undefined>(
  undefined,
)

export const OfferContextProvider = ({
  children,
  sellers = [],
  estate,
  isUpdatingInitialData,
  setOpen,
  listingAgreementBudget,
  sendOffer,
  offerSellerLink,
}: OfferContextProviderProps) => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const trackEvent = useTrackListingAgreement(
    estate as Partial<GQLGetBrokerEstateQuery['estate']>,
  )
  const { Quill } = useQuill()
  const { toast } = useToast()
  const { validateSync } = useValidateForm(
    estate,
    optionalSellerContactSchema,
    {
      fieldsTranslation: {
        mobilePhone: 'telefonnummer',
        email: 'e-post',
      },
    },
  )

  const isValuation = estate.isValuation
  const [tab, setTab] = React.useState<TemplateTabs>(TemplateTabs.EMAIL)
  const [selectedChannels, setSelectedChannels] = React.useState<{
    email: boolean
    sms: boolean
  }>({
    email: true,
    sms: true,
  })
  const [isSendingOffer, setIsSendingOffer] = React.useState(false)

  const proxies = getProxies(sellers)
  const sellersCID = sellers
    .map((seller) => {
      if (seller.contactType === 0) {
        return seller.proxy ? null : seller.contactId
      } else {
        return null
      }
    })
    .filter(isNotNull)

  const proxiesCID = proxies.map((proxy) => proxy.contactId)
  const extraContactsCID = React.useMemo(
    () =>
      estate?.extraContacts
        .map((contact) => (contact.deletedAt ? null : contact.contactId))
        .filter(isNotNull),
    [estate?.extraContacts],
  )
  const initialSelectedContacts = uniq([
    ...sellersCID,
    ...proxiesCID,
    ...extraContactsCID,
  ])
  const [selectedContacts, setSelectedContacts] = React.useState<string[]>(
    initialSelectedContacts,
  )

  const [includeListingAgreement, setIncludeListingAgreement] = React.useState(
    isValuation
      ? true
      : Boolean(estate.inspectionFolder?.listingAgreementActive),
  )
  const refExtraContacts = React.useRef<string[]>([])

  React.useEffect(() => {
    if (refExtraContacts.current.length === 0) {
      refExtraContacts.current = extraContactsCID
      return
    }

    // Find new contacts that were added
    const addedContacts = extraContactsCID.filter(
      (id) => !refExtraContacts.current.includes(id),
    )

    // Find contacts that were removed
    const removedContacts = refExtraContacts.current.filter(
      (id) => !extraContactsCID.includes(id),
    )

    setSelectedContacts((prev) => [
      ...prev.filter((id) => !removedContacts.includes(id)),
      ...addedContacts,
    ])

    refExtraContacts.current = extraContactsCID
  }, [extraContactsCID])
  const mainBroker = getBrokerByRole(BrokerRole.Main, {
    brokers: estate?.brokers,
    brokersIdWithRoles: estate?.brokersIdWithRoles,
  })

  const {
    draft,
    selectedTemplateId,
    handleSelectTemplate,
    handleOnChangeTemplateContent,
    dirty,
    signature,
    resetDraft,
    setDraft,
  } = useTemplates({
    estate,
    mainBroker,
    selectedContacts,
    sellers,
    setTab,
    tab,
  })

  const hasAllData = isValuation
    ? Boolean(estate && sellers)
    : Boolean(estate && sellers && listingAgreementBudget)
  const missingValues =
    estate && sellers && listingAgreementBudget && !isValuation
      ? validateMissingValues(listingAgreementBudget, sellers, estate)
      : []

  const canIncludeListingAgreement = missingValues.length === 0 && hasAllData

  function resetToInitialState() {
    resetDraft()
    setSelectedContacts(initialSelectedContacts)
    setIncludeListingAgreement(
      Boolean(estate.inspectionFolder?.listingAgreementActive),
    )
    setSelectedChannels({
      email: true,
      sms: true,
    })
  }

  async function handleSendSignLink({
    draft,
    dirty,
  }: {
    draft: Template<TemplateReplacements>
    dirty: boolean
  }) {
    setIsSendingOffer(true)
    const receivers = [...sellers, ...proxies, ...estate.extraContacts].filter(
      (contact) =>
        contact.contactId && selectedContacts.includes(contact.contactId),
    )

    if (!receivers || receivers.length === 0) {
      toast({
        title: 'Ingen mottakere',
        variant: 'destructive',
      })
      setIsSendingOffer(false)
      return
    }
    if (
      !validateInputs(receivers, estate, draft) ||
      !validateSellers(receivers ?? [], toast, validateSync) ||
      !validateDraft(draft, toast, selectedChannels)
    ) {
      setIsSendingOffer(false)
      return
    }
    try {
      await sendOffer({
        estate,
        draft,
        dirty,
        includeListingAgreement,
        receivers,
        trackEvent,
        channels: selectedChannels,
        Quill,
      })
      const pluralOrSingular = sellers.length > 1 ? 'selgerne' : 'selgeren'
      const title = isValuation
        ? `Verdivurdering${
            includeListingAgreement ? ' og tilbud' : ''
          } er sendt til ${pluralOrSingular}`
        : `Befaringsmappe${includeListingAgreement ? ' og oppdragsavtale' : ''} er delt med ${pluralOrSingular} på ${
            selectedChannels.sms ? 'e-post og SMS' : 'e-post'
          }.`
      toast({
        variant: 'success',
        title,
      })
      router.refresh()
      resetToInitialState()
      setOpen(false)
    } catch (error) {
      console.error(error)
      toast({
        title: `Noe gikk galt ved sending av presentasjonen og/eller tilbudet`,
        variant: 'destructive',
      })
    } finally {
      setIsSendingOffer(false)
    }
    queryClient.invalidateQueries({
      queryKey: ['estatesForBrokerById.infinite'],
    })
    router.refresh()
  }

  const handleOpen = (boolean: boolean) => {
    setOpen(boolean)
    if (!boolean) {
      resetDraft()
    }
  }

  const handleSelectChannel = (channel: Channels, value: boolean) => {
    setSelectedChannels((prev) => ({ ...prev, [channel]: value }))
  }

  return (
    <OfferContext.Provider
      value={{
        sellers: sellers ?? [],
        estate,
        canIncludeListingAgreement,
        isUpdatingInitialData,
        setOpen: handleOpen,
        linkToOffer: {
          url: offerSellerLink,
          text: getLinkToOfferText({
            agreementIncluded: includeListingAgreement,
            isValuation,
          }),
        },
        includeListingAgreement,
        setIncludeListingAgreement,
        selectedContacts,
        setSelectedContacts,
        handleSendSignLink,
        selectedTemplateId,
        tab,
        setTab,
        draft,
        setDraft,
        signature,
        handleSelectTemplate,
        handleOnChangeTemplateContent,
        dirty,
        isSendingOffer,
        setIsSendingOffer,

        canSendOffer: selectedContacts.length > 0,
        selectedChannels,
        handleSelectChannel,
      }}
    >
      {children}
    </OfferContext.Provider>
  )
}

export const useOfferContext = () => {
  const context = useContext(OfferContext)
  if (!context) {
    throw new Error('useOfferContext must be used within an OfferProvider')
  }
  return context
}
