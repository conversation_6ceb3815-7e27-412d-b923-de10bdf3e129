'use client'

import { ListingAgreementResponse } from '@befaring/actions/types'
import React, { useState } from 'react'

import { Button, ButtonProps } from '@nordvik/ui/button'
import * as Dialog from '@nordvik/ui/dialog_DEPRECATED'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import {
  GQLAgreementAndInspectionQuery,
  GQLGetBrokerEstateQuery,
} from '@/api/generated-client'
import { getLinkToObjectTabInNext } from '@/utils/get-link-to-object-tab-in-next'

import { DialogContent } from './dialog-content'
import { OfferContextProvider } from './offer-context'
import { sendOffer } from './send-offer-handlers'
import { OfferContact, Valuation } from './types'

export type SendOfferProps = {
  sellers?: OfferContact[]
  estate:
    | GQLAgreementAndInspectionQuery['estate']
    | GQLGetBrokerEstateQuery['estate']
  listingAgreementBudget?: ListingAgreementResponse | null
  triggerProps?: ButtonProps
  isUpdating?: boolean
  isLoading?: boolean
  triggerText?: string
  valuation?: Valuation
}

export function SendOfferDialog({
  sellers,
  estate,
  listingAgreementBudget,
  triggerProps,
  isUpdating,
  isLoading,
  triggerText,
}: SendOfferProps) {
  const [open, setOpen] = useState(false)
  const offerSellerLink = estate?.listingAgreement?.offerSellerLink
  if (!estate) {
    return null
  }

  const handleSetOpenModal = (boolean: boolean) => {
    setOpen(boolean)
    if (!boolean) {
      Object.keys(sessionStorage).forEach((key) => {
        if (key.startsWith(`eid-${estate.estateId}`)) {
          sessionStorage.removeItem(key)
        }
      })
    }
  }

  return (
    <Dialog.Dialog
      open={open}
      onOpenChange={handleSetOpenModal}
      key={estate.estateId + open.toString()}
    >
      <TriggerButton
        triggerProps={triggerProps}
        triggerText={triggerText}
        sellersLength={sellers?.length}
        estate={estate}
      />
      <OfferContextProvider
        sellers={estate.hasCompanySeller ? estate.companyContacts : sellers}
        estate={estate}
        listingAgreementBudget={listingAgreementBudget}
        isUpdatingInitialData={isLoading || isUpdating}
        setOpen={handleSetOpenModal}
        sendOffer={sendOffer}
        offerSellerLink={offerSellerLink}
      >
        <DialogContent />
      </OfferContextProvider>
    </Dialog.Dialog>
  )
}

function TriggerButton({
  triggerProps,
  triggerText,
  sellersLength,
  estate,
}: {
  triggerProps?: ButtonProps
  triggerText?: string
  sellersLength?: number
  estate:
    | GQLAgreementAndInspectionQuery['estate']
    | GQLGetBrokerEstateQuery['estate']
}) {
  const { toast } = useToast()

  const handleOnClick = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    if (!sellersLength && !estate?.hasCompanySeller) {
      e.preventDefault()
      return toast({
        title: 'Mangler selgerne i oppdragsavtalen',
        description: (
          <div>
            <p>Du må legge til minst en selger i oppdragsavtalen</p>
            {estate?.linkToNext && (
              <TextButton
                href={getLinkToObjectTabInNext(
                  estate.linkToNext,
                  'relatedparties',
                )}
                target="_blank"
                iconEnd="external"
              >
                Gå til Next for å legge til.
              </TextButton>
            )}
          </div>
        ),
        variant: 'destructive',
      })
    }
  }
  const agreementHasBeenIncluded =
    estate?.inspectionFolder?.listingAgreementActive
  return (
    <Dialog.DialogTrigger asChild>
      <Button
        variant="outline"
        onClick={handleOnClick}
        tooltip={`Del befaringsmappen ${
          agreementHasBeenIncluded
            ? 'med oppdragsavtalen'
            : 'eller uten oppdragsavtalen'
        }.`}
        size="lg"
        {...triggerProps}
      >
        {triggerText || 'Del med kunde'}
      </Button>
    </Dialog.DialogTrigger>
  )
}
