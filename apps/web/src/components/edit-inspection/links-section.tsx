import { LinkIcon, PlusIcon, XIcon } from 'lucide-react'
import { useState } from 'react'
import * as yup from 'yup'

import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import { OptionsPageSection } from '../send-offer-dialog/options-page/options-page-section'

const validateLink = (link: string, links: string[]) => {
  let error: null | string = null
  try {
    yup.string().url().required().validateSync(link)
    error = null
  } catch {
    error = 'Lim inn lenken til den aktuelle annonsen'
  }

  if (links.some((l) => l === link)) {
    error = 'Lenken er allerede lagt til'
  }
  return error
}

export function LinksSection({
  links,
  handleAddLink,
  handleRemoveLink,
  loading,
  updating,
  forceOpen,
  isExpandable,
  hideIcon,
}: {
  links: string[]
  handleAddLink: (link: string) => Promise<void>
  handleRemoveLink: (link: string) => Promise<void>
  loading: boolean
  updating: boolean
  forceOpen?: boolean
  isExpandable?: boolean
  hideIcon?: boolean
}) {
  const { toast } = useToast()
  const [link, setLink] = useState('')
  const [error, setError] = useState<string | null>(null)
  const onAddLink = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const valdationError = validateLink(link, links)
    if (valdationError) {
      setError(valdationError)
      return
    }

    try {
      await handleAddLink(link)
      setLink('')
    } catch (error) {
      toast({
        title: 'Kunne ikke legge til lenke',
        description: 'Prøv igjen senere',
      })
    }
  }
  return (
    <OptionsPageSection
      title="Relevante boligannonser"
      description="Legg til annonser som er relevante for selgeren (lik størrelse, prisantydning eller målgruppe)."
      Icon={hideIcon ? null : LinkIcon}
      isLoading={updating}
      forceOpen={forceOpen}
      isExpandable={isExpandable}
      itemsCount={links.length}
      className="pb-6"
    >
      <div>
        <div className="space-y-4 mb-4 pt-2 empty:hidden">
          {links?.map((link, index) => (
            <div
              key={link + index}
              data-loading={loading}
              className="group/link flex items-center justify-between gap-2 pl-3 rounded-md bg-inputs-fill-disabled data-[loading=true]:masked-placeholder-text data-[loading=true]:cursor-default overflow-hidden ink-disabled"
            >
              <a
                href={link}
                target="_blank"
                className="typo-body-md ink-subtle py-2.5 truncate hover:ink-default hover:underline hover:decoration-stroke-subtle underline-offset-2"
              >
                {link}
              </a>
              <Button
                variant="unstyled"
                onClick={() => handleRemoveLink(link)}
                iconOnly={<XIcon />}
                className="group-hover/link:ink-default ink-disabled"
                size="lg"
              />
            </div>
          ))}
        </div>

        <form onSubmit={onAddLink} className="first:pt-2">
          <Input
            placeholder="F.eks. http://finn.no/123..."
            value={link}
            onChange={(e) => {
              setError(null)
              setLink(e.target.value)
            }}
            errorMessage={error}
          />

          <TextButton
            type="submit"
            bold
            size="lg"
            className="mt-4"
            iconStart={<PlusIcon />}
          >
            Legg til
          </TextButton>
        </form>
      </div>
    </OptionsPageSection>
  )
}
