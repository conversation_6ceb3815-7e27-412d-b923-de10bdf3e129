'use client'

import React from 'react'

import { Button } from '@nordvik/ui/button'

import { TrackEventOnce } from '@/lib/analytics/track-event'

import { EmptyState } from './empty-state'
import { FeedbackButton } from './feedback-button'

function LowerCaseFirstLetterIfWordIsNotAnAcronym(input: string) {
  return input.charAt(1) === input.charAt(1)?.toUpperCase()
    ? input
    : input.charAt(0).toLowerCase() + input.slice(1)
}

export function NotFoundProtected({
  title = 'Siden du leter etter finnes ikke',
  parentRoute = '/nyheter',
  parentLabel = 'Nyheter',
}: {
  title?: string
} & (
  | {
      parentRoute: string
      parentLabel: string
    }
  | {
      parentRoute?: never
      parentLabel?: never
    }
)) {
  const formattedParentLabel =
    LowerCaseFirstLetterIfWordIsNotAnAcronym(parentLabel)

  return (
    <div className="flex justify-center pt-32 w-full">
      <TrackEventOnce event="not_found" />
      <EmptyState
        illustration="not-found"
        title={title}
        description={`Du vil kanskje tilbake til ${formattedParentLabel}. Hvis du tror noe er galt, kan du sende oss en melding.`}
        actions={
          <>
            <FeedbackButton size="lg" />
            <Button href={parentRoute} replace size="lg">
              Gå til {formattedParentLabel}
            </Button>
          </>
        }
      />
    </div>
  )
}
