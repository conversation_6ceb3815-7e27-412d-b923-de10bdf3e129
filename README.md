# Nordvik Megler

A modern, full-stack real estate broker application built for Nordvik Bolig. This application provides brokers with comprehensive tools for property management, client communication, document signing, and sales processes.

## 📋 Table of Contents

### 🚀 Quick Start

- [Getting Started](#-getting-started) - Installation and setup
- [Environment Variables](#-environment-variables) - Required configuration
- [VS Code Configuration](#-vs-code-configuration) - Editor setup

### 🏗️ Architecture & Development

- [Architecture & Tech Stack](#️-architecture--tech-stack) - Technologies used
- [Project Structure](#-project-structure) - Code organization
- [Development Workflow](#️-development-workflow) - Scripts and commands
- [Package Management](#-package-management) - Monorepo structure

### 🧪 Testing & Quality

- [Testing & Quality Assurance](#-testing--quality-assurance) - Testing tools
- [CI/CD Pipeline](#-cicd-pipeline) - Automated workflows
- [Troubleshooting](#-troubleshooting) - Common issues and solutions

### 🎨 Features & Integration

- [Authentication & Security](#-authentication--security) - Login and security
- [Design System](#-design-system) - UI components and styling
- [Analytics & Monitoring](#-analytics--monitoring) - PostHog and error tracking

### 📚 Resources & Deployment

- [Deployment](#-deployment) - Production deployment
- [Additional Resources](#-additional-resources) - Documentation links
- [Contributing](#-contributing) - Development guidelines

---

## 🏗️ Architecture & Tech Stack

### Core Technologies

- **Frontend**: Next.js 15.3.3 with App Router, React 19.1.0, TypeScript 5.8.3
- **Backend**: Next.js API Routes, GraphQL with Apollo Server 4.12.0
- **Database**: PostgreSQL with Prisma ORM 6.4.1
- **Authentication**: NextAuth.js 5.0.0-beta.28 with passkey support
- **Styling**: TailwindCSS 3.4.4 with custom design system
- **Monorepo**: Turborepo with pnpm workspace

### Key Features

- **Real Estate Management**: Property listings, estate management, and sales processes
- **Digital Signatures**: Signicat integration for document signing
- **Authentication**: Support for passkeys and traditional email/password
- **Document Management**: PDF generation, S3 storage, and document processing
- **Analytics**: PostHog integration for user analytics
- **Notifications**: Email (Mailchimp) and SMS capabilities
- **Third-party Integrations**: Vitec, AWS, Sentry, and more

### External Services

- **Signicat**: Digital signature and identity verification
- **Vitec**: Real estate data and integration
- **AWS**: S3 for file storage, Lambda for serverless functions
- **PostHog**: Analytics and user tracking
- **Sentry**: Error monitoring and performance tracking
- **Mailchimp**: Email marketing and transactional emails

## 📁 Project Structure

```
nordvik-megler-nextjs/
├── apps/
│   └── web/                          # Main Next.js application
│       ├── src/
│       │   ├── app/                  # Next.js App Router
│       │   ├── components/           # Reusable React components
│       │   ├── lib/                  # Utility functions and configurations
│       │   ├── server/               # Server-side utilities
│       │   └── external-services/    # External API integrations
│       ├── prisma/                   # Database schema and migrations
│       └── public/                   # Static assets
├── packages/
│   ├── nordvik-ui/                   # Shared UI component library
│   ├── nordvik-theme/                # Design tokens and theme configuration
│   ├── nordvik-utils/                # Shared utility functions
│   ├── signicat-express-sdk/         # Custom Signicat SDK
│   └── eslint-config/                # Shared ESLint configuration
└── deploy-to-production.ts           # Production deployment script
```

## 🚀 Getting Started

### Prerequisites

- **Node.js**: >= 22.x.x
- **pnpm**: 10.13.1 (package manager)
- **PostgreSQL**: Database server
- **Redis**: For caching and KV storage

### Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd nordvik-megler-nextjs
   ```

2. **Install dependencies**:

   ```bash
   pnpm install
   ```

3. **Set up environment variables**:
   Create `.env.local` in the `apps/web` directory:

   ```bash
   cp apps/web/.env.example apps/web/.env.local
   ```

4. **Configure database**:

   ```bash
   cd apps/web
   pnpm prisma:migrate
   pnpm prisma:generate
   ```

5. **Start development server**:

   ```bash
   pnpm dev
   ```

   The application will be available at [http://localhost:8040](http://localhost:8040).

## 🔧 Environment Variables

### Required Environment Variables

Create a `.env.local` file in `apps/web/` with the following variables:

#### Database & Cache

```env
PG_DATABASE_URL=postgresql://user:password@localhost:5432/nordvik_megler
REDIS_REST_API_URL=your_redis_url
REDIS_REST_API_TOKEN=your_redis_token
```

#### Authentication

```env
AUTH_SECRET=your_auth_secret_key
NEXTAUTH_URL=http://localhost:8040
```

#### External Services

```env
# Signicat (Document Signing)
SIGNICAT_CLIENT_ID=your_signicat_client_id
SIGNICAT_CLIENT_SECRET=your_signicat_client_secret
SIGNICAT_ACCOUNT_ID=your_signicat_account_id
SIGNICAT_API_URL=https://api.signicat.com
SIGNICAT_WEBHOOK_SECRET=your_webhook_secret

# Vitec (Real Estate Data)
VITEC_URL=your_vitec_url
VITEC_INSTALLATION_ID=your_installation_id
VITEC_USER=your_vitec_user
VITEC_PASSWORD=your_vitec_password

# AWS
AWS_REGION=eu-north-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Email & SMS
SMTP_HOST=your_smtp_host
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=your_sender_email
MAILCHIMP_API_KEY=your_mailchimp_key

# Analytics & Monitoring
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=your_posthog_host
SENTRY_AUTH_TOKEN=your_sentry_token
```

#### Development/Testing

```env
TEST_EMAIL=<EMAIL>
TEST_PHONE=+**********
ENABLE_REACT_QUERY_DEVTOOLS=true
DISABLE_CACHE=false
```

**Important**: When `TEST_EMAIL` and `TEST_PHONE` are set, they override recipient values in emails and SMS messages. This is crucial for local testing to prevent sending test communications to real customers.

### Public Environment Variables

```env
NEXT_PUBLIC_URL=http://localhost:8040
NEXT_PUBLIC_VERCEL_URL=your_vercel_url
NEXT_PUBLIC_FEATURE_FLAGS={}
NEXT_PUBLIC_NORDVIK_APP_URL=your_mobile_app_url
```

## 🛠️ Development Workflow

### Available Scripts

```bash
# Development
pnpm dev                    # Start development server
pnpm build                  # Build for production
pnpm start                  # Start production server

# Code Quality
pnpm lint                   # Run ESLint
pnpm format                 # Format code with Prettier
pnpm check                  # Run type checking, linting, and format checks

# Database
pnpm prisma:generate        # Generate Prisma client
pnpm prisma:migrate         # Run database migrations
pnpm prisma:dbpull          # Pull database schema

# Storybook
pnpm storybook              # Start Storybook server
pnpm storybook:build        # Build Storybook
pnpm storybook:snapshot     # Run Chromatic visual tests

# Code Generation
pnpm generate               # Generate GraphQL types and client
```

### Database Management

The project uses Prisma for database management:

1. **Make schema changes** in `apps/web/prisma/schema.prisma`
2. **Create migration**: `pnpm prisma:migrate`
3. **Generate client**: `pnpm prisma:generate`

### GraphQL Development

GraphQL schemas are located in `apps/web/src/server/` and types are auto-generated:

1. **Modify GraphQL schemas** in `.graphql` files
2. **Run code generation**: `pnpm generate`
3. **Generated types** will be available in `apps/web/src/api/generated-client.ts`

## 🧪 Testing & Quality Assurance

### Testing Stack

- **Storybook**: Component development and testing
- **Chromatic**: Visual regression testing
- **Playwright**: End-to-end testing
- **ESLint**: Code linting
- **Prettier**: Code formatting

### Running Tests

```bash
# Visual Testing
pnpm storybook:snapshot              # Run Chromatic visual tests
pnpm storybook:snapshot:accept       # Accept visual changes

# Code Quality
pnpm lint                            # Run ESLint
pnpm check:type                      # TypeScript type checking
pnpm check:format                    # Check code formatting
```

### Storybook Development

1. **Start Storybook**: `pnpm storybook`
2. **Access at**: http://localhost:8060 (web) or http://localhost:6006 (UI package)
3. **Create stories** in `*.stories.tsx` files
4. **Visual testing** is automated via Chromatic

## 🚀 Deployment

### Production Deployment

```bash
# Deploy to production
pnpm deploy-to-production
```

The application is configured for deployment on Vercel with:

- **Automatic deployments** from `dev` branch
- **Environment variables** configured in Vercel dashboard
- **Sentry** for error monitoring
- **Vercel Analytics** for performance monitoring

## 🔧 VS Code Configuration

### Required Extensions

- **ESLint**: Code linting
- **Prettier - Code formatter**: Code formatting (esbenp.prettier-vscode)
- **Tailwind CSS IntelliSense**: CSS class suggestions
- **TypeScript**: Language support

### Settings Configuration

Add to your VS Code `settings.json`:

```json
{
  "eslint.workingDirectories": [{ "mode": "auto" }],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "typescript.preferences.preferTypeOnlyAutoImports": true,

  // Prettier Configuration
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

### Prettier Configuration

This project uses a custom Prettier configuration with the following features:

#### Project Configuration

- **Import Sorting**: Uses `@trivago/prettier-plugin-sort-imports` for automatic import organization
- **Tailwind Integration**: `prettier-plugin-tailwindcss` for class sorting
- **Consistent Formatting**: Shared across the entire monorepo

#### Manual Formatting Commands

```bash
# Format all files
pnpm format

# Check formatting without fixing
pnpm check:format

# Format specific files
pnpm format src/components/Button.tsx
```

#### Prettier Configuration File

The project uses `prettier.config.js` in the root directory with:

- **Semi-colons**: Disabled
- **Single quotes**: Enabled
- **Trailing commas**: ES5 compatible
- **Tab width**: 2 spaces
- **Print width**: 80 characters

#### Troubleshooting Prettier

- **Conflicting formatters**: Disable other formatters (Beautify, etc.)
- **Not auto-formatting**: Check if Prettier is set as default formatter
- **Import sorting issues**: Ensure `@trivago/prettier-plugin-sort-imports` is working
- **Tailwind classes not sorting**: Verify `prettier-plugin-tailwindcss` is active

### Debugging Setup

The project includes VS Code debugging configuration:

1. **Open debugger panel** (⇧+⌘+D on Mac / Ctrl+Shift+D on Windows/Linux)
2. **Set breakpoints** in your code
3. **Select configuration**:
   - **Debug server-side**: For server-side debugging
   - **Debug client-side**: For client-side debugging in Chrome
4. **Start debugging** with the green arrow

## 📦 Package Management

### Workspace Structure

This is a monorepo using pnpm workspaces:

- **apps/web**: Main Next.js application
- **packages/nordvik-ui**: Shared React component library
- **packages/nordvik-theme**: Design tokens and theme configuration
- **packages/nordvik-utils**: Shared utility functions
- **packages/signicat-express-sdk**: Custom Signicat integration
- **packages/eslint-config**: Shared ESLint configuration

### Adding Dependencies

```bash
# Add to workspace root
pnpm add <package>

# Add to specific package
pnpm add <package> --filter web
pnpm add <package> --filter @nordvik/ui
```

### Dependency Management

Dependencies are managed with overrides and resolutions to ensure consistency:

- **TypeScript**: 5.8.3
- **React**: 19.1.0
- **Next.js**: 15.3.3
- **TailwindCSS**: 3.4.4

## 🔐 Authentication & Security

### Authentication Flow

1. **NextAuth.js** handles authentication
2. **Passkey support** for modern authentication
3. **Email/password** fallback
4. **Nordvik email domain** validation (@nordvikbolig.no)

### Security Features

- **CSP headers** configured
- **CORS** properly configured
- **Environment variables** secured
- **Sentry** for error monitoring
- **Input validation** with Zod

## 🎨 Design System

### UI Components

- **Radix UI**: Unstyled, accessible components
- **Custom design system**: `@nordvik/ui` package
- **TailwindCSS**: Utility-first CSS framework
- **Framer Motion**: Animation library
- **Lucide React**: Icon library

### Theme Configuration

- **Design tokens**: Defined in `packages/nordvik-theme/`
- **CSS custom properties**: For theme values
- **Dark/light mode**: Supported
- **Responsive design**: Mobile-first approach

## 📊 Analytics & Monitoring

### PostHog Integration

PostHog is our primary analytics and feature management platform:

#### Features

- **User Analytics**: Track user behavior, page views, and interactions
- **Feature Flags**: Control feature rollouts and A/B testing
- **Session Replay**: Debug user issues with session recordings
- **Funnels & Cohorts**: Analyze user journeys and retention
- **Custom Events**: Track specific business metrics

#### Configuration

```env
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_project_key
NEXT_PUBLIC_POSTHOG_HOST=https://eu.posthog.com
```

#### Usage in Code

```typescript
import { posthog } from '@/lib/analytics/posthog-client'

// Track custom events
posthog.capture('button_clicked', {
  button_name: 'submit_form',
  page: '/dashboard',
})

// Feature flags
const showNewFeature = posthog.isFeatureEnabled('new-feature-flag')
```

#### Key Events Tracked

- **User Authentication**: Login/logout events
- **Page Views**: Automatic page tracking
- **Form Submissions**: Estate creation, document signing
- **Feature Usage**: Component interactions and workflows

### Additional Analytics

- **Vercel Analytics**: Performance monitoring and Core Web Vitals
- **Custom tracking**: Page views and user interactions
- **Sentry Integration**: Error tracking with user context

### Error Monitoring

- **Sentry**: Error tracking and performance monitoring
- **Next.js**: Built-in error boundaries
- **Logging**: Structured logging for debugging
- **PostHog Integration**: User context in error reports

## 🔄 CI/CD Pipeline

### GitHub Actions

- **Code Quality**: ESLint, TypeScript, Prettier checks
- **Visual Testing**: Chromatic snapshots on PRs
- **Deployment**: Automatic deployment to Vercel

### Workflows

- **PR checks**: Run on pull requests to `dev`
- **Deployment**: Automatic on merge to `dev`
- **Visual testing**: Chromatic integration

## 🐛 Troubleshooting

### Common Issues

1. **Database connection errors**:

   - Check `PG_DATABASE_URL` in environment variables
   - Ensure PostgreSQL is running
   - Run `pnpm prisma:generate`

2. **Build failures**:

   - Clear `.next` cache: `rm -rf .next`
   - Reinstall dependencies: `pnpm install`
   - Check TypeScript errors: `pnpm check:type`

3. **Environment variables**:

   - Ensure `.env.local` is in `apps/web/`
   - Check for typos in variable names
   - Restart development server after changes

4. **Storybook issues**:

   - Clear Storybook cache: `pnpm storybook --no-manager-cache`
   - Check for conflicting CSS imports

5. **PostHog analytics not working**:

   - Verify `NEXT_PUBLIC_POSTHOG_KEY` and `NEXT_PUBLIC_POSTHOG_HOST` in environment variables
   - Check browser console for PostHog errors
   - Ensure PostHog is not blocked by ad blockers in development

6. **Prettier not working in VS Code**:
   - Install the official Prettier extension: `esbenp.prettier-vscode`
   - Set Prettier as default formatter in VS Code settings
   - Check that `prettier.config.js` is being detected in the root directory
   - Disable conflicting formatters (Beautify, etc.)
   - Restart VS Code after configuration changes

### Performance Tips

- Use `DISABLE_CACHE=true` for development debugging
- Enable React Query DevTools: `ENABLE_REACT_QUERY_DEVTOOLS=true`
- Monitor bundle size with Next.js bundle analyzer
- Use PostHog feature flags for gradual feature rollouts

## 📚 Additional Resources

### Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [Storybook Documentation](https://storybook.js.org/docs)
- [PostHog Documentation](https://posthog.com/docs)

### Internal Resources

- **Design System**: Available in Storybook
- **API Documentation**: GraphQL Playground in development
- **Component Library**: `@nordvik/ui` package documentation

## 🤝 Contributing

### Development Process

1. **Create feature branch** from `dev`
2. **Make changes** with appropriate tests
3. **Run quality checks**: `pnpm check`
4. **Create pull request** to `dev`
5. **Code review** and approval
6. **Merge** and deploy

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Custom configuration
- **Prettier**: Consistent formatting
- **Conventional commits**: Preferred
- **Component documentation**: Storybook stories required

### Pull Request Guidelines

- Include tests for new features
- Update documentation as needed
- Ensure visual tests pass
- Follow existing code patterns
- Add appropriate labels

---

**Need help?** Contact the development team or check the internal documentation for more detailed information.
